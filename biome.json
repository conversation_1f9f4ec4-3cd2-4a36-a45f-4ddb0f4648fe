{"files": {"includes": ["*.tsx", "*.ts", "*.jsx", "*.js", "*.json", "*.css"]}, "assist": {"actions": {"source": {"organizeImports": {"level": "on", "options": {"groups": [":NODE:", ":BLANK_LINE:", ["@my/lib", "@my/lib/**", "!@my/lib/special", "!@my/lib/special/**"], "@/**"]}}}}}, "formatter": {"enabled": true, "indentStyle": "space", "lineWidth": 120}, "linter": {"enabled": true, "rules": {"style": {"useLiteralEnumMembers": "error", "noCommaOperator": "error", "useNodejsImportProtocol": "error", "useAsConstAssertion": "error", "useNumericLiterals": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useConst": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "useExponentiationOperator": "error", "useTemplate": "error", "noParameterAssign": "error", "noNonNullAssertion": "error", "useDefaultParameterLast": "error", "noArguments": "error", "useImportType": "error", "useExportType": "error", "noUselessElse": "error", "useShorthandFunctionType": "error"}, "nursery": {"useSortedClasses": {"options": {}, "level": "error", "fix": "unsafe"}}}}}