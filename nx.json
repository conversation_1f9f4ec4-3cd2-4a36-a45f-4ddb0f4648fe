{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default"], "sharedGlobals": ["{workspaceRoot}/.github/workflows/ci.yml"]}, "nxCloudId": "67ec0ed6adaeb26f9591139f", "plugins": [{"plugin": "@nx/js/typescript", "options": {"typecheck": {"targetName": "typecheck"}, "build": {"targetName": "build", "configName": "tsconfig.lib.json", "buildDepsName": "build-deps", "watchDepsName": "watch-deps"}}}]}