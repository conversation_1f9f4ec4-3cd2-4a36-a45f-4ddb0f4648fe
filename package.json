{"name": "@imoblr/base", "version": "0.0.0", "license": "MIT", "packageManager": "pnpm@10.12.1", "scripts": {"dev:app": "NX_NO_CLOUD=true nx dev app", "dev:api": "NX_NO_CLOUD=true nx dev api", "dev:sites": "NX_NO_CLOUD=true nx dev customer-sites", "dev:marketing": "NX_NO_CLOUD=true nx dev marketing-website", "dev:proxy": "NX_NO_CLOUD=true nx dev proxy", "dev:stripe": "stripe listen --forward-to localhost:4242/webhook", "supabase:start": "NX_NO_CLOUD=true nx start supabase", "supabase:stop": "NX_NO_CLOUD=true nx stop supabase", "supabase:functions": "NX_NO_CLOUD=true nx functions:serve supabase", "build:app": "NX_NO_CLOUD=true nx build app", "build:api": "NX_NO_CLOUD=true nx build api", "build:sites": "NX_NO_CLOUD=true nx build customer-sites", "build:marketing": "NX_NO_CLOUD=true nx build marketing-website", "build:proxy": "NX_NO_CLOUD=true nx build proxy", "lint": "biome lint --unsafe"}, "private": true, "devDependencies": {"@biomejs/biome": "2.0.0-beta.2", "@nx/js": "20.7.0", "dotenv-cli": "^8.0.0", "nx": "20.7.0", "tslib": "^2.3.0", "typescript": "~5.7.2"}, "workspaces": ["packages/*"]}