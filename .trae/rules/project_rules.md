- This is an NX monorepo.
- The main app/dashboard will be in packages/app.
- The backend/data/api will be in packages/api.
- This project uses PNPM. You will only use PNPM to manage dependencies.
- This project WAS using Supabase/Postgres RPC functions for backend logic. This is no longer the case and this is being deprecated.
- You will create new endpoints and backend logic on packages/api EXCLUSIVELY. You will NOT use Supabase RPC functions. You will use hono + drizzle on cloudflare workers on packages/api instead.