export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const hostname = url.hostname;
    let targetHostname = 'imoblr-sites.pages.dev'; // Default target
    let targetPort = '';
    let websiteAddress = 'default'; // Default website address

    // Define prefixes and suffixes once
    const stagingPrefix = 'env-staging-';
    const imoblrDomainSuffix = '.imoblr.com.br';
    const localHostnames = ['localhost', '127.0.0.1'];

    // Create a new request object to modify headers
    const newRequest = new Request(request);
    let environment; // Determined by hostname

    // Check if this is a local development request
    if (localHostnames.includes(hostname)) {
      environment = "development";

      // For local development, check if the path already contains a website address
      const pathParts = url.pathname.split('/');

      if (pathParts.length > 1 && pathParts[1]) {
        // Use the first path segment as the website address
        websiteAddress = pathParts[1];

        // Store the original path to use for rewriting links
        newRequest.headers.set("x-original-path-prefix", `/${pathParts[1]}`);
      }

      // For local development, target the local Astro dev server
      targetHostname = hostname;
      targetPort = '4321'; // Default Astro dev server port
    }
    // Handle production and staging environments
    else if (hostname.substring(0, stagingPrefix.length) === stagingPrefix && hostname.endsWith(imoblrDomainSuffix)) {
      // Staging: env-staging-SUBDOMAIN.imoblr.com.br
      environment = "staging";
      const startIndex = stagingPrefix.length;
      const endIndex = hostname.length - imoblrDomainSuffix.length;
      if (endIndex > startIndex) { // Ensure there's something between prefix and suffix
        websiteAddress = hostname.substring(startIndex, endIndex); // Extract SUBDOMAIN
      } else {
        console.error(`Unexpected staging hostname format: ${hostname}`);
        environment = undefined; // Mark as indeterminate
      }
    } else if (hostname.endsWith(imoblrDomainSuffix)) {
      // Production Subdomain: SUBDOMAIN.imoblr.com.br
      environment = "production";
      websiteAddress = hostname.split('.')[0]; // Extract SUBDOMAIN
    } else {
      // Custom Domain
      environment = "production";
      websiteAddress = hostname; // Use full hostname
    }

    // Set the environment header if determined
    if (environment) {
      newRequest.headers.set("x-supabase-environment", environment);
    } else {
      console.error(`Could not determine environment for hostname: ${hostname}`);
    }

    // Set the website address in the header for backward compatibility
    newRequest.headers.set("x-website-address", websiteAddress);

    // Set appropriate headers for the customer-sites middleware
    if (hostname.endsWith(imoblrDomainSuffix)) {
      // This is a subdomain (either staging or production)
      const subdomain = hostname.endsWith(imoblrDomainSuffix) ? 
        (hostname.substring(0, stagingPrefix.length) === stagingPrefix ? 
          hostname.substring(stagingPrefix.length, hostname.length - imoblrDomainSuffix.length) : 
          hostname.split('.')[0]) : 
        websiteAddress;
      newRequest.headers.set("x-customer-subdomain", subdomain);
    } else if (!localHostnames.includes(hostname)) {
      // This is a custom domain
      newRequest.headers.set("x-customer-domain", hostname);
    }

    // Route to the determined target application
    url.hostname = targetHostname;
    if (targetPort) {
      url.port = targetPort;
    }

    // Modify the URL to include the website address in the path
    // Don't modify API requests or static assets (including font files)
    if (!url.pathname.startsWith('/api/') && !url.pathname.match(/\.(svg|png|jpg|jpeg|gif|ico|css|js|woff|woff2|ttf|eot)$/i)) {
      // Check if the path already starts with the website address
      if (!url.pathname.startsWith(`/${websiteAddress}/`) && url.pathname !== `/${websiteAddress}`) {
        // If the path is just '/', replace it with '/websiteAddress'
        if (url.pathname === '/') {
          url.pathname = `/${websiteAddress}`;
        }
        // Otherwise, prepend the website address to the path
        else {
          // Check if the path already has the website address
          const pathParts = url.pathname.split('/');
          if (pathParts.length > 1 && pathParts[1] !== websiteAddress) {
            url.pathname = `/${websiteAddress}${url.pathname}`;
          }
        }
      }
    }

    return fetch(url.toString(), newRequest);
  }
}