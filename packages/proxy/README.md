# Customer Sites Proxy

This is a Cloudflare Worker that serves as a proxy for the customer sites application. It handles routing requests to the appropriate customer site based on the domain or subdomain.

## NX Monorepo Integration

This package is part of the Imoblr Platform monorepo and is managed using NX. Dependencies are installed at the root level of the monorepo.

## Development

To run the proxy locally:

```bash
# From the root of the monorepo
bun run dev:proxy

# Or using nx directly
nx dev proxy
```

## Building

To build the proxy:

```bash
# From the root of the monorepo
bun run build:proxy

# Or using nx directly
nx build proxy
```

## Deployment

To deploy the proxy to Cloudflare:

```bash
# From the root of the monorepo
nx deploy proxy
```

## Configuration

The proxy is configured using the `wrangler.toml` file. See the [Cloudflare Workers documentation](https://developers.cloudflare.com/workers/) for more information.
