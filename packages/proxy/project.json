{"name": "proxy", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/proxy", "projectType": "application", "targets": {"build": {"executor": "nx:run-commands", "options": {"cwd": "packages/proxy", "command": "wrangler deploy --dry-run --outdir dist"}}, "dev": {"executor": "nx:run-commands", "options": {"cwd": "packages/proxy", "command": "wrangler dev"}}, "deploy": {"executor": "nx:run-commands", "options": {"cwd": "packages/proxy", "command": "wrangler deploy"}}}, "tags": []}