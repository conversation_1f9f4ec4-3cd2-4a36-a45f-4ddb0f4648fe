// custom.d.ts

declare module "*.svg" {
  import type React from "react";
  import type { SvgProps as OgSvgProps } from "react-native-svg";

  interface SvgProps extends OgSvgProps<SVGSVGElement> {
    className?: string;
  }

  const content: React.FC<SvgProps>;
  export default content;
}

declare module "react-native-svg" {
  import type { CircleProps as OgCircleProps } from "react-native-svg";
  import type { PathProps as OgPathProps } from "react-native-svg";
  import type { SvgProps as OgSvgProps } from "react-native-svg";

  export interface SvgProps extends OgSvgProps<SVGSVGElement> {
    className?: string;
  }

  export interface CircleProps extends OgCircleProps {
    className?: string;
  }

  export interface PathProps extends OgPathProps {
    className?: string;
  }
}
