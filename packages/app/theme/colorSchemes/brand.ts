import { vars } from "nativewind";
import { brand as brandColor, gray, success } from "../colors";
import { brandQuaternary } from "../colors/brandQuaternary";
import { brandSecondary } from "../colors/brandSecondary";
import { brandTertiary } from "../colors/brandTertiary";
import { error } from "../colors/error";
import { warning } from "../colors/warning";

export const brand = {
  light: vars({
    // Primary theme color
    "--color-primary": brandColor["500"],
    "--color-primary-50": brandColor["50"],
    "--color-primary-100": brandColor["100"],
    "--color-primary-200": brandColor["200"],
    "--color-primary-300": brandColor["300"],
    "--color-primary-400": brandColor["400"],
    "--color-primary-500": brandColor["500"],
    "--color-primary-600": brandColor["600"],
    "--color-primary-700": brandColor["700"],
    "--color-primary-800": brandColor["800"],
    "--color-primary-900": brandColor["900"],
    "--color-primary-950": brandColor["950"],
    // Secondary theme color
    "--color-secondary": brandSecondary["500"],
    "--color-secondary-50": brandSecondary["50"],
    "--color-secondary-100": brandSecondary["100"],
    "--color-secondary-200": brandSecondary["200"],
    "--color-secondary-300": brandSecondary["300"],
    "--color-secondary-400": brandSecondary["400"],
    "--color-secondary-500": brandSecondary["500"],
    "--color-secondary-600": brandSecondary["600"],
    "--color-secondary-700": brandSecondary["700"],
    "--color-secondary-800": brandSecondary["800"],
    "--color-secondary-900": brandSecondary["900"],
    "--color-secondary-950": brandSecondary["950"],
    // Tertiary theme color
    "--color-tertiary": brandTertiary["500"],
    "--color-tertiary-50": brandTertiary["50"],
    "--color-tertiary-100": brandTertiary["100"],
    "--color-tertiary-200": brandTertiary["200"],
    "--color-tertiary-300": brandTertiary["300"],
    "--color-tertiary-400": brandTertiary["400"],
    "--color-tertiary-500": brandTertiary["500"],
    "--color-tertiary-600": brandTertiary["600"],
    "--color-tertiary-700": brandTertiary["700"],
    "--color-tertiary-800": brandTertiary["800"],
    "--color-tertiary-900": brandTertiary["900"],
    "--color-tertiary-950": brandTertiary["950"],
    // Quaternary theme color
    "--color-quaternary": brandQuaternary["500"],
    "--color-quaternary-50": brandQuaternary["50"],
    "--color-quaternary-100": brandQuaternary["100"],
    "--color-quaternary-200": brandQuaternary["200"],
    "--color-quaternary-300": brandQuaternary["300"],
    "--color-quaternary-400": brandQuaternary["400"],
    "--color-quaternary-500": brandQuaternary["500"],
    "--color-quaternary-600": brandQuaternary["600"],
    "--color-quaternary-700": brandQuaternary["700"],
    "--color-quaternary-800": brandQuaternary["800"],
    "--color-quaternary-900": brandQuaternary["900"],
    "--color-quaternary-950": brandQuaternary["950"],
    // Gray theme color
    "--color-gray": gray["600"],
    "--color-gray-50": gray["50"],
    "--color-gray-100": gray["100"],
    "--color-gray-200": gray["200"],
    "--color-gray-300": gray["300"],
    "--color-gray-400": gray["400"],
    "--color-gray-500": gray["500"],
    "--color-gray-600": gray["600"],
    "--color-gray-700": gray["700"],
    "--color-gray-800": gray["800"],
    "--color-gray-900": gray["900"],
    "--color-gray-950": gray["950"],
    // Success theme color
    "--color-success": success["500"],
    "--color-success-50": success["50"],
    "--color-success-100": success["100"],
    "--color-success-200": success["200"],
    "--color-success-300": success["300"],
    "--color-success-400": success["400"],
    "--color-success-500": success["500"],
    "--color-success-600": success["600"],
    "--color-success-700": success["700"],
    "--color-success-800": success["800"],
    "--color-success-900": success["900"],
    "--color-success-950": success["950"],
    // Error theme color
    "--color-error": error["500"],
    "--color-error-50": error["50"],
    "--color-error-100": error["100"],
    "--color-error-200": error["200"],
    "--color-error-300": error["300"],
    "--color-error-400": error["400"],
    "--color-error-500": error["500"],
    "--color-error-600": error["600"],
    "--color-error-700": error["700"],
    "--color-error-800": error["800"],
    "--color-error-900": error["900"],
    "--color-error-950": error["950"],
    // Warning theme color
    "--color-warning": warning["500"],
    "--color-warning-50": warning["50"],
    "--color-warning-100": warning["100"],
    "--color-warning-200": warning["200"],
    "--color-warning-300": warning["300"],
    "--color-warning-400": warning["400"],
    "--color-warning-500": warning["500"],
    "--color-warning-600": warning["600"],
    "--color-warning-700": warning["700"],
    "--color-warning-800": warning["800"],
    "--color-warning-900": warning["900"],
    "--color-warning-950": warning["950"],
    // Theme color variables
    "--color-background": "255,255,255",
    "--color-background-inverse": gray["950"],
    "--color-background-dark": gray["50"],
    "--color-background-darker": gray["100"],
    "--color-background-darkest": gray["200"],
    // - start - Border colors
    "--color-border-darkest": gray["600"],
    "--color-border-darker": gray["500"],
    "--color-border-dark": gray["400"],
    "--color-border": gray["300"],
    "--color-border-light": gray["200"],
    "--color-border-lighter": gray["100"],
    "--color-border-lightest": gray["50"],
    // - end - Border colors
    // - start - Ring colors
    "--color-ring": brandColor["200"],
    // - end - Ring colors
    // -----------------------------------------------------------------------------
    // TYPOGRAPHY COLORS
    // -----------------------------------------------------------------------------
    "--color-text": `rgb(${gray["950"]})`,
    "--color-text_hover": `rgb(${gray["700"]})`,
    "--color-text-inverse": "rgb(255,255,255)",
    "--color-text-on-brand": "white",
    "--color-text-secondary": `rgb(${gray["600"]})`,
    "--color-text-secondary_hover": `rgb(${gray["500"]})`,
    "--color-text-secondary_on-brand": brandColor["200"],
    "--color-text-tertiary": `rgb(${gray["500"]})`,
    "--color-text-tertiary_hover": `rgb(${gray["400"]})`,
    "--color-text-tertiary_on-brand": brandColor["200"],
    "--color-text-quaternary": `rgb(${gray["400"]})`,
    "--color-text-quaternary_on-brand": brandColor["300"],
    "--color-text-disabled": `rgb(${gray["300"]})`,
    "--color-text-placeholder": `rgb(${gray["500"]})`,
    "--color-text-placeholder_subtle": `rgb(${gray["300"]})`,
    // -----------------------------------------------------------------------------
    //
    // UTIL COLORS
    //
    // -----------------------------------------------------------------------------
    // Shadow colors
    "--color-shadow-lightest": `rgba(${brandColor["950"]}, 0.03)`,
    "--color-shadow-lighter": `rgba(${brandColor["950"]}, 0.035)`,
    "--color-shadow-light": `rgba(${brandColor["950"]}, 0.04)`,
    "--color-shadow": `rgba(${brandColor["950"]}, 0.05)`,
    "--color-shadow-dark": `rgba(${brandColor["950"]}, 0.06)`,
    "--color-shadow-darker": `rgba(${brandColor["950"]}, 0.08)`,
    "--color-shadow-darkest": `rgba(${brandColor["950"]}, 0.9)`,
  }),
  dark: vars({
    "--color-input": "green",
    "--color-primary": "green",
    "--color-secondary": "red",
  }),
};
