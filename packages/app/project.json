{"name": "app", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/app", "projectType": "application", "targets": {"build": {"executor": "nx:run-commands", "options": {"cwd": "packages/app", "command": "expo export -p web"}}, "build:staging": {"executor": "nx:run-commands", "options": {"cwd": "packages/app", "command": "echo 'Building app package for staging'"}}, "dev": {"executor": "nx:run-commands", "options": {"cwd": "packages/app", "command": "NODE_ENV=development npx dotenv-cli -e .env.local -- expo start"}, "configurations": {"dry-run": {"command": "echo 'Would run: NODE_ENV=development npx dotenv-cli -e .env.local -- expo start'"}}}, "dev:staging": {"executor": "nx:run-commands", "options": {"cwd": "packages/app", "command": "NODE_ENV=development npx dotenv-cli -e .env.staging -- expo start"}, "configurations": {"dry-run": {"command": "echo 'Would run: NODE_ENV=development npx dotenv-cli -e .env.staging -- expo start'"}}}, "android": {"executor": "nx:run-commands", "options": {"cwd": "packages/app", "command": "expo run:android"}, "configurations": {"dry-run": {"command": "echo 'Would run: expo run:android'"}}}, "ios": {"executor": "nx:run-commands", "options": {"cwd": "packages/app", "command": "expo run:ios"}, "configurations": {"dry-run": {"command": "echo 'Would run: expo run:ios'"}}}, "web": {"executor": "nx:run-commands", "options": {"cwd": "packages/app", "command": "expo start --web"}, "configurations": {"dry-run": {"command": "echo 'Would run: expo start --web'"}}}, "lint": {"executor": "nx:run-commands", "options": {"cwd": "packages/app", "command": "biome check ."}, "configurations": {"dry-run": {"command": "echo 'Would run: biome check .'"}}}, "format": {"executor": "nx:run-commands", "options": {"cwd": "packages/app", "command": "biome format . --write"}, "configurations": {"dry-run": {"command": "echo 'Would run: biome format . --write'"}}}, "prebuild": {"executor": "nx:run-commands", "options": {"cwd": "packages/app", "command": "expo prebuild"}, "configurations": {"dry-run": {"command": "echo 'Would run: expo prebuild'"}}}}, "tags": []}