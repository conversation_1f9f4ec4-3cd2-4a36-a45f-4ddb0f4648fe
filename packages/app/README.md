# Imoblr App

This is the mobile and web application for Imoblr, built with Expo and React Native.

## NX Monorepo Integration

This package is part of the Imoblr Platform monorepo and is managed using NX. Dependencies are installed at the root level of the monorepo.

### Installation

To install dependencies for the monorepo, follow these steps:

1. Install dependencies at the root level:

```bash
cd /path/to/Platform
bun install
```

2. Install app-specific dependencies:

```bash
cd packages/app
bun install
```

This two-step process ensures that both the monorepo-level dependencies and the app-specific dependencies are properly installed.

This package is part of the Imoblr Platform monorepo and is managed using NX. To run commands, use the NX CLI:

```bash
# Start the app in development mode with local environment
nx dev app

# Start the app in development mode with staging environment
nx dev:staging app

# Build the web app for production
nx build app

# Build the web app for staging
nx build:staging app

# Run on Android
nx android app

# Run on iOS
nx ios app

# Start web version
nx web app

# Lint the code
nx lint app

# Format the code
nx format app

# Run prebuild
nx prebuild app
```

## Dependencies

The app uses Expo, React Native, and a variety of UI libraries. See `package.json` for the full list of dependencies.

## Environment Variables

The app uses different environment files:
- `.env.local` - Local development
- `.env.staging` - Staging environment
- `.env.example` - Example environment variables

## Project Structure

- `app/` - Expo Router app directory
- `assets/` - Static assets
- `components/` - UI components
- `constants/` - Constants and configuration
- `context/` - React context providers
- `hooks/` - Custom React hooks
- `supabase/` - Supabase related code
- `theme/` - Styling themes
- `types/` - TypeScript type definitions
- `utils/` - Utility functions
- `workers/` - Cloudflare Workers code
