import type { AMENITY_VALUES } from "../constants/amenities";
import type { PROPERTY_AMENITY_VALUES } from "../constants/amenities";
import type { BUILDING_AMENITY_VALUES } from "../constants/amenities";

export type PropertyAmenity = (typeof PROPERTY_AMENITY_VALUES)[number];
export type BuildingAmenity = (typeof BUILDING_AMENITY_VALUES)[number];
export type Amenity = (typeof AMENITY_VALUES)[number];
