export type MapboxGeocoderItem = {
  type: string
  id: string
  geometry: {
    type: string
    coordinates: number[]
  }
  properties: {
    mapbox_id: string
    feature_type: string
    full_address: string
    name: string
    name_preferred: string
    coordinates: {
      longitude: number
      latitude: number
      accuracy: string
      routable_points: {
        name: string
        latitude: number
        longitude: number
      }[]
    }
    place_formatted: string
    match_code: {
      address_number: string
      street: string
      postcode: string
      place: string
      region: string
      locality: string
      country: string
      confidence: string
    }
    context: {
      address: {
        mapbox_id: string
        address_number: string
        street_name: string
        name: string
      }
      street: {
        mapbox_id: string
        name: string
      }
      neighborhood: {
        mapbox_id: string
        name: string
      }
      postcode: {
        mapbox_id: string
        name: string
      }
      locality: {
        mapbox_id: string
        name: string
        wikidata_id: string
      }
      place: {
        mapbox_id: string
        name: string
        wikidata_id: string
      }
      region: {
        mapbox_id: string
        name: string
        wikidata_id: string
        region_code: string
        region_code_full: string
      }
      country: {
        mapbox_id: string
        name: string
        wikidata_id: string
        country_code: string
        country_code_alpha_3: string
      }
    }
  }
}

export type MapboxGeocoderResponse = {
  type: string
  features: MapboxGeocoderItem[]
  attribution: string
}