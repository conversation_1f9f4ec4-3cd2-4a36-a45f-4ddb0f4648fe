import type { PROPERTY_TYPE_VALUES } from "../constants/propertyTypes";
import type { Address } from "./addresses";
import type { BuildingAmenity, PropertyAmenity } from "./amenities";

export type PropertyType = (typeof PROPERTY_TYPE_VALUES)[number];

export type PropertyPurpose = "residential" | "commercial" | "mixed";

export interface PropertyAmenities {
  id: string;
  property_id: string;
  barbecue_grill: boolean;
  gourmet_space: boolean;
  garden: boolean;
  pool: boolean;
  backyard: boolean;
  water_heating: boolean;
  heating: boolean;
  air_conditioning: boolean;
  internet: boolean;
  garage: boolean;
  fireplace: boolean;
  laundry: boolean;
  sauna: boolean;
  spa: boolean;
  security_system: boolean;
}

export interface BuildingAmenities {
  id: string;
  property_id: string;
  shared_barbecue_grill: boolean;
  shared_gourmet_space: boolean;
  bicycle_storage: boolean;
  intercom: boolean;
  gym: boolean;
  green_area: boolean;
  playground: boolean;
  shared_pool: boolean;
  tennis_court: boolean;
  sports_area: boolean;
  party_room: boolean;
  game_room: boolean;
  storage: boolean;
  shared_laundry: boolean;
  elevator: boolean;
  shared_garage: boolean;
  shared_water_heating: boolean;
  power_generator: boolean;
  reception: boolean;
  shared_sauna: boolean;
  shared_spa: boolean;
  shared_security_system: boolean;
  gated_community: boolean;
  private_security: boolean;
}

export interface Property {
  id: string;
  team_account_id: string;
  purpose: PropertyPurpose;
  type: PropertyType;
  floors_count?: number;
  floor?: number;
  rooms_count?: number;
  bathrooms_count?: number;
  parking_spots_count?: number;
  suites_count?: number;
  pets_allowed: boolean;
  available_for_rent: boolean;
  available_for_sale: boolean;
  available_for_bnb: boolean;
  furnished: boolean;
  address_id: string;
  address_complement?: string;
  address?: Address;
  created_at: string;
  updated_at: string;
  title: string;
  description: string;
  property_amenities_id?: string;
  building_amenities_id?: string;
  property_amenities?: PropertyAmenities;
  building_amenities?: BuildingAmenities;
  prices?: {
    id: string;
    property_id: string;
    sale_price?: number;
    rent_price?: number;
    bnb_price?: number;
    condominium_monthly_tax?: number;
    iptu_monthly_tax?: number;
    insurance_monthly_tax?: number;
    other_monthly_tax?: number;
  };
}
