export interface GooglePlace {
  id: string;
  formattedAddress: string;
  displayName: {
    text: string;
    languageCode: string;
  };
}

export interface GooglePlacesSearchResponse {
  places: GooglePlace[];
}

export interface AddressAutocompletePrediction {
  properties: {
    full_address: string;
    place_id: string;
    context: {
      address: {
        name: string;
      };
      neighborhood?: {
        name: string;
      };
      locality?: {
        name: string;
      };
      region?: {
        region_code: string;
      };
      place?: {
        name: string;
      };
      postcode?: {
        name: string;
      };
    };
  };
}
