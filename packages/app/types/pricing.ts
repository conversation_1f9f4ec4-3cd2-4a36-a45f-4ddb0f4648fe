export interface StripeProductPriceTier {
  up_to: number | null;
  unit_amount: number | null;
}

export interface StripeProductPrice {
  id: string;
  currency: string;
  unit_amount: number | null;
  recurring: {
    interval: string;
  };
  type: string;
  metadata: Record<string, string>;
  billing_scheme: "per_unit" | "tiered";
  lookup_key?: string;
  tiers?: StripeProductPriceTier[];
}

export interface StripeProductFeature {
  name: string;
}

export interface StripeProduct {
  id: string;
  name: string;
  description: string;
  features: Array<StripeProductFeature>;
  metadata: Record<string, string>;
  prices: StripeProductPrice[];
}
