import type { Database } from "../basejump_shared";

export type GetBillingStatusResponse = {
  billing_customer_id: string;
  subscription_id: string;
  status: Database["basejump"]["Tables"]["billing_subscriptions"]["Row"]["status"];
  trial_end?: number; // Unix timestamp
  current_period_end?: number; // Unix timestamp
  plan: {
    id: string;
    name: string;
    interval: string;
    trial_period_days?: number;
  };
  cancel_at_period_end: boolean;
  billing_email?: string;
  account_role: Database["basejump"]["Tables"]["account_user"]["Row"]["account_role"];
  is_primary_owner: boolean;
  billing_enabled: boolean;
  subscription_active: boolean;
};
