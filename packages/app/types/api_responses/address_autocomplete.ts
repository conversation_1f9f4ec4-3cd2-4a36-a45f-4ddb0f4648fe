/* ================================================================================================
//
//  This is the response from the Addresses cloudflare worker 
//
// ================================================================================================ */

// POST @ /address-autocomplete

export interface AddressAutocompleteResponse {
  suggestions: AddressAutocompletePrediction[];
}

export interface AddressAutocompletePrediction {
  placePrediction: {
    place: string;
    placeId: string;
    text: {
      text: string;
      matches: Array<{ startOffset?: number; endOffset: number }>;
    };
    structuredFormat: {
      mainText: {
        text: string;
        matches: Array<{ startOffset?: number; endOffset: number }>;
      };
      secondaryText: {
        text: string;
      };
    };
    types: string[];
  };
}
