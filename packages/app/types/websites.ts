export type WebsiteTheme = {
  id: string;
  primary_color: string;
  secondary_color: string;
  font_family?: string;
  dark_mode_settings?: Record<string, unknown>;
  created_at: string;
  updated_at: string;
};

export type DomainVerificationStatus = "pending" | "verified" | "active" | "failed";
export type DomainSslStatus = "pending" | "active" | "expired";

export type CustomDomain = {
  id: string;
  team_account_id: string;
  website_id: string;
  domain_name: string;
  verification_status: DomainVerificationStatus;
  ssl_status: DomainSslStatus;
  ssl_expiration_date?: string;
  created_at: string;
  updated_at: string;
  is_published: boolean;
  cloudflare_hostname_id: string;
};

export type Website = {
  id: string;
  team_account_id: string;
  title: string;
  subdomain: string;
  description?: string;
  slug?: string;
  published: boolean;
  status: "draft" | "published" | "archived";
  theme_id?: string;
  created_at: string;
  updated_at: string;
  theme?: WebsiteTheme;
  custom_domains?: CustomDomain[];
  custom_domain?: string; // For backward compatibility
  logo_image_url?: string;
  hero_image_url?: string;
  institutional_image_url?: string;
};
