export interface City {
  id: number;
  name: string;
  state: string;
}

export interface Neighborhood {
  id: number;
  city_id: number;
  name: string;
}

export interface Street {
  id: number;
  city_id: number;
  neighborhood_id: number;
  name: string;
}

export interface Address {
  id: string; // Changed from number to string (UUID)
  city_id: number;
  city: City;
  street_id: number;
  street: Street;
  neighborhood_id: number;
  neighborhood: Neighborhood;
  street_number: string;
  coordinates?: string;
  postcode: string;
  state: string;
  country: string;
}
