import { useQuery } from "@tanstack/react-query";
import { useSupabase } from "@/hooks/useSupabase";
// Define the LookupInvitationResponse type
export interface LookupInvitationResponse {
  active: boolean;
  account_name: string;
  team_id?: string;
  account_role?: string;
}

export const INVITATION_LOOKUP_KEY = "invitation_lookup";

export function useLookupInvitation(token: string | undefined | null) {
  const supabaseClient = useSupabase();

  return useQuery<LookupInvitationResponse, Error>({
    queryKey: [INVITATION_LOOKUP_KEY, token],
    queryFn: async () => {
      if (!supabaseClient) {
        throw new Error("Supabase client not initialized");
      }
      
      if (!token) {
        throw new Error("No invitation token provided");
      }

      try {
        // Use the RPC function that's already set up with correct parameter name
        const { data, error } = await supabaseClient.rpc("lookup_invitation", {
          lookup_invitation_token: token
        });
        
        if (error) {
          console.error("Error looking up invitation:", error);
          throw new Error(error.message || "Failed to lookup invitation");
        }
        
        // If no data is returned, the invitation doesn't exist or is expired
        if (!data) {
          return { active: false, account_name: '' };
        }
        
        // The RPC function should return the account_name and active status
        return { 
          active: true, 
          account_name: data.account_name || 'Time',
          team_id: data.team_id,
          account_role: data.account_role
        };
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        console.error("Error in useLookupInvitation:", errorMessage);
        throw new Error(errorMessage || "Failed to lookup invitation");
      }
    },
    enabled: !!supabaseClient && !!token,
  });
}
