import type { GetBillingStatusResponse } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { useAxios } from "./useAxios";
import { useCurrentTeamId } from "./useCurrentTeamId";

export function useBillingStatus(teamId?: string) {
  const [currentTeamId] = useCurrentTeamId();
  const { axios } = useAxios(); // Use the authenticated axios instance

  const accountId = teamId || currentTeamId;

  return useQuery<GetBillingStatusResponse, Error>({
    queryKey: ["billingStatus", accountId],
    queryFn: async () => {
      try {
        const response = await axios.get(`/billing/status/${accountId}`);
        return response.data;
      } catch (error) {
        console.error("Error fetching billing status:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
        throw new Error(errorMessage || "Failed to load billing status. Please try again later.");
      }
    },
    enabled: !!accountId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}
