import { useSupabaseSession } from '@/context/supabase-session';
import axiosInstance from '@/utils/axios';
import type { AxiosInstance } from 'axios';
import { useEffect, useMemo } from 'react';

/**
 * Hook that provides an axios instance with authentication headers automatically added
 * @returns An object containing the configured axios instance
 */
export function useAxios(): { axios: AxiosInstance } {
  const { session } = useSupabaseSession();

  // Configure axios instance with auth token when session changes
  useEffect(() => {
    // Set up request interceptor
    const interceptorId = axiosInstance.interceptors.request.use(
      (config) => {
        // If we have a session, add the token to the Authorization header
        if (session?.access_token) {
          config.headers.Authorization = `Bearer ${session.access_token}`;
          console.log('Added auth token to request:', config.url);
        } else {
          console.warn('No auth token available for request:', config.url);
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Clean up interceptor when component unmounts or session changes
    return () => {
      axiosInstance.interceptors.request.eject(interceptorId);
    };
  }, [session]);

  // Return memoized axios instance
  return useMemo(() => ({ axios: axiosInstance }), []);
}
