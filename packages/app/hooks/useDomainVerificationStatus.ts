import type { CustomDomain } from "@/types/websites";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useAxios } from "./useAxios";

interface DomainVerificationResponse {
  success: boolean;
  domain: CustomDomain;
  cloudflare_data: any;
  verification_errors: string[];
}

interface UseDomainVerificationStatusOptions {
  domainId: string;
  enabled?: boolean;
  refetchInterval?: number | false;
}

export const useDomainVerificationStatus = (options: UseDomainVerificationStatusOptions) => {
  const { axios } = useAxios();
  const queryClient = useQueryClient();
  const { domainId, enabled = true, refetchInterval = false } = options;

  return useQuery<DomainVerificationResponse>({
    queryKey: ["domain-verification-status", domainId],
    queryFn: async () => {
      if (!domainId) {
        throw new Error("Domain ID is required");
      }

      try {
        const response = await axios.get(`/domains/${domainId}/verification-status`);

        // If the verification status has changed, invalidate the custom-domains query
        // to ensure the domains list is updated
        if (response.data.success) {
          const customDomainsQueries = queryClient.getQueriesData({ queryKey: ["custom-domains"] });
          if (customDomainsQueries.length > 0) {
            customDomainsQueries.forEach(([queryKey]) => {
              queryClient.invalidateQueries({ queryKey: queryKey as string[] });
            });
          }
        }

        return response.data;
      } catch (error) {
        console.error("Error fetching domain verification status:", error);
        throw error;
      }
    },
    enabled: !!domainId && enabled,
    refetchInterval,
  });
};
