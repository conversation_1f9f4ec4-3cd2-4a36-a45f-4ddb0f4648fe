import { useQuery, type UseQueryOptions } from "@tanstack/react-query";
import { supabaseClient } from "@/utils/supabase";
import type { GetProfileResponse } from "@/types";

export const useCurrentUserProfile = (options?: Omit<UseQueryOptions<GetProfileResponse>, "queryKey" | "queryFn">) => {
  return useQuery<GetProfileResponse>({
    queryKey: ["current-user"],
    queryFn: async () => {
      const { data, error } = await supabaseClient.rpc("get_personal_account");

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    ...options,
  });
};
