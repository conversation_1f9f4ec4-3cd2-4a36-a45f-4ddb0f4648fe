import { useRouter } from "expo-router";
import { useCurrentTeamId } from "./useCurrentTeamId";

export const useSwitchTeamAccount = () => {
  const router = useRouter();
  const [currentTeamId, setCurrentTeamId] = useCurrentTeamId();

  const switchTeamAccount = (newTeamId: string | null, callback?: () => void) => {
    console.log(`Mudando de conta - ${currentTeamId} -> para -> ${newTeamId}`);
    setCurrentTeamId(newTeamId === null ? undefined : newTeamId);
    if (callback) {
      callback();
    } else {
      router.replace("/");
    }
  };

  return switchTeamAccount;
};
