import { useAxios } from "@/hooks/useAxios";
import { useQuery } from "@tanstack/react-query";
import { createTeamInvitesApi, type TeamInvite } from "@/utils/teamInvitesApi";

export const TEAM_INVITATIONS_QUERY_KEY = "team_invitations";

// Use the TeamInvite type from the API utils
type TeamInvitation = TeamInvite;

export function useTeamInvitations(teamId: string | undefined) {
  const { axios } = useAxios();
  const teamInvitesApi = createTeamInvitesApi(axios);

  return useQuery<TeamInvitation[], Error>({
    // Pass single options object
    queryKey: [TEAM_INVITATIONS_QUERY_KEY, teamId], // Query key includes teamId
    queryFn: async () => {
      if (!teamId) {
        // Return empty array or handle as needed if teamId is not available yet
        // Relies on the `enabled` option below
        return [];
      }

      const response = await teamInvitesApi.listTeamInvites(teamId);

      if (!response.success) {
        throw new Error("Failed to fetch team invitations");
      }

      // Return the invitations array
      return response.invitations;
    },
    // Options are part of the same object
    // Only run the query if teamId is available
    enabled: !!teamId,
    // Optional: Configure staleTime, cacheTime, etc.
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
