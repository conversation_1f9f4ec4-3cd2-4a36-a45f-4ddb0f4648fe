import { useCallback, useEffect, useState } from "react";
import { supabaseClient } from "@/utils/supabase";
import type { Session } from "@supabase/supabase-js";

export function useSupabaseAuth() {
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabaseClient.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabaseClient.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });

    return () => subscription.unsubscribe();
  }, []);

  // Send magic link via email
  const sendMagicLink = useCallback(async (email: string) => {
    const { data, error } = await supabaseClient.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: typeof window !== 'undefined' ? window.location.origin : undefined,
      },
    });
    
    if (error) throw error;
    return data;
  }, []);

  // Verify OTP code sent via email
  const verifyOtp = useCallback(async (email: string, token: string) => {
    const { data, error } = await supabaseClient.auth.verifyOtp({
      email,
      token,
      type: 'email',
    });
    
    if (error) throw error;
    return data;
  }, []);

  // Legacy password methods (keeping for backwards compatibility)
  const signUp = useCallback(async (credentials: { email: string; password: string }) => {
    const { data, error } = await supabaseClient.auth.signUp(credentials);
    if (error) throw error;
    return data;
  }, []);

  const signIn = useCallback(async (credentials: { email: string; password: string }) => {
    const { data, error } = await supabaseClient.auth.signInWithPassword(credentials);
    if (error) throw error;
    return data;
  }, []);

  const signOut = useCallback(async () => {
    const { error } = await supabaseClient.auth.signOut();
    if (error) throw error;
  }, []);

  return {
    session,
    loading,
    sendMagicLink,
    verifyOtp,
    signUp,
    signIn,
    signOut,
  };
}
