import { useSupabase } from "@/hooks/useSupabase";
import { useQuery } from "@tanstack/react-query";

export const TEAM_PROFILE_QUERY_KEY = "team_profile";

export interface TeamProfile {
  id: string;
  team_account_id: string;
  name: string | null;
  address: string | null;
  phone_number: string | null;
  whatsapp_number: string | null;
  instagram_url: string | null;
  facebook_url: string | null;
  youtube_url: string | null;
  created_at: string;
  updated_at: string;
}

export function useTeamProfile(teamId?: string) {
  const supabaseClient = useSupabase();

  return useQuery({
    queryKey: [TEAM_PROFILE_QUERY_KEY, teamId],
    queryFn: async () => {
      if (!teamId) {
        throw new Error("Team ID is required");
      }

      const { data, error } = await supabaseClient
        .from("public_team_profiles")
        .select("*")
        .eq("team_account_id", teamId)
        .single();

      if (error) {
        throw error;
      }

      return data as TeamProfile;
    },
    enabled: !!teamId,
  });
}
