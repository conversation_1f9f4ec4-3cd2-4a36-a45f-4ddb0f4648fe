import { useQuery } from "@tanstack/react-query";
import { useAxios } from "./useAxios";
import { useCurrentTeamId } from "./useCurrentTeamId";
import type { CustomDomain } from "@/types/websites";

interface CustomDomainWithWebsite extends CustomDomain {
  website?: {
    id: string;
    title: string;
    subdomain: string;
  };
}

interface UseCustomDomainsOptions {
  websiteId?: string;
  enabled?: boolean;
}

export const useCustomDomains = (options: UseCustomDomainsOptions = {}) => {
  const { axios } = useAxios();
  const [currentTeamId] = useCurrentTeamId();
  const { websiteId, enabled = true } = options;

  return useQuery<CustomDomainWithWebsite[]>({
    queryKey: ["custom-domains", currentTeamId, websiteId],
    queryFn: async () => {
      if (!currentTeamId) {
        throw new Error("No team selected");
      }

      // Build the URL with optional websiteId parameter
      let url = `/domains/team/${currentTeamId}`;
      if (websiteId) {
        url += `?websiteId=${websiteId}`;
      }

      try {
        const response = await axios.get(url);
        if (response.data.success) {
          return response.data.domains || [];
        }
        throw new Error(response.data.error || "Failed to fetch custom domains");
      } catch (error) {
        console.error("Error fetching custom domains:", error);
        throw error;
      }
    },
    enabled: !!currentTeamId && enabled,
  });
};
