import { useQuery } from "@tanstack/react-query";
import { useSupabase } from "./useSupabase";

export interface PropertyMedia {
  id: string;
  property_id: string;
  media_type: string;
  url: string;
  file_name: string;
  file_size: number;
  file_type: string;
  created_at: string;
}

export const usePropertyMedia = (propertyId: string) => {
  const supabase = useSupabase();

  return useQuery({
    queryKey: ["property-media", propertyId],
    queryFn: async () => {
      if (!propertyId) {
        return [];
      }

      const { data, error } = await supabase
        .from("properties_media")
        .select("*")
        .eq("property_id", propertyId)
        .eq("media_type", "image")
        .order("created_at", { ascending: true });

      if (error) {
        throw error;
      }

      return data as PropertyMedia[];
    },
    enabled: !!propertyId,
  });
};
