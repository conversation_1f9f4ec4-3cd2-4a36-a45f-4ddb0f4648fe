import type { Website, WebsiteTheme } from "@/types/websites";
import { useCallback } from "react";
import { useSupabase } from "./useSupabase";

export const useWebsites = () => {
  const supabase = useSupabase();

  const createWebsite = useCallback(
    async (website: Omit<Website, "id" | "created_at" | "updated_at">) => {
      const { data, error } = await supabase.from("websites").insert(website).select().single();

      if (error) throw error;
      return data;
    },
    [supabase],
  );

  const updateWebsite = useCallback(
    async (id: string, updates: Partial<Website>) => {
      const { data, error } = await supabase.from("websites").update(updates).eq("id", id).select().single();

      if (error) throw error;
      return data;
    },
    [supabase],
  );

  const getWebsite = useCallback(
    async (id: string) => {
      const { data, error } = await supabase
        .from("websites")
        .select("*, theme:website_themes!websites_theme_id_fkey(*)")
        .eq("id", id)
        .single();

      if (error) throw error;
      return data;
    },
    [supabase],
  );

  const listTeamWebsites = useCallback(
    async (teamAccountId: string) => {
      const { data, error } = await supabase
        .from("websites")
        .select("*, theme:website_themes!websites_theme_id_fkey(*)")
        .eq("team_account_id", teamAccountId);

      if (error) throw error;
      return data;
    },
    [supabase],
  );

  const createTheme = useCallback(
    async (theme: Omit<WebsiteTheme, "id" | "created_at" | "updated_at">) => {
      const { data, error } = await supabase.from("website_themes").insert(theme).select().single();

      if (error) throw error;
      return data;
    },
    [supabase],
  );

  const updateWebsiteTheme = useCallback(
    async (id: string, updates: Partial<WebsiteTheme>) => {
      const { data, error } = await supabase.from("website_themes").update(updates).eq("id", id).select().single();

      if (error) throw error;
      return data;
    },
    [supabase],
  );

  const deleteWebsite = useCallback(
    async (id: string) => {
      const { error } = await supabase.from("websites").delete().eq("id", id);

      if (error) throw error;
    },
    [supabase],
  );

  return {
    createWebsite,
    updateWebsite,
    getWebsite,
    listTeamWebsites,
    createTheme,
    updateWebsiteTheme,
    deleteWebsite,
  };
};
