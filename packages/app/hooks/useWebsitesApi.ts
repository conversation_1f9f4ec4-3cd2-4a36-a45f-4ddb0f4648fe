import type { Website, WebsiteTheme } from "@/types/websites";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useAxios } from "./useAxios";
import { useCurrentTeamId } from "./useCurrentTeamId";

interface CreateWebsiteRequest {
  team_account_id: string;
  title: string;
  subdomain: string;
  description?: string;
  published?: boolean;
  status?: "draft" | "published" | "archived";
  theme?: {
    primary_color: string;
    secondary_color: string;
    font_family?: string;
  };
}

interface CreateWebsiteResponse {
  success: boolean;
  website: Website;
}

interface GetWebsiteResponse {
  success: boolean;
  website: Website;
}

interface ListWebsitesResponse {
  success: boolean;
  websites: Website[];
}

export const useWebsitesApi = () => {
  const { axios } = useAxios();
  const [currentTeamId] = useCurrentTeamId();

  const createWebsiteMutation = useMutation<CreateWebsiteResponse, Error, CreateWebsiteRequest>({
    mutationFn: async (data) => {
      const response = await axios.post("/websites", data);
      return response.data;
    },
  });

  const getWebsite = async (websiteId: string): Promise<Website> => {
    const response = await axios.get(`/websites/${websiteId}`);
    if (response.data.success) {
      return response.data.website;
    }
    throw new Error(response.data.error || "Failed to fetch website");
  };

  const useTeamWebsites = (options: { enabled?: boolean } = {}) => {
    const { enabled = true } = options;

    return useQuery<Website[]>({
      queryKey: ["websites", "team", currentTeamId],
      queryFn: async () => {
        if (!currentTeamId) {
          throw new Error("No team selected");
        }

        const response = await axios.get(`/websites/team/${currentTeamId}`);
        if (response.data.success) {
          return response.data.websites || [];
        }
        throw new Error(response.data.error || "Failed to fetch websites");
      },
      enabled: !!currentTeamId && enabled,
      staleTime: 30000, // Data is fresh for 30 seconds
      cacheTime: 1000 * 60 * 5, // Cache for 5 minutes
      refetchOnWindowFocus: false, // Don't refetch when window regains focus
      refetchOnMount: true, // Refetch when component mounts
    });
  };

  return {
    createWebsite: async (data: CreateWebsiteRequest) => {
      const result = await createWebsiteMutation.mutateAsync(data);
      return result.website;
    },
    getWebsite,
    useTeamWebsites,
    isCreatingWebsite: createWebsiteMutation.isPending,
    createWebsiteError: createWebsiteMutation.error,
  };
};
