import { useSupabase } from "@/hooks/useSupabase";
import { useQuery } from "@tanstack/react-query";
import type { CurrentUserAccountRoleResponse } from "@/types/basejump_shared";

export const CURRENT_USER_ROLE_QUERY_KEY = "current_user_role";

export function useCurrentUserAccountRole(teamId: string | undefined) {
  const supabaseClient = useSupabase();

  return useQuery<CurrentUserAccountRoleResponse | null, Error>({
    queryKey: [CURRENT_USER_ROLE_QUERY_KEY, teamId],
    queryFn: async () => {
      if (!supabaseClient) {
        throw new Error("Supabase client not initialized");
      }
      if (!teamId) {
        return null; // No role if no team is selected/provided
      }

      const { data, error } = await supabaseClient.rpc(
        "current_user_team_account_role", // Ensure this matches the actual RPC name
        { team_account_id: teamId },
      );

      if (error) {
        // Handle cases where the user might not be part of the team
        if (error.code === "PGRST116" || error.message.includes("User is not a member")) {
          console.warn(`Current user not a member of team ${teamId}`);
          return null; // Treat as no specific role if not a member
        }
        console.error("Error fetching current user role:", error);
        throw new Error(error.message || "Failed to fetch user role");
      }

      // The RPC returns an object like { team_role: 'owner' | 'member' } or null
      return data as CurrentUserAccountRoleResponse | null;
    },
    enabled: !!teamId, // Only run if teamId is available
    // Consider staleTime if role changes are infrequent
    // staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
