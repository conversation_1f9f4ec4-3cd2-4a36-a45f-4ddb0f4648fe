import { useQuery } from "@tanstack/react-query";
import { useSupabase } from "./useSupabase";
import { useTeamAccount } from "./useTeamAccount";
import type { City, Neighborhood, Street, Address } from "@/types";

export const usePropertiesList = () => {
  const supabase = useSupabase();
  const { data: currentTeam } = useTeamAccount();

  return useQuery({
    queryKey: ["properties", currentTeam?.id],
    queryFn: async () => {
      if (!currentTeam?.id) {
        throw new Error("No team selected");
      }

      const { data, error } = await supabase
        .from("properties")
        .select(
          `
          *,
          address:address_id (
            id,
            street_number,
            postcode,
            state,
            country,
            neighborhood:neighborhood_id (id, name),
            city:city_id (id, name, state),
            street:street_id (id, name)
          ),
          prices:properties_prices(
            id,
            property_id,
            sale_price,
            rent_price,
            bnb_price,
            condominium_monthly_tax,
            iptu_monthly_tax,
            insurance_monthly_tax,
            other_monthly_tax
          )
        `,
        )
        .eq("team_account_id", currentTeam.id)
        .order("created_at", { ascending: false });

      if (error) {
        throw error;
      }

      return data;
    },
    enabled: !!currentTeam?.id,
  });
};

export type { Address, City, Neighborhood, Street };
