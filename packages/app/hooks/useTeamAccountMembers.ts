import { useQuery, type UseQueryOptions } from "@tanstack/react-query";
import { useMMKVString } from "react-native-mmkv";

import { supabaseClient } from "@/utils/supabase";
import type { GetAccountMembersResponse } from "@/types/basejump_shared"; // Corrected type name
import { CURRENT_TEAM_ID } from "@/constants";
import { useCurrentTeamId } from "./useCurrentTeamId";

export const TEAM_MEMBERS_QUERY_KEY = "team_account_members";

export const useTeamAccountMembers = (
  teamId?: string,
  options?: Omit<UseQueryOptions<GetAccountMembersResponse>, "queryKey" | "queryFn">,
) => {
  const [currentTeamId] = useCurrentTeamId();

  // If teamId is manually passed, use it. Otherwise, use the current team ID from MMKV
  const queryTeamId = teamId || currentTeamId;

  return useQuery<GetAccountMembersResponse>({
    enabled: !!queryTeamId,
    queryKey: [TEAM_MEMBERS_QUERY_KEY, queryTeamId], // Use the exported constant
    queryFn: async () => {
      const { data, error } = await supabaseClient.rpc("get_team_account_members", {
        team_account_id: queryTeamId,
      });

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    ...options,
  });
};

export default useTeamAccountMembers;
