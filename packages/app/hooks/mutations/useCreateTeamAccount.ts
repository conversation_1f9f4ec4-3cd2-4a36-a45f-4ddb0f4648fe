import { useAxios } from "@/hooks/useAxios";
import { supabaseClient } from "@/utils/supabase";
import { useMutation } from "@tanstack/react-query";

interface CreateTeamAccountData {
  name: string;
  type: "agent" | "agency" | "owner";
  slug?: string;
}

// Response type is handled by the API

export function useCreateTeamAccount() {
  const { axios } = useAxios(); // Use the authenticated axios instance

  return useMutation({
    mutationFn: async (data: CreateTeamAccountData) => {
      const slug = data.slug || data.name.toLowerCase().replace(/\s+/g, "-");

      // Get current user's email
      const { data: { user }, error: userError } = await supabaseClient.auth.getUser();

      if (userError || !user?.email) {
        throw new Error("Failed to get user email");
      }

      try {
        const response = await axios.post("/teams", {
          name: data.name,
          slug,
          type: data.type,
          billing_email: user.email,
        });

        return response;
      } catch (error: unknown) {
        console.error("Error creating team account:", error);
        throw new Error("Failed to create team account");
      }
    },
  });
}
