import { useMutation } from "@tanstack/react-query";
import { useCurrentTeamId } from "../useCurrentTeamId";
import { useSupabase } from "../useSupabase";

type ErrorWithMessage = {
  message: string;
};

interface MediaFile {
  uri: string;
  fileName?: string;
  type: "image" | "video"; // Assuming only images for now based on worker logic
}

interface UploadMediaParams {
  websiteId: string;
  mediaFile: MediaFile;
  mediaType: "logo" | "hero" | "institutional"; // Type of website media being uploaded
}

// Structure expected back from the /proxy-upload endpoint's mediaData
interface WorkerMediaData {
  url: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  databaseId?: string; // Optional DB ID
  // processingMetadata might also be here if needed
}

interface UploadResult {
  success: boolean;
  uploadedFile?: {
    originalUri: string; // Keep track of the original URI for reference
    workerResponse: WorkerMediaData; // Store the data returned by the worker
    key: string; // The R2 key used
  };
  error?: string;
}

export const useUploadWebsiteMedia = () => {
  const supabase = useSupabase();
  const [currentTeamId] = useCurrentTeamId();

  return useMutation<UploadResult, Error, UploadMediaParams>({
    mutationFn: async ({ websiteId, mediaFile, mediaType }) => {
      if (!mediaFile) {
        console.log("No media file to upload");
        return { success: false, error: "No media file provided" };
      }

      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session?.access_token) {
        throw new Error("No authenticated session found");
      }

      const fileUri = mediaFile.uri;
      // Ensure a default filename if none provided
      const fileName =
        mediaFile.fileName ||
        `website_${mediaType}_${websiteId}_${Date.now()}.${mediaFile.type === "image" ? "jpg" : "mp4"}`;
      // Determine fileType based on mediaFile.type, default to jpeg for images
      const fileType = mediaFile.type === "image" ? "image/jpeg" : "video/mp4";

      console.log(`Preparing to upload website ${mediaType} image:`, {
        uri: fileUri,
        name: fileName,
        type: fileType,
        websiteId: websiteId,
      });

      let key = ""; // To store the key generated by get-upload-url

      try {
        // STEP 1: Get the proxy upload URL from the Cloudflare worker
        console.log(`Requesting proxy upload URL for website ${mediaType} image...`);
        const presignedResponse = await fetch(
          `${process.env.EXPO_PUBLIC_API_URL}/media/get-website-upload-url`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session.access_token}`,
            },
            body: JSON.stringify({
              websiteId: websiteId.toString(),
              fileName,
              fileType,
              mediaType,
              teamId: currentTeamId,
            }),
          },
        );

        if (!presignedResponse.ok) {
          const errorText = await presignedResponse.text();
          console.error(`Failed to get upload URL (${presignedResponse.status}):`, errorText);
          throw new Error(`Failed to get upload URL: ${errorText}`);
        }

        const presignedData = await presignedResponse.json();
        const { uploadUrl } = presignedData;
        key = presignedData.key; // Store the key

        if (!uploadUrl || !key) {
          console.error(`Invalid response from /get-website-upload-url:`, presignedData);
          throw new Error("Invalid response received when requesting upload URL.");
        }
        console.log(`Received proxy upload URL: ${uploadUrl.split("?")[0]}... and key: ${key}`);

        // STEP 2: Upload the file to the proxy URL
        console.log(`Fetching file content from URI: ${fileUri}`);
        let fileBlob: Blob;
        try {
          const fileResponse = await fetch(fileUri);
          if (!fileResponse.ok) {
            throw new Error(`Failed to fetch file from URI - Status ${fileResponse.status}`);
          }
          fileBlob = await fileResponse.blob();
          console.log(`File blob created, size: ${fileBlob.size}, type: ${fileBlob.type}`);
        } catch (error: unknown) {
          console.error(`Error fetching file from URI:`, error);
          throw new Error(`Failed to fetch file from URI: ${error instanceof Error ? error.message : String(error)}`);
        }

        console.log(`Uploading file to proxy worker at: ${uploadUrl.split("?")[0]}...`);
        let uploadResponse: Response | undefined;
        try {
          // Use PUT as expected by the worker's handleProxyUpload
          uploadResponse = await fetch(uploadUrl, {
            method: "PUT",
            headers: {
              // The worker determines the final content type after compression,
              // but we send the original type hint in the query param.
              // The 'Content-Type' header here might be less critical for the proxy,
              // but good practice to include. Let's use the blob's type.
              "Content-Type": fileBlob.type || fileType,
              // Include the authorization header for authentication
              Authorization: `Bearer ${session.access_token}`,
            },
            body: fileBlob,
          });

          console.log(`Proxy upload response status: ${uploadResponse.status}`);
        } catch (error: unknown) {
          console.error(`Error during proxy upload fetch:`, error);
          throw new Error(
            `Network error during proxy upload: ${error instanceof Error ? error.message : String(error)}`,
          );
        }

        // STEP 3: Process the response from the proxy worker
        if (!uploadResponse || !uploadResponse.ok) {
          let errorDetails = "Unknown error during proxy upload";
          try {
            const errorJson = await uploadResponse.json();
            errorDetails = errorJson.error || JSON.stringify(errorJson);
            console.error(`Proxy upload failed (${uploadResponse.status}):`, errorDetails);
          } catch (e) {
            const errorText = await uploadResponse.text().catch(() => "Could not read error response text");
            errorDetails = errorText;
            console.error(`Proxy upload failed (${uploadResponse.status}):`, errorText);
          }
          throw new Error(`Proxy upload failed: ${errorDetails}`);
        }

        // If upload was successful, the worker returns JSON with mediaData
        const resultData = await uploadResponse.json();

        if (!resultData.success || !resultData.mediaData) {
          console.error(
            `Proxy upload response indicates failure or missing mediaData:`,
            resultData,
          );
          throw new Error(resultData.error || "Proxy upload succeeded but returned invalid data.");
        }

        console.log(`Proxy upload successful. Worker response:`, resultData.mediaData);

        // STEP 4: Update the website record with the new image URL
        const { error: updateError } = await supabase
          .from("websites")
          .update({
            [`${mediaType}_image_url`]: resultData.mediaData.url,
          })
          .eq("id", websiteId);

        if (updateError) {
          console.error(`Error updating website with ${mediaType} image URL:`, updateError);
          throw new Error(`Failed to update website with ${mediaType} image URL: ${updateError.message}`);
        }

        // Return the successful upload details
        return {
          success: true,
          uploadedFile: {
            originalUri: fileUri,
            workerResponse: resultData.mediaData as WorkerMediaData,
            key: key,
          },
        };
      } catch (error) {
        console.error(`Error processing file ${fileName}:`, error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
        };
      }
    },
  });
};
