import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useSupabase } from "../useSupabase";

interface UpdatePropertyPayload {
  property: {
    id: string;
    title?: string;
    description?: string;
    purpose?: "residential" | "commercial" | "mixed";
    type?: string;
    floors_count?: number;
    floor?: number;
    rooms_count?: number;
    bathrooms_count?: number;
    parking_spots_count?: number;
    suites_count?: number;
    built_area?: number;
    total_area?: number;
    pets_allowed?: boolean;
    available_for_rent?: boolean;
    available_for_sale?: boolean;
    available_for_bnb?: boolean;
    furnished?: boolean;
  };
  prices?: {
    id: string;
    sale_price?: number;
    rent_price?: number;
    bnb_price?: number;
    condominium_monthly_tax?: number;
    iptu_monthly_tax?: number;
    insurance_monthly_tax?: number;
    other_monthly_tax?: number;
  };
}

export const useUpdateProperty = () => {
  const supabase = useSupabase();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: UpdatePropertyPayload) => {
      if (!payload.property.id) {
        throw new Error("Property ID is required");
      }

      // Update property details
      const { data: updatedProperty, error: propertyError } = await supabase
        .from("properties")
        .update(payload.property)
        .eq("id", payload.property.id)
        .select()
        .single();

      if (propertyError) {
        throw propertyError;
      }

      // Update prices if provided
      if (payload.prices && payload.prices.id) {
        const { error: pricesError } = await supabase
          .from("properties_prices")
          .update(payload.prices)
          .eq("id", payload.prices.id);

        if (pricesError) {
          throw pricesError;
        }
      }

      return updatedProperty;
    },
    onSuccess: (data) => {
      // Invalidate property query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["property", data.id] });
      queryClient.invalidateQueries({ queryKey: ["properties"] });
    },
  });
};
