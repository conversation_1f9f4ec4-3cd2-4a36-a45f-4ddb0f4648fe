import type { FormFields } from "@/components/CreateNewProperty/form";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAxios } from "../useAxios";

const formatPriceForUpload = (price?: string) => {
  if (!price) return 0;
  // Remove all non-numeric characters
  const justNumbers = price.replace(/[^0-9]/g, '');
  // Check if the original input had leading zeros
  const hasLeadingZeros = justNumbers.startsWith('0');
  // Remove leading zeros
  const withoutLeadingZeros = justNumbers.replace(/^0+/, '') || '0';
  // Parse the number
  const num = Number.parseInt(withoutLeadingZeros, 10);

  // Apply special formatting for numbers 1-99 (add '00')
  // But only if the original input didn't have leading zeros
  if (num >= 1 && num <= 99 && !hasLeadingZeros) {
    return num * 100; // Equivalent to adding '00'
  }

  // Return the number as is for other cases
  return num;
};

interface CreatePropertyPayload {
  property: {
    title: string;
    description: string;
    team_account_id: string;
    purpose: "residential" | "commercial" | "mixed";
    type: string;
    floors_count?: number;
    floor?: number;
    rooms_count?: number;
    bathrooms_count?: number;
    parking_spots_count?: number;
    suites_count?: number;
    built_area?: number;
    total_area?: number;
    pets_allowed: boolean;
    available_for_rent: boolean;
    available_for_sale: boolean;
    available_for_bnb: boolean;
    furnished: boolean;
    address_complement?: string;
    address_id: string;
  };
  prices: {
    sale_price?: number;
    rent_price?: number;
    bnb_price?: number;
    condominium_monthly_tax?: number;
    iptu_monthly_tax?: number;
    insurance_monthly_tax?: number;
    other_monthly_tax?: number;
  };
  property_amenities?: Record<string, boolean>;
  building_amenities?: Record<string, boolean>;
}

export const useCreateProperty = () => {
  const { axios } = useAxios();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (formData: FormFields) => {
      const payload: CreatePropertyPayload = {
        property: {
          team_account_id: formData.team_account_id,
          title: formData.title,
          description: formData.description,
          purpose: formData.purpose,
          type: formData.type,
          floors_count: formData.floors_count,
          floor: formData.floor,
          rooms_count: formData.rooms_count,
          bathrooms_count: formData.bathrooms_count,
          parking_spots_count: formData.parking_spots_count,
          suites_count: formData.suites_count,
          built_area: formData.built_area,
          total_area: formData.total_area,
          pets_allowed: formData.pets_allowed,
          available_for_rent: formData.available_for_rent,
          available_for_sale: formData.available_for_sale,
          available_for_bnb: formData.available_for_bnb,
          furnished: formData.furnished,
          address_complement: formData.address_complement,
          address_id: formData.address_id,
        },
        prices: {
          sale_price: formatPriceForUpload(formData.sale_price),
          rent_price: formatPriceForUpload(formData.rent_price),
          bnb_price: formatPriceForUpload(formData.bnb_price),
          condominium_monthly_tax: formatPriceForUpload(formData.condominium_monthly_tax),
          iptu_monthly_tax: formatPriceForUpload(formData.iptu_monthly_tax),
          insurance_monthly_tax: formatPriceForUpload(formData.insurance_monthly_tax),
          other_monthly_tax: formatPriceForUpload(formData.other_monthly_tax),
        },
        property_amenities: formData.property_amenities,
        building_amenities: formData.building_amenities,
      };

      try {
        console.log("Sending property creation request to API:", payload);
        const response = await axios.post("/properties", payload);
        console.log("API response:", response.data);
        return response.data;
      } catch (error) {
        console.error("Error creating property:", error);
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate properties query to trigger a refetch
      queryClient.invalidateQueries({ queryKey: ["properties"] });
    },
  });
};
