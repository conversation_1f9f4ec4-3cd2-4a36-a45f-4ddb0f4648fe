import { useSupabase } from "@/hooks/useSupabase";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { TEAM_PROFILE_QUERY_KEY } from "../useTeamProfile";

export interface UpdateTeamProfileData {
  name?: string;
  address?: string;
  phone_number?: string;
  whatsapp_number?: string;
  instagram_url?: string;
  facebook_url?: string;
  youtube_url?: string;
  about_us?: string;
}

export function useUpdateTeamProfile(teamId?: string) {
  const supabaseClient = useSupabase();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateTeamProfileData) => {
      if (!teamId) {
        throw new Error("Team ID is required");
      }

      const { data: updatedProfile, error } = await supabaseClient
        .from("public_team_profiles")
        .update({
          name: data.name,
          address: data.address,
          phone_number: data.phone_number,
          whatsapp_number: data.whatsapp_number,
          instagram_url: data.instagram_url,
          facebook_url: data.facebook_url,
          youtube_url: data.youtube_url,
          about_us: data.about_us,
        })
        .eq("team_account_id", teamId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return updatedProfile;
    },
    onSuccess: () => {
      // Invalidate the team profile query to refetch the data
      queryClient.invalidateQueries({ queryKey: [TEAM_PROFILE_QUERY_KEY, teamId] });
    },
  });
}
