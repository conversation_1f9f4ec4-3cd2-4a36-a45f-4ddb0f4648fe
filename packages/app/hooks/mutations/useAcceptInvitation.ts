import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useSupabase } from "@/hooks/useSupabase";
import { TEAM_MEMBERS_QUERY_KEY } from "../useTeamAccountMembers";
import type { AcceptInvitationResponse } from "@/types/basejump_shared";

interface AcceptInvitationProps {
  token: string;
}

export function useAcceptInvitation() {
  const queryClient = useQueryClient();
  const supabaseClient = useSupabase();

  return useMutation<AcceptInvitationResponse, Error, AcceptInvitationProps>({
    mutationFn: async ({ token }) => {
      if (!supabaseClient) {
        throw new Error("Supabase client not initialized");
      }

      try {
        // Use the RPC function that already exists
        console.log("Accepting invitation with token:", token);
        const { data, error } = await supabaseClient.rpc("accept_invitation", {
          lookup_invitation_token: token
        });
        
        if (error) {
          console.error("Error accepting invitation:", error);
          throw new Error(error.message || "Failed to accept invitation");
        }
        
        if (!data) {
          throw new Error("No data returned from accept_invitation");
        }
        
        return {
          account_id: data.team_id,
          account_role: data.account_role,
          slug: data.slug
        };
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : String(err);
        console.error("Error in acceptInvitation:", errorMessage);
        throw new Error(errorMessage || "Failed to accept invitation");
      }
    },
    onSuccess: () => {
      // Invalidate the team members query to refresh the list
      queryClient.invalidateQueries({ queryKey: [TEAM_MEMBERS_QUERY_KEY] });
    },
  });
}
