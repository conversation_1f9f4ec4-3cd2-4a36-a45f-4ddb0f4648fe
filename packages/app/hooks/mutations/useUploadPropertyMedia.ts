import { useMutation } from "@tanstack/react-query";
import { useCurrentTeamId } from "../useCurrentTeamId";
import { useSupabase } from "../useSupabase";

type ErrorWithMessage = {
  message: string;
};

interface MediaFile {
  uri: string;
  fileName?: string;
  type: "image" | "video"; // Assuming only images for now based on worker logic
}

interface UploadMediaParams {
  propertyId: string;
  mediaFiles: MediaFile[];
}

// Structure expected back from the /proxy-upload endpoint's mediaData
interface WorkerMediaData {
  url: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  databaseId?: string; // Optional DB ID
  // processingMetadata might also be here if needed
}

interface UploadResult {
  success: boolean;
  uploadedFiles: Array<{
    originalUri: string; // Keep track of the original URI for reference
    workerResponse: WorkerMediaData; // Store the data returned by the worker
    key: string; // The R2 key used
  }>;
}

export const useUploadPropertyMedia = () => {
  const supabase = useSupabase();
  const [currentTeamId] = useCurrentTeamId();

  return useMutation<UploadResult, Error, UploadMediaParams>({
    mutationFn: async ({ propertyId, mediaFiles }) => {
      if (!mediaFiles.length) {
        console.log("No media files to upload");
        return { success: true, uploadedFiles: [] };
      }

      const uploadedFiles: UploadResult["uploadedFiles"] = [];
      const {
        data: { session },
      } = await supabase.auth.getSession();

      if (!session?.access_token) {
        throw new Error("No authenticated session found");
      }

      // Process each media file
      for (const [mediaIndex, mediaFile] of mediaFiles.entries()) {
        const fileUri = mediaFile.uri;
        // Ensure a default filename if none provided
        const fileName =
          mediaFile.fileName ||
          `media_${propertyId}_${Date.now()}_${mediaIndex}.${mediaFile.type === "image" ? "jpg" : "mp4"}`;
        // Determine fileType based on mediaFile.type, default to jpeg for images
        const fileType = mediaFile.type === "image" ? "image/jpeg" : "video/mp4"; // Adjust if worker handles more types

        console.log(`[${mediaIndex + 1}/${mediaFiles.length}] Preparing to upload:`, {
          uri: fileUri,
          name: fileName,
          type: fileType,
          propertyId: propertyId,
        });

        let key = ""; // To store the key generated by get-upload-url

        try {
          // STEP 1: Get the proxy upload URL from the Cloudflare worker
          console.log(`[${mediaIndex + 1}] Requesting proxy upload URL...`);
          const presignedResponse = await fetch(
            `${process.env.EXPO_PUBLIC_API_URL}/media/get-upload-url`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${session.access_token}`,
              },
              body: JSON.stringify({
                propertyId: propertyId.toString(),
                fileName,
                fileType,
                teamId: currentTeamId,
              }),
            },
          );

          if (!presignedResponse.ok) {
            const errorText = await presignedResponse.text();
            console.error(`[${mediaIndex + 1}] Failed to get upload URL (${presignedResponse.status}):`, errorText);
            throw new Error(`Failed to get upload URL: ${errorText}`);
          }

          const presignedData = await presignedResponse.json();
          const { uploadUrl } = presignedData;
          key = presignedData.key; // Store the key

          if (!uploadUrl || !key) {
            console.error(`[${mediaIndex + 1}] Invalid response from /get-upload-url:`, presignedData);
            throw new Error("Invalid response received when requesting upload URL.");
          }
          console.log(`[${mediaIndex + 1}] Received proxy upload URL: ${uploadUrl.split("?")[0]}... and key: ${key}`);

          // STEP 2: Upload the file to the proxy URL
          console.log(`[${mediaIndex + 1}] Fetching file content from URI: ${fileUri}`);
          let fileBlob: Blob;
          try {
            const fileResponse = await fetch(fileUri);
            if (!fileResponse.ok) {
              throw new Error(`Failed to fetch file from URI - Status ${fileResponse.status}`);
            }
            fileBlob = await fileResponse.blob();
            console.log(`[${mediaIndex + 1}] File blob created, size: ${fileBlob.size}, type: ${fileBlob.type}`);
          } catch (error: unknown) {
            console.error(`[${mediaIndex + 1}] Error fetching file from URI:`, error);
            throw new Error(`Failed to fetch file from URI: ${error instanceof Error ? error.message : String(error)}`);
          }

          console.log(`[${mediaIndex + 1}] Uploading file to proxy worker at: ${uploadUrl.split("?")[0]}...`);
          let uploadResponse: Response | undefined;
          try {
            // Use PUT as expected by the worker's handleProxyUpload
            uploadResponse = await fetch(uploadUrl, {
              method: "PUT",
              headers: {
                // The worker determines the final content type after compression,
                // but we send the original type hint in the query param.
                // The 'Content-Type' header here might be less critical for the proxy,
                // but good practice to include. Let's use the blob's type.
                "Content-Type": fileBlob.type || fileType,
                // Include the authorization header for authentication
                Authorization: `Bearer ${session.access_token}`,
              },
              body: fileBlob,
            });

            console.log(`[${mediaIndex + 1}] Proxy upload response status: ${uploadResponse.status}`);
          } catch (error: unknown) {
            console.error(`[${mediaIndex + 1}] Error during proxy upload fetch:`, error);
            throw new Error(
              `Network error during proxy upload: ${error instanceof Error ? error.message : String(error)}`,
            );
          }

          // STEP 3: Process the response from the proxy worker
          if (!uploadResponse || !uploadResponse.ok) {
            let errorDetails = "Unknown error during proxy upload";
            try {
              const errorJson = await uploadResponse.json();
              errorDetails = errorJson.error || JSON.stringify(errorJson);
              console.error(`[${mediaIndex + 1}] Proxy upload failed (${uploadResponse.status}):`, errorDetails);
            } catch (e) {
              const errorText = await uploadResponse.text().catch(() => "Could not read error response text");
              errorDetails = errorText;
              console.error(`[${mediaIndex + 1}] Proxy upload failed (${uploadResponse.status}):`, errorText);
            }
            throw new Error(`Proxy upload failed: ${errorDetails}`);
          }

          // If upload was successful, the worker returns JSON with mediaData
          const resultData = await uploadResponse.json();

          if (!resultData.success || !resultData.mediaData) {
            console.error(
              `[${mediaIndex + 1}] Proxy upload response indicates failure or missing mediaData:`,
              resultData,
            );
            throw new Error(resultData.error || "Proxy upload succeeded but returned invalid data.");
          }

          console.log(`[${mediaIndex + 1}] Proxy upload successful. Worker response:`, resultData.mediaData);

          // Add the successful upload details to our results array
          uploadedFiles.push({
            originalUri: fileUri,
            workerResponse: resultData.mediaData as WorkerMediaData,
            key: key, // Include the key used for this upload
          });
        } catch (error) {
          console.error(`[${mediaIndex + 1}] Error processing file ${fileName}:`, error);
          // Decide on error handling: stop all uploads or continue?
          // Currently, it throws, stopping the loop.
          // To continue, you could push an error object to uploadedFiles and remove the throw.
          throw error;
        }
      } // End of loop

      console.log("All media files processed.");
      return { success: true, uploadedFiles };
    },
    // onError, onSettled etc. can be added here if needed
  });
};
