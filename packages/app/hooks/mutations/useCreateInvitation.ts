import { useAxios } from "@/hooks/useAxios";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { TEAM_INVITATIONS_QUERY_KEY } from "@/hooks/useTeamInvitations";
import { createTeamInvitesApi } from "@/utils/teamInvitesApi";

type CreateInvitationVariables = {
  teamId: string;
  email: string;
  role: "member" | "owner";
};

type CreateInvitationResponse = {
  token: string;
  invitation_id: string;
};

export function useCreateInvitation() {
  const { axios } = useAxios();
  const queryClient = useQueryClient();
  const teamInvitesApi = createTeamInvitesApi(axios);

  return useMutation<CreateInvitationResponse, Error, CreateInvitationVariables>({
    mutationFn: async ({ teamId, email, role }: CreateInvitationVariables) => {
      if (!teamId) {
        throw new Error("Team ID is required");
      }
      if (!email) {
        throw new Error("Email is required");
      }
      if (!role) {
        throw new Error("Role is required");
      }

      const response = await teamInvitesApi.createTeamInvite({
        team_id: teamId,
        email,
        team_role: role,
      });

      if (!response.success) {
        throw new Error("Failed to create invitation");
      }

      return {
        token: response.invitation.token,
        invitation_id: response.invitation.id,
      };
    },
    onSuccess: (_: CreateInvitationResponse, variables: CreateInvitationVariables) => {
      // Invalidate the invitations query to refetch the list
      queryClient.invalidateQueries({
        queryKey: [TEAM_INVITATIONS_QUERY_KEY, variables.teamId],
      });
      console.log("Invitation created successfully");
    },
    onError: (error: Error) => {
      console.error("Mutation error creating invitation:", error.message);
    },
  });
}