import { useAxios } from "@/hooks/useAxios";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { TEAM_INVITATIONS_QUERY_KEY } from "@/hooks/useTeamInvitations";
import { createTeamInvitesApi } from "@/utils/teamInvitesApi";

type DeleteInvitationVariables = {
  invitationId: string;
  teamId: string; // Needed to invalidate the correct query
};

export function useDeleteInvitation() {
  const { axios } = useAxios();
  const queryClient = useQueryClient();
  const teamInvitesApi = createTeamInvitesApi(axios);

  return useMutation<void, Error, DeleteInvitationVariables>({
    mutationFn: async ({ invitationId }: DeleteInvitationVariables) => {
      if (!invitationId) {
        throw new Error("Invitation ID is required");
      }

      const response = await teamInvitesApi.deleteTeamInvite(invitationId);

      if (!response.success) {
        throw new Error("Failed to delete invitation");
      }
    },
    onSuccess: (_: void, variables: DeleteInvitationVariables) => {
      // Invalidate the invitations query to refetch the list
      queryClient.invalidateQueries({
        queryKey: [TEAM_INVITATIONS_QUERY_KEY, variables.teamId],
      });
      console.log("Invitation deleted successfully:", variables.invitationId);
      // TODO: Add user feedback (e.g., toast notification)
    },
    onError: (error: Error) => {
      console.error("Mutation error deleting invitation:", error.message);
      // TODO: Add user feedback (e.g., toast notification)
    },
  });
}
