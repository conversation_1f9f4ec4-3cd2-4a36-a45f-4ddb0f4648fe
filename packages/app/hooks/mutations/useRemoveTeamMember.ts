import { useSupabase } from "@/hooks/useSupabase";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { TEAM_MEMBERS_QUERY_KEY } from "@/hooks/useTeamAccountMembers"; // Assuming this key structure

type RemoveTeamMemberVariables = {
  teamId: string;
  userId: string;
};

export function useRemoveTeamMember() {
  const supabaseClient = useSupabase();
  const queryClient = useQueryClient();

  return useMutation<void, Error, RemoveTeamMemberVariables>({
    mutationFn: async ({ teamId, userId }: RemoveTeamMemberVariables) => {
      if (!supabaseClient) {
        throw new Error("Supabase client not initialized");
      }
      if (!teamId || !userId) {
        throw new Error("Team ID and User ID are required");
      }

      const { error } = await supabaseClient.rpc("remove_team_account_member", {
        team_account_id: teamId, // Ensure param name matches RPC definition
        user_id: userId,
      });

      if (error) {
        console.error("Error removing team member:", error);
        // Handle specific errors, e.g., trying to remove the primary owner
        if (error.message.includes("primary owner")) {
          throw new Error("Cannot remove the primary owner of the team.");
        }
        throw new Error(error.message || "Failed to remove team member");
      }
    },
    onSuccess: (_, variables) => {
      // Invalidate the members query to refetch the list
      queryClient.invalidateQueries({
        queryKey: [TEAM_MEMBERS_QUERY_KEY, variables.teamId],
      });
      console.log("Team member removed successfully:", variables.userId);
      // TODO: Add user feedback (e.g., toast notification)
    },
    onError: (error: Error) => {
      console.error("Mutation error removing team member:", error.message);
      // TODO: Add user feedback (e.g., toast notification)
    },
  });
}
