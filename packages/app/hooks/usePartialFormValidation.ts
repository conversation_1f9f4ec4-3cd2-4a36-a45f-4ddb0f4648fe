import { type Control, useFormContext, useWatch, type FieldValues, type Path } from "react-hook-form";
import { useState, useCallback, useEffect, useRef } from "react";

interface Props<T extends FieldValues> {
  name: Path<T> | Path<T>[];
  control?: Control<T>;
}

export const usePartialFormValidation = <T extends FieldValues>({ name, control }: Props<T>) => {
  const formContext = useFormContext<T>();
  const [isValid, setValid] = useState(false);
  const validateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const prevValuesRef = useRef<any>(null);
  
  const formControl = control || formContext?.control;

  const formValues = useWatch<T>({
    name: Array.isArray(name) ? name : [name],
    control: formControl,
  });

  const validateFields = useCallback(async () => {
    if (!formValues || !formControl || !formContext) return;
    const fields = Array.isArray(name) ? name : [name];
    
    try {
      // Get form state info for each field
      const fieldsInfo = fields.map(field => {
        const fieldState = formContext.getFieldState(field as Path<T>);
        const fieldValue = formContext.getValues(field as Path<T>);
        return { field, state: fieldState, value: fieldValue };
      });
      
      // Check if any fields have been touched or submitted
      const anyFieldInteracted = fieldsInfo.some(f => {
        return f.state.isTouched || f.state.invalid;
      });
      
      // When a value changes, always validate regardless of previous touch state
      const valueChanged = prevValuesRef.current !== null && 
        JSON.stringify(prevValuesRef.current) !== JSON.stringify(formValues);
      
      if (anyFieldInteracted || valueChanged) {
        // Always trigger validation if values have changed after a submission
        await formContext.trigger(fields as Path<T>[], { shouldFocus: false });
        
        // Check the current error state
        const hasErrors = fields.some(field => {
          const fieldState = formContext.getFieldState(field as Path<T>);
          return !!fieldState.error;
        });
        
        setValid(!hasErrors);
      } else {
        // If no fields have been interacted with, consider the form section valid
        setValid(true);
      }
    } catch (error) {
      console.error('Error validating fields:', error);
      setValid(false);
    }
  }, [formValues, formControl, name, formContext]);

  useEffect(() => {
    // Only validate if values have actually changed
    const currentValuesJson = JSON.stringify(formValues);
    const prevValuesJson = JSON.stringify(prevValuesRef.current);
    
    if (currentValuesJson !== prevValuesJson) {
      prevValuesRef.current = formValues;
      
      // Debounce validation to prevent rapid consecutive validations
      if (validateTimeoutRef.current) {
        clearTimeout(validateTimeoutRef.current);
      }
      
      validateTimeoutRef.current = setTimeout(() => {
        validateFields();
      }, 100);
    }
    
    return () => {
      if (validateTimeoutRef.current) {
        clearTimeout(validateTimeoutRef.current);
        validateTimeoutRef.current = null;
      }
    };
  }, [formValues, validateFields]);

  return { isValid };
};
