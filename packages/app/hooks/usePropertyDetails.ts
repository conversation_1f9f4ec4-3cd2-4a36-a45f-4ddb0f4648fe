import type { Property } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { useSupabase } from "./useSupabase";
import { useTeamAccount } from "./useTeamAccount";

export const usePropertyDetails = (propertyId: string) => {
  const supabase = useSupabase();
  const { data: currentTeam } = useTeamAccount();

  return useQuery({
    queryKey: ["property", propertyId, currentTeam?.id],
    queryFn: async () => {
      if (!currentTeam?.id) {
        throw new Error("No team selected");
      }

      if (!propertyId) {
        throw new Error("No property ID provided");
      }

      const { data, error } = await supabase
        .from("properties")
        .select(
          `
          *,
          address:address_id (
            id,
            street_number,
            postcode,
            state,
            country,
            neighborhood:neighborhood_id (id, name),
            city:city_id (id, name, state),
            street:street_id (id, name)
          ),
          prices:properties_prices(
            id,
            property_id,
            sale_price,
            rent_price,
            bnb_price,
            condominium_monthly_tax,
            iptu_monthly_tax,
            insurance_monthly_tax,
            other_monthly_tax
          ),
          property_amenities:property_amenities_id(
            id,
            property_id,
            barbecue_grill,
            gourmet_space,
            garden,
            pool,
            backyard,
            water_heating,
            heating,
            air_conditioning,
            internet,
            garage,
            fireplace,
            laundry,
            sauna,
            spa,
            security_system
          ),
          building_amenities:building_amenities_id(
            id,
            property_id,
            shared_barbecue_grill,
            shared_gourmet_space,
            bicycle_storage,
            intercom,
            gym,
            green_area,
            playground,
            shared_pool,
            tennis_court,
            sports_area,
            party_room,
            game_room,
            storage,
            shared_laundry,
            elevator,
            shared_garage,
            shared_water_heating,
            power_generator,
            reception,
            shared_sauna,
            shared_spa,
            shared_security_system,
            gated_community,
            private_security
          )
        `,
        )
        .eq("team_account_id", currentTeam.id)
        .eq("id", propertyId)
        .single();

      if (error) {
        throw error;
      }

      return data as Property;
    },
    enabled: !!currentTeam?.id && !!propertyId,
  });
};
