import type { Product } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { useAxios } from "./useAxios";

const getPrice = (product: Product, isYearly: boolean) => {
  if (!product.prices) return null;

  return product.prices.find((price) => {
    return price.recurring.interval === (isYearly ? "year" : "month");
  });
};

export const usePricingProducts = (isYearly: boolean) => {
  const { axios } = useAxios();

  return useQuery<Product[], Error>({
    queryKey: ["pricingProducts"],
    queryFn: async () => {
      try {
        console.log("Fetching pricing products with authenticated axios");
        const response = await axios.get("/billing/products");
        console.log("Pricing products response:", response.status);

        if (!response.data?.products) {
          throw new Error("No pricing plans available.");
        }

        // Sort products by metadata.order first, then by price
        const sortedProducts = (response.data.products as Product[]).sort((a: Product, b: Product) => {
          // First, sort by metadata.order
          const orderA = Number.parseInt(a.metadata?.order ?? "999");
          const orderB = Number.parseInt(b.metadata?.order ?? "999");

          if (orderA !== orderB) {
            return orderA - orderB;
          }

          // If orders are equal, sort by price as a fallback
          const aPrice = getPrice(a, isYearly);
          const bPrice = getPrice(b, isYearly);
          const aAmount = aPrice?.unit_amount ?? 0;
          const bAmount = bPrice?.unit_amount ?? 0;
          return aAmount - bAmount;
        });

        return sortedProducts;
      } catch (error) {
        console.error("Error fetching products:", error);

        // Check if it's an axios error with response data
        if (error.response) {
          console.error("Response error data:", error.response.status, error.response.data);

          // If it's a 401 unauthorized error
          if (error.response.status === 401) {
            throw new Error("Authentication required. Please sign in to view pricing plans.");
          }
        }

        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
        throw new Error(errorMessage || "Failed to load pricing plans. Please try again later.");
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};
