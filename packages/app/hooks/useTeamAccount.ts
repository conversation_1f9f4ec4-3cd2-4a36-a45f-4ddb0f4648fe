import { useQuery, type UseQueryOptions } from "@tanstack/react-query";

import { supabaseClient } from "@/utils/supabase";
import type { GetAccountResponse } from "@/types";
import { useCurrentTeamId } from "./useCurrentTeamId";

export const useTeamAccount = (
  teamId?: string,
  options?: Omit<UseQueryOptions<GetAccountResponse>, "queryKey" | "queryFn">,
) => {
  const [currentTeamId] = useCurrentTeamId();

  // If teamId is manually passed, use it. Otherwise, use the current team ID from MMKV
  const queryTeamId = teamId || currentTeamId;

  return useQuery<GetAccountResponse>({
    enabled: !!queryTeamId,
    queryKey: ["team_accounts", queryTeamId],
    queryFn: async () => {
      const { data, error } = await supabaseClient.rpc("get_team_account", {
        team_account_id: queryTeamId,
      });

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    ...options,
  });
};

export default useTeamAccount;
