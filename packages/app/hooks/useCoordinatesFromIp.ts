import { useAxios } from '@/hooks'

export const useCoordinatesFromIp = () => {
  const { axios } = useAxios()

  const fetchAutocomplete = async (ip: string): Promise<geoapifyAutocompleteItem> => {
    const { data: geocodingData } = await axios.get(`/geocode?address=${address}`)
    return geocodingData.data as geoapifyAutocompleteItem
  }

  return fetchAutocomplete
}

export default useAutocompleteAddress
