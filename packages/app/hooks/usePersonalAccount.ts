import { supabaseClient } from "@/utils/supabase";
import { useQuery } from "@tanstack/react-query";

export interface PersonalAccount {
  account_id: string;
  user_id: string;
  email?: string;
}

export const usePersonalAccount = () => {
  return useQuery<PersonalAccount>({
    queryKey: ["personal_account"],
    queryFn: async () => {
      const { data, error } = await supabaseClient.rpc("get_personal_account");
      
      if (error) {
        console.error("Error fetching personal account:", error);
        throw error;
      }

      // Fetch email separately
      const { data: { user } } = await supabaseClient.auth.getUser();
      
      return {
        ...data,
        email: user?.email
      };
    }
  });
};
