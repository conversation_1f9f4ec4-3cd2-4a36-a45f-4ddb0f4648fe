import { useTheme } from '@/theme';
import { brand, warning, success } from '@/theme/colors';
import { error } from '@/theme/colors/error';

const colorStops = {
  brand: {
    50: [`rgba(${brand[50]}, 1)`, 'rgba(255,255,255,1)'] as const,
    100: [`rgba(${brand[100]}, 1)`, 'rgba(255,255,255,1)'] as const,
    200: [`rgba(${brand[200]}, 1)`, 'rgba(255,255,255,1)'] as const,
    300: [`rgba(${brand[300]}, 1)`, 'rgba(255,255,255,1)'] as const,
    400: [`rgba(${brand[400]}, 1)`, 'rgba(255,255,255,1)'] as const,
    500: [`rgba(${brand[500]}, 1)`, 'rgba(255,255,255,1)'] as const,
  },
  warning: {
    50: [`rgba(${warning[50]}, 1)`, 'rgba(255,255,255,1)'] as const,
    100: [`rgba(${warning[100]}, 1)`, 'rgba(255,255,255,1)'] as const,
    200: [`rgba(${warning[200]}, 1)`, 'rgba(255,255,255,1)'] as const,
    300: [`rgba(${warning[300]}, 1)`, 'rgba(255,255,255,1)'] as const,
    400: [`rgba(${warning[400]}, 1)`, 'rgba(255,255,255,1)'] as const,
    500: [`rgba(${warning[500]}, 1)`, 'rgba(255,255,255,1)'] as const,
  },
  success: {
    50: [`rgba(${success[50]}, 1)`, 'rgba(255,255,255,1)'] as const,
    100: [`rgba(${success[100]}, 1)`, 'rgba(255,255,255,1)'] as const,
    200: [`rgba(${success[200]}, 1)`, 'rgba(255,255,255,1)'] as const,
    300: [`rgba(${success[300]}, 1)`, 'rgba(255,255,255,1)'] as const,
    400: [`rgba(${success[400]}, 1)`, 'rgba(255,255,255,1)'] as const,
    500: [`rgba(${success[500]}, 1)`, 'rgba(255,255,255,1)'] as const,
  },
  error: {
    50: [`rgba(${error[50]}, 1)`, 'rgba(255,255,255,1)'] as const,
    100: [`rgba(${error[100]}, 1)`, 'rgba(255,255,255,1)'] as const,
    200: [`rgba(${error[200]}, 1)`, 'rgba(255,255,255,1)'] as const,
    300: [`rgba(${error[300]}, 1)`, 'rgba(255,255,255,1)'] as const,
    400: [`rgba(${error[400]}, 1)`, 'rgba(255,255,255,1)'] as const,
    500: [`rgba(${error[500]}, 1)`, 'rgba(255,255,255,1)'] as const,
  },
} as const;

export const useGradient = (color: 'primary' | 'warning' | 'success' | 'error') => {
  const { colorScheme } = useTheme();

  let effectiveColor: keyof typeof colorStops = 'brand'; // Default to brand

  if (color === 'primary') {
    if (colorScheme in colorStops) {
      effectiveColor = colorScheme as keyof typeof colorStops;
    } else {
      console.warn(
        `Primary color scheme '${colorScheme}' is not a valid gradient color. Falling back to 'brand'.`
      );
    }
  } else {
    if (color in colorStops) {
      effectiveColor = color as keyof typeof colorStops;
    } else {
      console.warn(
        `Invalid gradient color '${color}'. Falling back to 'brand'.`
      );
    }
  }

  return colorStops[effectiveColor];
};