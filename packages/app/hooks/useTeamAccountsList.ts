import { useQuery, type UseQueryOptions } from "@tanstack/react-query";
import { supabaseClient } from "@/utils/supabase";
import type { GetAccountsResponse } from "@/types";

export const useTeamAccountsList = (options?: Omit<UseQueryOptions<GetAccountsResponse>, "queryKey" | "queryFn">) => {
  return useQuery<GetAccountsResponse>({
    queryKey: ["team-accounts"],
    queryFn: async () => {
      const { data, error } = await supabaseClient.rpc("get_team_accounts");

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    ...options,
  });
};
