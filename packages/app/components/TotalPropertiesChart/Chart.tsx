// import React, { useState } from "react";

// import { Text, Box, Button } from "@/components/ui";
// import { Cartesian<PERSON><PERSON>, Bar, useChartPressState } from "victory-native";
// import { Circle, vec } from "@shopify/react-native-skia";
// import { View, useColorScheme } from "react-native";
// import { LinearGradient, Text as SKText } from "@shopify/react-native-skia";
// import { useDerivedValue } from "react-native-reanimated";

// // const inter = require("../../roboto.ttf");

// const DATA = (length = 10) =>
//   Array.from({ length }, (_, index) => ({
//     month: index + 1,
//     listenCount: Math.floor(Math.random() * (100 - 50 + 1)) + 50,
//   }));

// export const BarChart = () => {
//   const [data, setData] = useState(DATA(5));
//   const colorMode = "light";
//   const { state, isActive } = useChartPressState({
//     x: 0,
//     y: { listenCount: 0 },
//   });

//   const isDark = colorMode === "dark";

//   const value = useDerivedValue(() => {
//     return `$${state.y.listenCount.value.value}`;
//   }, [state]);

//   const textYPosition = useDerivedValue(() => {
//     return state.y.listenCount.position.value - 15;
//   }, [value]);

//   const textXPosition = useDerivedValue(() => {
//     // if (!toolTipFont) {
//     return 0;
//     // }
//     // return state.x.position.value - toolTipFont.measureText(value.value).width / 2;
//   }, [value]);

//   return (
//     <Box $dark-bg="$black" $light-bg="$white" flex={1} paddingHorizontal={5} paddingVertical={30}>
//       <Box className="h-[80%] w-[100%] pt-[90%]">
//         <CartesianChart
//           xKey="month"
//           padding={5}
//           yKeys={["listenCount"]}
//           domain={{ y: [0, 100] }}
//           domainPadding={{ left: 50, right: 50, top: 30 }}
//           axisOptions={{
//             tickCount: 5,
//             formatXLabel: (value) => {
//               const date = new Date(2023, value - 1);
//               return date.toLocaleString("default", { month: "short" });
//             },
//             lineColor: isDark ? "#71717a" : "#d4d4d8",
//             labelColor: isDark ? "white" : "black",
//           }}
//           chartPressState={state}
//           data={data}
//         >
//           {({ points, chartBounds }) => {
//             return (
//               <>
//                 <Bar
//                   points={points.listenCount}
//                   chartBounds={chartBounds}
//                   animate={{ type: "timing", duration: 1000, delay: 2000 }}
//                   barWidth={20}
//                   roundedCorners={{
//                     topLeft: 10,
//                     topRight: 10,
//                   }}
//                 >
//                   <LinearGradient start={vec(0, 0)} end={vec(0, 400)} colors={["green", "#90ee9050"]} />
//                 </Bar>

//                 {isActive ? (
//                   <>
//                     <SKText color={isDark ? "white" : "black"} x={textXPosition} y={textYPosition} text={value} />
//                     <Circle
//                       cx={state.x.position}
//                       cy={state.y.listenCount.position}
//                       r={8}
//                       color={"grey"}
//                       opacity={0.8}
//                     />
//                   </>
//                 ) : null}
//               </>
//             );
//           }}
//         </CartesianChart>
//       </Box>
//       <Box paddingTop={30} width="95%" height="20%" alignItems="center">
//         <Button
//           onPress={() => {
//             setData(DATA(5));
//           }}
//         >
//           <Text>Update Chart</Text>
//         </Button>
//       </Box>
//     </Box>
//   );
// };

// export default BarChart;

import {
  Bar,
  BarGroup,
  CartesianChart,
  type ChartBounds,
  Line,
  type PointsArray,
  useAnimatedPath,
  useBarPath,
  useChartPressState,
} from "victory-native";
import { LinearGradient, useFont, vec } from "@shopify/react-native-skia";
import { Box } from "../ui";
import { Path } from "@shopify/react-native-skia";

const data = Array.from({ length: 6 }, (_, index) => ({
  // Starting at 1 for Jaunary
  month: index + 1,
  // Randomizing the listen count between 100 and 50
  listenCount: Math.floor(Math.random() * (100 - 50 + 1)) + 50,
}));

function MyCustomBars({
  points,
  chartBounds,
  innerPadding,
}: {
  points: PointsArray;
  chartBounds: ChartBounds;
  innerPadding?: number;
}) {
  // 👇 use the hook to generate a path object.
  const { path } = useBarPath(points, chartBounds, innerPadding);
  const animPath = useAnimatedPath(path);

  return <Path path={animPath} style="fill" color="red" />;
}

export default function Chart() {
  const { state, isActive } = useChartPressState({ x: 0, y: { highTmp: 0 } });

  return (
    <Box className="h-[100px]">
      <CartesianChart
        data={data}
        xKey="month"
        yKeys={["listenCount"]}
        domainPadding={{ left: 50, right: 50, top: 100 }}
        axisOptions={{
          lineWidth: 0,
          formatXLabel(value) {
            const date = new Date(2023, value - 1);
            return date.toLocaleString("default", { month: "short" });
          },
        }}
      >
        {({ points, chartBounds }) => (
          <Bar
            chartBounds={chartBounds}
            points={points.listenCount}
            barWidth={20}
            roundedCorners={{
              topLeft: 5,
              topRight: 5,
            }}
          >
            {/* <LinearGradient start={vec(0, 10)} end={vec(0, 100)} colors={["#000", "transparent"]} /> */}
            <LinearGradient start={vec(0, 50)} end={vec(0, 100)} colors={["green", "transparent"]} />
          </Bar>
        )}
      </CartesianChart>
    </Box>
  );
}
