import { Link, type Href } from "expo-router";
import { Box, Center, Text } from "../ui";
import { Easing, FadeOut } from "react-native-reanimated";
import Reanimated, {
  useAnimatedStyle,
  withTiming,
  withSpring,
  useSharedValue,
  Keyframe,
} from "react-native-reanimated";
import { useEffect } from "react";
import type { FC, SVGProps } from "react";
import { cx } from "@/utils";

export type SectionItemProps = {
  isActive?: boolean;
  title?: string;
  href: Href;
  icon?: FC<SVGProps<SVGSVGElement>>;
  activeIcon?: FC<SVGProps<SVGSVGElement>>;
  disabled?: boolean;
};

export type MenuSectionProps = {
  id: string;
  title?: string;
  items: SectionItemProps[];
  isCollapsed?: boolean;
};

const MenuItem = ({ item, isCollapsed, title }: { item: SectionItemProps; isCollapsed: boolean; title: string }) => {
  const opacity = useSharedValue(0);
  const width = useSharedValue(0);

  useEffect(() => {
    if (item.isActive) {
      opacity.value = 1;
      width.value = isCollapsed ? 46 : 220;
    } else {
      opacity.value = 0;
      width.value = 0;
    }
    return undefined;
  }, [width, opacity, item.isActive, isCollapsed]);

  return (
    <Link
      href={item.href}
      key={`menu-${title}-${item.title}`}
      className={cx({
        "pointer-events-none": item.isActive || item.disabled,
      })}
    >
      <Box className={cx("absolute transition-all duration-300 ease-in-out left-0 w-1 opacity-0 bg-primary h-[80%] top-[10%] rounded-r-xs", {
        "opacity-100": item.isActive,
      })} />
      <Box
        className={cx("my-0.5 mx-4 rounded-lg border border-[rgba(255,255,255,0)] px-3 py-2.5", {
          "bg-background-darker/60": item.isActive,
          "opacity-50": item.disabled,
        })}
      >
        <Box className="flex flex-row items-center gap-1 text-text-secondary">
          <Center className="mr-1 h-[16px] w-[16px]">
            {item.isActive && item.activeIcon ? (
              <item.activeIcon className="select-none text-primary" />
            ) : item.icon ? (
              <item.icon className={cx("select-none text-text-tertiary", {
                "opacity-70": item.disabled,
              })} />
            ) : null}
          </Center>
          <Reanimated.View
            style={[
              {
                opacity: withTiming(isCollapsed ? 0 : 1, {
                  duration: 300,
                  easing: Easing.bezier(0.4, 0, 0.2, 1),
                }),
                width: withSpring(isCollapsed ? 0 : 160, {
                  damping: 15,
                  stiffness: 100,
                }),
              },
              {
                overflow: "hidden",
              },
            ]}
          >
            <Text
              className={cx("select-none whitespace-nowrap font-medium text-sm text-text-tertiary", {
                "text-text": item.isActive,
                "opacity-70": item.disabled,
              })}
            >
              {item.title}
            </Text>
          </Reanimated.View>
        </Box>
      </Box>
    </Link>
  );
};

const MenuSection = ({ title, items, isCollapsed = false }: MenuSectionProps) => {
  const opacity = useSharedValue(0);
  const height = useSharedValue(0);

  const animatedStyles = useAnimatedStyle(() => {
    return {
      opacity: withTiming(opacity.value, {
        duration: 300,
        easing: Easing.bezier(0.4, 0, 0.2, 1),
      }),
      height: withSpring(height.value, {
        damping: 15,
        stiffness: 100,
      }),
    };
  });

  useEffect(() => {
    if (!!title && !isCollapsed) {
      opacity.value = 1;
      height.value = 24;
    } else {
      opacity.value = 0;
      height.value = 0;
    }
    return undefined;
  }, [title, isCollapsed, opacity, height]);

  return (
    <Box className="mt-1 flex flex-col">
      <Reanimated.View
        style={[animatedStyles, { overflow: "hidden", marginBottom: 6, flexDirection: "row", alignItems: "center" }]}
      >
        <Text className="mt-3 ml-7 select-none text-2xs text-text-quaternary uppercase">{title}</Text>
      </Reanimated.View>
      {items.map((item) => (
        <MenuItem key={`menu-${title || ""}-${item.title}`} item={item} isCollapsed={isCollapsed} title={title || ""} />
      ))}
    </Box>
  );
};

export default MenuSection;
