import SidebarLeftIcon from "@/assets/icons/sidebar-left.svg";
import SidebarRightIcon from "@/assets/icons/sidebar-right.svg";
import ImoblrSymbolLightBackground from "@/assets/logos/imoblr-symbol.svg";
import ImoblrWordingLightBackground from "@/assets/logos/imoblr-wording.svg";
import ImoblrNewLogo from "@/assets/logos/imoblr-violet-new.svg";
import { useEffect, useState } from "react";
import { Pressable, useWindowDimensions } from "react-native";
import Reanimated, { Easing, useSharedValue, withTiming } from "react-native-reanimated";
import TrialPeriodIndicator from "../TrialPeriodIndicator";
import { Box, Center, Icon, Text } from "../ui";
import AccountMenu from "./AccountMenu";
import SidebarMenu from "./SidebarMenu";

export const CollapsedSidebarWidth = 64;
export const ExpandedSidebarWidth = 240;
export const SidebarPadding = 8;

const SidebarToggleButton = ({ isExpanded = false, onPress }: { isExpanded?: boolean | null; onPress: () => void }) => {
  return (
    <Box className="-right-5 absolute top-24 z-50 hidden xl:flex">
      <Pressable className={"transition-all duration-500 ease-in-out"} onPress={onPress}>
        <Center className="h-[24px] w-[24px] overflow-hidden rounded-lg border border-border bg-background shadow-xs hover:bg-primary-25">
          <Box className={"absolute h-[24px] w-[24px] items-center justify-center bg-background"}>
            {isExpanded ? (
              <Icon as={SidebarLeftIcon} className="h-[14px] w-[14px] text-text-secondary" />
            ) : (
              <Icon as={SidebarRightIcon} className="h-[14px] w-[14px] text-text-secondary" />
            )}
          </Box>
        </Center>
      </Pressable>
    </Box>
  );
};

const AppSidebar = () => {
  const [isExpanded, setIsExpanded] = useState<boolean | null>(null);
  const { width: windowWidth } = useWindowDimensions();
  const outerSidebarWidth = useSharedValue<number>(isExpanded ? ExpandedSidebarWidth : CollapsedSidebarWidth);
  const innerSidebarWidth = useSharedValue<number>(isExpanded ? ExpandedSidebarWidth : CollapsedSidebarWidth);

  useEffect(() => {
    if (windowWidth >= 1280) {
      setIsExpanded(true);
    } else if (windowWidth < 1280) {
      setIsExpanded(false);
    }
  }, [windowWidth]);

  useEffect(() => {
    outerSidebarWidth.set(
      withTiming(isExpanded ? ExpandedSidebarWidth : CollapsedSidebarWidth, {
        duration: 150,
        easing: Easing.linear,
      }),
    );

    innerSidebarWidth.set(
      withTiming(isExpanded ? ExpandedSidebarWidth : CollapsedSidebarWidth, {
        duration: 150,
        easing: Easing.linear,
      }),
    );
  }, [isExpanded, outerSidebarWidth, innerSidebarWidth]);

  const toggleIsPinned = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <Reanimated.View
      style={{
        width: outerSidebarWidth,
        zIndex: 50,
        height: "100vh",
      }}
    >
      <Box className="h-full w-full bg-background border-r border-border-light">
        {windowWidth >= 1280 && <SidebarToggleButton isExpanded={isExpanded} onPress={toggleIsPinned} />}
        {isExpanded && (
          <Box className="z-50 border-b border-border-light">
            <AccountMenu />
          </Box>
        )}
        <SidebarMenu isCollapsed={!isExpanded} />
        <TrialPeriodIndicator isCollapsed={!isExpanded} />
        <Box className="flex flex-row items-center gap-3 px-8 py-2 text-text-secondary">
          <Text className="text-text-quaternary text-xs">Tecnologia</Text>
          <Center className="h-[36px] w-[36px]">
            <Icon as={ImoblrSymbolLightBackground} className="h-[100%] w-[100%] select-none" />
          </Center>
          {isExpanded && (
            <Box className="overflow-hidden">
              <ImoblrWordingLightBackground className="h-[14px] w-[48px]" />
            </Box>
          )}
        </Box>
      </Box>
    </Reanimated.View>
  );
};

export default AppSidebar;
