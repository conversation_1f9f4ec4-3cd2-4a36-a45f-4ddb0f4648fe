import { Box } from "../ui";
import MenuSection, { type MenuSectionProps } from "./MenuSection";
import { usePathname } from "expo-router";
import HomeIcon from "@/assets/icons/dashboard-speed-01.svg";
import ActiveHomeIcon from "@/assets/icons/solid/dashboard-speed-01.svg";
import BuildingIcon from "@/assets/icons/building-05.svg";
import ActiveBuildingIcon from "@/assets/icons/solid/building-05.svg";
import CustomersIcon from "@/assets/icons/user-multiple-02.svg";
import ActiveCustomersIcon from "@/assets/icons/solid/user-multiple-02.svg";
import CalendarIcon from "@/assets/icons/calendar-01.svg";
import ActiveCalendarIcon from "@/assets/icons/solid/calendar-01.svg";
import BlockchainIcon from "@/assets/icons/block-game.svg";
import BrowserIcon from "@/assets/icons/globe-02.svg";

import HelpIcon from '@platform/assets/icons/help-circle.svg'

const SidebarMenu = ({ isCollapsed }: { isCollapsed?: boolean }) => {
  const pathname = usePathname();

  const basePath = pathname.split("/")[1];
  const basePathMatches = (pathname: string) => pathname === basePath;

  const menuSections = [
    {
      id: "dashboard",
      title: undefined,
      items: [
        {
          isActive: pathname === "/" || pathname === "/onboarding",
          title: "Visão geral",
          href: {
            pathname: "/",
          },
          icon: HomeIcon,
          activeIcon: ActiveHomeIcon,
        },
      ],
    },
    {
      id: "main",
      title: "Menu principal",
      items: [
        {
          isActive: basePathMatches("imoveis"),
          title: "Imóveis",
          href: {
            pathname: "/imoveis",
          },
          icon: BuildingIcon,
          activeIcon: ActiveBuildingIcon,
        },
        {
          isActive: basePathMatches("clientes"),
          title: "Clientes e leads",
          href: {
            pathname: "/clientes",
          },
          icon: CustomersIcon,
          activeIcon: ActiveCustomersIcon,
          disabled: true,
        },
        {
          isActive: basePathMatches("agenda"),
          title: "Agendamentos",
          href: {
            pathname: "/agenda",
          },
          icon: CalendarIcon,
          activeIcon: ActiveCalendarIcon,
          disabled: true,
        },
      ],
    },
    {
      id: "marketing",
      title: "Marketing",
      items: [
        {
          isActive: basePathMatches("canais"),
          title: "Canais e Integrações",
          href: {
            pathname: "/canais",
          },
          icon: BlockchainIcon,
          // activeIcon: ActiveBlockchainIcon,
          disabled: true,
        },
        {
          isActive: basePathMatches("websites"),
          title: "Website",
          href: {
            pathname: "/websites",
          },
          icon: BrowserIcon,
          // activeIcon: ActiveBrowserIcon,
        },
      ],
    },
  ];

  const secondaryMenuSection = [
    {
      id: "secondary",
      title: undefined,
      items: [
        {
          isActive: basePathMatches("ajuda"),
          title: "Ajuda",
          href: {
            pathname: "#",
          },
          icon: HelpIcon,
          activeIcon: HelpIcon,
          disabled: true,
        },
        {
          isActive: basePathMatches("configuracoes"),
          title: "Configurações",
          href: {
            pathname: "/configuracoes",
          },
          icon: HomeIcon,
          activeIcon: ActiveHomeIcon,
        },
      ],
    },
  ];

  return (
    <>
      <Box className="mt-4 flex-1">
        {menuSections.map((section) => (
          <MenuSection
            isCollapsed={isCollapsed}
            key={`sidebar-menu-section-${section.id}`}
            {...(section as MenuSectionProps)}
          />
        ))}
      </Box>
      {secondaryMenuSection.map((section) => (
        <Box className="mb-4">
          <MenuSection
            isCollapsed={isCollapsed}
            key={`sidebar-menu-section-${section.id}`}
            {...(section as MenuSectionProps)}
          />
        </Box>
      ))}
    </>
  );
};

export default SidebarMenu;
