import { Box, Form } from "@/components/ui";
import Drawer from "../Drawer";
import { useForm } from "react-hook-form";
import { type FormFields, resolver, defaultValues } from "./form";
import AddressForm from "./AddressForm";
import PropertyStructureForm from "./PropertyStructureForm";
import PropertyCharacteristicsForm from "./PropertyCharacteristicsForm";
import PropertyPricingForm from "./PropertyPricingForm";
import PropertyContentForm from "./PropertyContentForm";
import { useState } from "react";
// import { DevTool } from "@hookform/devtools";
import { useTeamAccount } from "@/hooks";
import { useCreateProperty, useUploadPropertyMedia } from "@/hooks/mutations";
import PropertyCreationStatus from "./PropertyCreationStatus";
import PropertyCreationSuccess from "./PropertyCreationSuccess";

export const CreateNewProperty = ({
  mode = "default",
  isOpen = false,
  onClose,
}: {
  mode?: "default" | "drawer";
  isOpen?: boolean;
  onClose?: () => void;
}) => {
  const [currentFormStep, setCurrentFormStep] = useState(1);
  const [isPropertyCreating, setIsPropertyCreating] = useState(false);
  const [hasActiveAddressSearch, setHasActiveAddressSearch] = useState(false);
  const [propertyCreationState, setPropertyCreationState] = useState<"idle" | "loading" | "success" | "error">("idle");
  const [mediaUploadState, setMediaUploadState] = useState<"idle" | "loading" | "success" | "error">("idle");
  const [uploadedMediaCount, setUploadedMediaCount] = useState(0);
  const [newPropertyId, setNewPropertyId] = useState<string | null>(null);
  const createNewPropertyForm = useForm<FormFields>({
    resolver,
    defaultValues,
    shouldUnregister: false,
  });
  createNewPropertyForm.register("address_id");
  createNewPropertyForm.register("google_maps_place_id");
  createNewPropertyForm.register("property_amenities");
  createNewPropertyForm.register("building_amenities");
  createNewPropertyForm.register("address_complement");

  const mediaCount = createNewPropertyForm.watch("media")?.length || 0;

  const {
    data: currentTeam,
    isLoading: isCurrentTeamAccountLoading,
    error: currentTeamAccountError,
  } = useTeamAccount();

  const createProperty = useCreateProperty();
  const uploadPropertyMedia = useUploadPropertyMedia();

  const onSubmit = createNewPropertyForm.handleSubmit(async (data) => {
    if (!currentTeam?.id) {
      alert("No team selected");
      return;
    }

    try {
      setCurrentFormStep(6);
      setIsPropertyCreating(true);
      setPropertyCreationState("loading");
      await createProperty
        .mutateAsync(
          {
            ...data,
            team_account_id: currentTeam.id,
          },
          {
            onMutate: (vars) => {
              console.log("Mutation payload:", JSON.stringify(vars, null, 2));
              return vars;
            },
            onError: (error) => {
              console.error("Creation error:", error);
              setTimeout(() => {
                setPropertyCreationState("error");
              }, 1500);
            },
            onSuccess: () => {
              console.log("Property created successfully");
              setTimeout(() => {
                setPropertyCreationState("success");
              }, 1500);
            },
          },
        )
        .catch((error) => {
          console.error("Creation error:", error);
          alert("Failed property db creation mutation", error);
        })
        .then(async (response) => {
          const propertyId = response?.property?.id;
          setNewPropertyId(propertyId?.toString() || null);

          // Get media files from form data
          const mediaFiles = data.media || [];

          if (mediaFiles.length > 0) {
            try {
              setMediaUploadState("loading");
              setUploadedMediaCount(0);

              // Since the hook doesn't support progress callbacks directly,
              // we'll implement our own tracking
              const totalFiles = mediaFiles.length;
              let completedFiles = 0;

              // Process files one by one and track progress
              const processedResults = [];

              for (const mediaFile of mediaFiles) {
                // Upload one file at a time
                const result = await uploadPropertyMedia.mutateAsync({
                  propertyId: propertyId.toString(),
                  mediaFiles: [mediaFile],
                });

                // Increment the counter after each successful upload
                completedFiles++;
                setUploadedMediaCount(completedFiles);

                // Store the result
                processedResults.push(result);
              }

              console.log("Media upload completed:", processedResults);
              setMediaUploadState("success");
              // Final count should already be correct, but set it just to be sure
              setUploadedMediaCount(mediaFiles.length);
              setTimeout(() => {
                setCurrentFormStep(7);
              }, 1500);
            } catch (error) {
              console.error("Error uploading property media:", error);
              setMediaUploadState("error");
              throw error; // Propagate the error to be handled by the outer catch block
            }
          }
        });
    } catch (error) {
      console.error("Error creating property:", error);
      setPropertyCreationState("error");
      setMediaUploadState("error");
      alert("Failed to create property. Please try again.");
    }
  });

  const touchedFields = createNewPropertyForm.formState.touchedFields;
  const dirtyFields = createNewPropertyForm.formState.dirtyFields;

  const formProgress: Record<number, Array<{ isComplete: boolean; percentage: number }>> = {
    1: [
      { isComplete: true, percentage: 10 },
      { isComplete: hasActiveAddressSearch || !!touchedFields.address_id, percentage: 40 },
      { isComplete: !!touchedFields.address_id, percentage: 50 },
      { isComplete: currentFormStep > 1, percentage: 0 },
    ],
    2: [
      { isComplete: true, percentage: 10 },
      { isComplete: !!dirtyFields.type, percentage: 40 },
      { isComplete: !!dirtyFields.purpose, percentage: 40 },
      { isComplete: currentFormStep > 2, percentage: 10 },
    ],
    3: [
      { isComplete: true, percentage: 60 },
      { isComplete: currentFormStep > 3, percentage: 40 },
    ],
    4: [
      { isComplete: true, percentage: 60 },
      { isComplete: currentFormStep > 4, percentage: 40 },
    ],
    5: [
      { isComplete: true, percentage: 60 },
      { isComplete: isPropertyCreating, percentage: 40 },
    ],
  };

  return (
    <>
      {mode === "drawer" ? (
        <>
          {/* DevTool temporarily removed to fix control._updateValid error */}
          {isOpen && (
            <Drawer
              title="Complete seu cadastro"
              isOpen={true}
              onClose={onClose}
              currentStep={currentFormStep}
              progress={formProgress}
            >
              <>
                {/* Regular form steps */}
                <Form {...createNewPropertyForm}>
                  <Box className="">
                    {currentFormStep === 1 && (
                      <AddressForm
                        onSubmit={() => setCurrentFormStep(2)}
                        onCancel={() => onClose?.()}
                        onAddressSearchStart={() => setHasActiveAddressSearch(true)}
                        onAddressSearchEnd={() => setHasActiveAddressSearch(false)}
                      />
                    )}
                    {currentFormStep === 2 && (
                      <PropertyPricingForm
                        onCancel={() => onClose?.()}
                        onBack={() => setCurrentFormStep(1)}
                        onSubmit={() => setCurrentFormStep(3)}
                      />
                    )}
                    {currentFormStep === 3 && (
                      <PropertyStructureForm
                        onBack={() => setCurrentFormStep(2)}
                        onCancel={() => onClose?.()}
                        onSubmit={() => setCurrentFormStep(4)}
                      />
                    )}
                    {currentFormStep === 4 && (
                      <PropertyCharacteristicsForm
                        onBack={() => setCurrentFormStep(3)}
                        onCancel={() => onClose?.()}
                        onSubmit={() => setCurrentFormStep(5)}
                      />
                    )}
                    {currentFormStep === 5 && !isPropertyCreating && (
                      <PropertyContentForm
                        onCancel={() => onClose?.()}
                        onBack={() => setCurrentFormStep(4)}
                        onSubmit={onSubmit}
                      />
                    )}

                    {/* Step 6: Loading Screen - Property Creation */}
                    {currentFormStep === 6 && (
                      <PropertyCreationStatus
                        propertyCreationState={propertyCreationState}
                        mediaUploadState={mediaUploadState}
                        mediaCount={mediaCount}
                        uploadedMediaCount={uploadedMediaCount}
                      />
                    )}

                    {/* Step 7: Success Screen */}
                    {currentFormStep === 7 && (
                      <PropertyCreationSuccess newPropertyId={newPropertyId} onClose={onClose} />
                    )}
                  </Box>
                </Form>
              </>
            </Drawer>
          )}
        </>
      ) : (
        <Form {...createNewPropertyForm}>
          <AddressForm />
        </Form>
      )}
    </>
  );
};

export default CreateNewProperty;
