import ArrowRightIcon from "@/assets/icons/arrow-right.svg";
import BlockchainIcon from "@/assets/icons/bulk/blockchain.svg";
import ChartRelationshipIcon from "@/assets/icons/bulk/chart-relationship.svg";
import ConnectIcon from "@/assets/icons/bulk/connect.svg";
import BrowserIcon from "@/assets/icons/duotone/browser.svg";
import CheckmarkIcon from "@/assets/icons/tick.svg";
import VivaRealSymbol from "@/assets/logos/vivareal-symbol.svg";
import { DrawerFooter } from "@/components/Drawer";
import DrawerHeader from "@/components/Drawer/DrawerHeader";
import {
  Box,
  Button,
  Center,
  FormField,
  FormInput,
  FormTextarea,
  Heading,
  LabelSeparator,
  Switch,
  Text,
} from "@/components/ui";
import { PROPERTY_TYPE_VALUE_LABEL_MAP } from "@/constants";
import { usePartialFormValidation } from "@/hooks";
import type { PropertyType } from "@/types";
import * as FileSystem from "expo-file-system";
import * as ImagePicker from "expo-image-picker";
import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { Pressable } from "react-native";

const checkFileSize = async (fileURI: string) => {
  if (!fileURI) return;

  const fileSizeInBytes = await FileSystem.getInfoAsync(fileURI);
  return fileSizeInBytes;
};

const PropertyChannelsForm = ({
  onSubmit,
  onBack,
  onCancel,
}: {
  onSubmit: () => void;
  onBack?: () => void;
  onCancel?: () => void;
}) => {
  const { control, resetField, setValue, watch, trigger } = useFormContext();
  const [mediaToUpload, setMediaToUpload] = useState<ImagePicker.ImagePickerAsset[]>([]);

  const title = watch("title");
  const propertyType = (watch("") || "apartment") as PropertyType;
  const builtArea = watch("built_area");
  const totalArea = watch("total_area");
  const neighborhood = watch("address.neighborhood");

  const { isValid } = usePartialFormValidation({
    name: ["purpose", "type"],
    control,
  });

  const pickImage = async () => {
    // No permissions request is necessary for launching the image library
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images", "videos"],
      allowsEditing: true,
      allowsMultipleSelection: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      for (const asset of result.assets) {
        const fileSize = asset.fileSize;

        if (fileSize && fileSize > 5 * 1024 * 1024) {
          alert("O tamanho do arquivo selecionado deve ser menor que 5MB");
          return;
        }
        setMediaToUpload([...mediaToUpload, asset]);
      }
    }
  };

  return (
    <Box className="pb-24">
      <DrawerHeader
        title="Canais e integrações"
        description="Adicione informações como título, descrição, fotos e vídeos."
        icon={ConnectIcon}
      />

      <Box className="mt-8 mb-8 gap-8">
        <Box className="px-8 pt-12">
          <Heading size="lg">Onde deseja publicar este imóvel?</Heading>
          <Text className="mt-2 pr-[25%] text-text-secondary text-xs">
            Selecione os canais onde deseja que este imóvel seja divulgado. (Você poderá alterar essas informações
            posteriormente)
          </Text>
        </Box>
        <LabelSeparator className="pl-4" label="Canais principais" labelPosition="left" />
        <Center className="mx-8 flex-row gap-6 rounded-xl border border-border-light p-2 pr-4 shadow-sm">
          <Center className="flex-1 flex-row justify-start">
            <Center className="mr-4 h-8 w-8 rounded-xl bg-primary-50">
              <BrowserIcon className="h-5 w-5 stroke-[2] text-text-primary" />
            </Center>
            <Text className="font-medium text-sm">Meu site</Text>
          </Center>
          <Button size="sm" variant="outline">
            <Text>Editar valores</Text>
          </Button>
          <Switch checked onCheckedChange={() => {}} />
        </Center>
        <LabelSeparator className="pl-4" label="Portais imobiliários" labelPosition="left" />
        <Center className="mx-8 flex-row gap-6 rounded-xl border border-border-light p-2 pr-4 shadow-sm">
          <Center className="flex-1 flex-row justify-start">
            <Center className="mr-4 h-8 w-8 rounded-xl bg-[#038DF7]">
              <VivaRealSymbol className="h-5 w-5" />
            </Center>
            <Text className="font-medium text-sm">VivaReal</Text>
          </Center>
          <Button size="sm" variant="outline">
            <Text>Editar valores</Text>
          </Button>
          <Switch checked onCheckedChange={() => {}} />
        </Center>
      </Box>
      <DrawerFooter
        primaryAction={{
          disabled: false,
          label: "Concluir e cadastrar imóvel",
          iconBefore: CheckmarkIcon,
          onPress: onSubmit,
        }}
        secondaryAction={{
          label: "Voltar",
          // iconBefore: ArrowLeftIcon,
          onPress: async () => {
            onBack?.();
          },
        }}
        tertiaryAction={{
          label: "Cancelar",
          // iconBefore: ArrowLeftIcon,
          onPress: async () => {
            onCancel?.();
          },
        }}
      />
    </Box>
  );
};

export default PropertyChannelsForm;
