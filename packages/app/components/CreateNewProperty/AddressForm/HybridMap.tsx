"use dom";

import { brand } from "@/theme";
import MapGL, { Marker } from "react-map-gl";

export interface HybridMapProps {
  showMarker: boolean;
  lat: number;
  lon: number;
}

const HybridMap = ({ lat, lon, showMarker }: HybridMapProps) => {
  return (
    <MapGL
      mapboxAccessToken="pk.eyJ1IjoiaW1vYmxyIiwiYSI6ImNsc3UzZmFkejI3OWQyaW4xbWx0ZHRvZ3IifQ.g6ymuW8QH1cPYXR5fpalYw"
      latitude={lat - 0.0001}
      longitude={lon}
      zoom={15}
      style={{ width: "100%", height: "100%", pointerEvents: "none" }}
      mapStyle="mapbox://styles/mapbox/streets-v12"
    >
      {showMarker && <Marker latitude={lat} longitude={lon} color={`rgb(${brand[500]})`} />}
    </MapGL>
  );
};

export default HybridMap;
