export const fetchPlaceDetails = async ({
  placeId,
  accessToken,
}: { placeId: string; accessToken: string | undefined }) => {
  if (!placeId || !accessToken) {
    console.error("Missing place ID or access token");
    return null;
  }

  try {
    const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/addresses/details`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({ placeId }),
    });

    if (!response.ok) {
      throw new Error(`Server responded with ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error fetching place details:", error);
    return null;
  }
};
