import { Box, FormCheck<PERSON>, <PERSON><PERSON>ield, LabelSeparator } from "@/components/ui";
import { BUILDING_AMENITIES } from "@/constants";
import type { BuildingAmenity, PropertyType } from "@/types";
import type { Control } from "react-hook-form";

interface BuildingAmenitiesProps {
  control: Control;
  propertyType: PropertyType;
}

type PropertyTypeBuildingAmenities = Record<PropertyType, BuildingAmenity[]>;

const buildingAmenitiesPerPropertyType: PropertyTypeBuildingAmenities = {
  // residential
  apartment: [
    "shared_water_heating",
    "shared_barbecue_grill",
    "shared_gourmet_space",
    "bicycle_storage",
    "intercom",
    "gym",
    "green_area",
    "playground",
    "shared_pool",
    "tennis_court",
    "sports_area",
    "party_room",
    "game_room",
    "storage",
    "shared_laundry",
    "elevator",
    "shared_garage",
    "power_generator",
    "reception",
    "shared_sauna",
    "shared_spa",
    "shared_security_system",
    "gated_community",
    "private_security",
  ],
  house: [],
  condominium_house: [
    "shared_barbecue_grill",
    "shared_gourmet_space",
    "bicycle_storage",
    "intercom",
    "gym",
    "green_area",
    "playground",
    "shared_pool",
    "tennis_court",
    "sports_area",
    "party_room",
    "game_room",
    "storage",
    "shared_laundry",
    "elevator",
    "shared_garage",
    "power_generator",
    "reception",
    "shared_sauna",
    "shared_spa",
    "shared_security_system",
    "gated_community",
    "private_security",
  ],
  penthouse: [
    "shared_water_heating",
    "shared_barbecue_grill",
    "shared_gourmet_space",
    "bicycle_storage",
    "intercom",
    "gym",
    "green_area",
    "playground",
    "shared_pool",
    "tennis_court",
    "sports_area",
    "party_room",
    "game_room",
    "storage",
    "shared_laundry",
    "elevator",
    "shared_garage",
    "power_generator",
    "reception",
    "shared_sauna",
    "shared_spa",
    "shared_security_system",
    "gated_community",
    "private_security",
  ],
  flat: [
    "shared_water_heating",
    "shared_barbecue_grill",
    "shared_gourmet_space",
    "bicycle_storage",
    "intercom",
    "gym",
    "green_area",
    "playground",
    "shared_pool",
    "tennis_court",
    "sports_area",
    "party_room",
    "game_room",
    "storage",
    "shared_laundry",
    "elevator",
    "shared_garage",
    "power_generator",
    "reception",
    "shared_sauna",
    "shared_spa",
    "shared_security_system",
    "gated_community",
    "private_security",
  ],
  studio: [
    "shared_water_heating",
    "shared_barbecue_grill",
    "shared_gourmet_space",
    "bicycle_storage",
    "intercom",
    "gym",
    "green_area",
    "playground",
    "shared_pool",
    "tennis_court",
    "sports_area",
    "party_room",
    "game_room",
    "storage",
    "shared_laundry",
    "elevator",
    "shared_garage",
    "power_generator",
    "reception",
    "shared_sauna",
    "shared_spa",
    "shared_security_system",
    "gated_community",
    "private_security",
  ],
  lot: [],
  townhouse: [],
  residential_building: [
    "shared_water_heating",
    "shared_barbecue_grill",
    "shared_gourmet_space",
    "bicycle_storage",
    "intercom",
    "gym",
    "green_area",
    "playground",
    "shared_pool",
    "tennis_court",
    "sports_area",
    "party_room",
    "game_room",
    "storage",
    "shared_laundry",
    "elevator",
    "shared_garage",
    "power_generator",
    "reception",
    "shared_sauna",
    "shared_spa",
    "shared_security_system",
    "gated_community",
    "private_security",
  ],
  rural_property: [],
  // commercial
  medical_office: [
    "shared_water_heating",
    "bicycle_storage",
    "intercom",
    "gym",
    "green_area",
    "playground",
    "storage",
    "elevator",
    "shared_garage",
    "power_generator",
    "reception",
    "shared_security_system",
    "private_security",
  ],
  warehouse: [],
  commercial_property: [
    "shared_water_heating",
    "bicycle_storage",
    "intercom",
    "gym",
    "green_area",
    "playground",
    "storage",
    "elevator",
    "shared_garage",
    "power_generator",
    "reception",
    "shared_security_system",
    "private_security",
  ],
  commercial_lot: [],
  store: [],
  office: [
    "shared_water_heating",
    "bicycle_storage",
    "intercom",
    "gym",
    "green_area",
    "playground",
    "storage",
    "elevator",
    "shared_garage",
    "power_generator",
    "reception",
    "shared_security_system",
    "private_security",
  ],
  commercial_building: [
    "shared_water_heating",
    "bicycle_storage",
    "intercom",
    "gym",
    "green_area",
    "playground",
    "storage",
    "elevator",
    "shared_garage",
    "power_generator",
    "reception",
    "shared_security_system",
    "private_security",
  ],
};

const BuildingAmenities = ({ control, propertyType }: BuildingAmenitiesProps) => {
  const filteredBuildingAmenities = BUILDING_AMENITIES.filter((amenity) => {
    return buildingAmenitiesPerPropertyType[propertyType].includes(amenity.value);
  });

  return (
    filteredBuildingAmenities.length > 0 && (
      <>
        <LabelSeparator className="my-8 pl-4" label="Comodidades do condomínio/prédio" labelPosition="left" />
        <Box className="-mb-6 flex-row flex-wrap pr-2 pl-8">
          {filteredBuildingAmenities.map((amenity, index) => (
            <Box className="mb-6 w-1/3 pr-6" key={`property-amenity-${amenity.value}-${index}`}>
              <FormField
                control={control}
                name={`building_amenities.${amenity.value}`}
                render={({ field }) => (
                  <FormCheckbox {...field} label={amenity.label} onChange={field.onChange} value={field.value} />
                )}
              />
            </Box>
          ))}
        </Box>
      </>
    )
  );
};

export default BuildingAmenities;
