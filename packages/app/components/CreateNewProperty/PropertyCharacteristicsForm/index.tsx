import { Box, FormField, FormSwitch, LabelSeparator } from "@/components/ui";
import { useFormContext } from "react-hook-form";
import { DrawerFooter } from "@/components/Drawer";
import ArrowRightIcon from "@/assets/icons/arrow-right.svg";
import { usePartialFormValidation } from "@/hooks";
import DrawerHeader from "@/components/Drawer/DrawerHeader";
import CheckListIcon from "@/assets/icons/duotone/check-list.svg";

import BuildingAmenities from "./BuildingAmenities";
import PropertyAmenities from "./PropertyAmenities";

const PropertyCharacteristicsForm = ({
  onSubmit,
  onBack,
  onCancel,
}: {
  onSubmit?: () => void;
  onBack?: () => void;
  onCancel?: () => void;
}) => {
  const { control, watch } = useFormContext();

  const propertyPurpose = watch("purpose");
  const propertyType = watch("type");

  const isNoFurnuturePropertyType = ["lot", "commercial_lot", "warehouse"].includes(propertyType);

  const { isValid } = usePartialFormValidation({
    name: ["purpose", "type"],
    control,
  });

  return (
    <Box className="pt-12 pb-24">
      <DrawerHeader
        title="Características, comodidades e outros"
        description="Informações gerais como tipo de imóvel, tamanho, etc."
        icon={CheckListIcon}
      />

      {(propertyPurpose === "residential" || !isNoFurnuturePropertyType) && (
        <LabelSeparator className="mb-4 pl-4" label="Características principais" labelPosition="left" />
      )}
      {!isNoFurnuturePropertyType && (
        <Box className="gap-8">
          <Box className="px-8">
            <FormField
              control={control}
              name="furnished"
              render={({ field }) => (
                <FormSwitch
                  className="my-2"
                  label={`Este imóvel é mobiliado? (${field.value ? "Sim" : "Não"})`}
                  {...field}
                />
              )}
            />
          </Box>
        </Box>
      )}

      {propertyPurpose === "residential" && (
        <Box className="gap-8">
          <Box className="px-8">
            <FormField
              control={control}
              name="pets_allowed"
              render={({ field }) => (
                <FormSwitch
                  className="my-2"
                  label={`Este imóvel aceita animais? (${field.value ? "Sim" : "Não"})`}
                  {...field}
                />
              )}
            />
          </Box>
        </Box>
      )}
      {propertyType && <PropertyAmenities control={control} propertyType={propertyType} />}
      {propertyType && <BuildingAmenities control={control} propertyType={propertyType} />}
      <DrawerFooter
        primaryAction={{
          disabled: !isValid,
          label: "Continuar",
          iconAfter: ArrowRightIcon,
          onPress: async () => {
            onSubmit?.();
          },
        }}
        secondaryAction={{
          label: "Voltar",
          // iconBefore: ArrowLeftIcon,
          onPress: async () => {
            onBack?.();
          },
        }}
        tertiaryAction={{
          label: "Cancelar",
          // iconBefore: ArrowLeftIcon,
          onPress: async () => {
            onCancel?.();
          },
        }}
      />
    </Box>
  );
};

export default PropertyCharacteristicsForm;
