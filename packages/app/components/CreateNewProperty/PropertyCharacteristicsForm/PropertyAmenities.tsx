import { Box, FormCheckbox, FormField, FormInput, FormSelect, LabelSeparator, SelectItem } from "@/components/ui";
import { PROPERTY_AMENITIES, PROPERTY_AMENITY_LABELS } from "@/constants";
import type { PropertyAmenity, PropertyType } from "@/types";
import { property } from "lodash-es";
import type { Control } from "react-hook-form";

interface PropertyAmenitiesProps {
  control: Control;
  propertyType: PropertyType;
}

type PropertyTypeAmenities = Record<PropertyType, PropertyAmenity[]>;

const propertyAmenitiesPerPropertyType: PropertyTypeAmenities = {
  // residential
  apartment: [
    "barbecue_grill",
    "gourmet_space",
    "garden",
    "pool",
    "heating",
    "water_heating",
    "air_conditioning",
    "internet",
    "fireplace",
    "laundry",

    "sauna",
    "spa",
    "security_system",
  ],
  house: [
    "barbecue_grill",
    "gourmet_space",
    "garden",
    "pool",
    "backyard",
    "heating",
    "water_heating",
    "air_conditioning",
    "internet",
    "garage",
    "fireplace",
    "laundry",

    "sauna",
    "spa",
    "security_system",
  ],
  condominium_house: [
    "barbecue_grill",
    "gourmet_space",
    "garden",
    "pool",
    "backyard",
    "heating",
    "water_heating",
    "air_conditioning",
    "internet",
    "garage",
    "fireplace",
    "laundry",

    "sauna",
    "spa",
    "security_system",
  ],
  penthouse: [
    "barbecue_grill",
    "gourmet_space",
    "garden",
    "pool",
    "backyard",
    "heating",
    "water_heating",
    "air_conditioning",
    "internet",
    "fireplace",
    "laundry",

    "sauna",
    "spa",
    "security_system",
  ],
  flat: [
    "heating",
    "water_heating",
    "air_conditioning",
    "internet",
    "fireplace",
    "laundry",

    "security_system",
  ],
  studio: ["heating", "water_heating", "air_conditioning", "internet", "laundry", "security_system"],
  lot: [],
  townhouse: [
    "barbecue_grill",
    "gourmet_space",
    "garden",
    "pool",
    "backyard",
    "heating",
    "water_heating",
    "air_conditioning",
    "internet",
    "garage",
    "fireplace",
    "laundry",

    "sauna",
    "spa",
    "security_system",
  ],
  residential_building: [
    "barbecue_grill",
    "gourmet_space",
    "garden",
    "pool",
    "backyard",
    "heating",
    "water_heating",
    "air_conditioning",
    "internet",
    "garage",
    "fireplace",
    "laundry",

    "sauna",
    "spa",
    "security_system",
  ],
  rural_property: [
    "barbecue_grill",
    "gourmet_space",
    "garden",
    "pool",
    "backyard",
    "heating",
    "water_heating",
    "air_conditioning",
    "internet",
    "garage",
    "fireplace",
    "laundry",

    "sauna",
    "spa",
    "security_system",
  ],
  // commercial
  medical_office: [
    "garden",
    "heating",
    "water_heating",
    "air_conditioning",
    "internet",
    "garage",

    "security_system",
  ],
  warehouse: ["heating", "water_heating", "air_conditioning", "internet", "garage", "security_system"],
  commercial_property: ["garage", "internet", "security_system"],
  commercial_lot: [],
  store: ["heating", "water_heating", "air_conditioning", "internet", "garage", "security_system"],
  office: ["heating", "water_heating", "air_conditioning", "internet", "security_system"],
  commercial_building: [
    "garden",
    "heating",
    "water_heating",
    "air_conditioning",
    "internet",
    "garage",
    "security_system",
  ],
};

const PropertyAmenities = ({ control, propertyType }: PropertyAmenitiesProps) => {
  const filteredPropertyAmenities = PROPERTY_AMENITIES.filter((amenity) => {
    return propertyAmenitiesPerPropertyType[propertyType].includes(amenity.value);
  });

  return (
    filteredPropertyAmenities.length > 0 && (
      <>
        <LabelSeparator className="mt-4 mb-8 pl-4" label="Comodidades do imóvel" labelPosition="left" />
        <Box className="-mb-6 flex-row flex-wrap pr-6 pl-8">
          {filteredPropertyAmenities.map((amenity, index) => (
            <Box className="mb-6 w-1/3 pr-1.5" key={`property-amenity-${amenity.value}-${index}`}>
              <FormField
                control={control}
                name={`property_amenities.${amenity.value}`}
                render={({ field }) => (
                  <FormCheckbox {...field} label={amenity.label} onChange={field.onChange} value={field.value} />
                )}
              />
            </Box>
          ))}
        </Box>
      </>
    )
  );
};

export default PropertyAmenities;
