import type { FC } from "react";
import { Box, Button, Heading, Text } from "@/components/ui";
import SuccessIllustration from "@/assets/illustrations/storyset/Building blocks-pana.svg";
import { router } from "expo-router";
import BuildingIcon from "@/assets/icons/building-06.svg";

interface PropertyCreationSuccessProps {
  newPropertyId: string | null;
  onClose?: () => void;
}

const PropertyCreationSuccess: FC<PropertyCreationSuccessProps> = ({ newPropertyId, onClose }) => {
  return (
    <Box className="h-[85vh] bg-background p-8">
      <Box className="items-center p-8">
        <SuccessIllustration className="h-60 w-60" />

        <Heading size="lg" className="mb-4 text-center">
          Seu imóvel foi criado!
        </Heading>
        <Text className="w-[60%] text-center text-sm text-text-tertiary">
          Cadastramos o seu imóvel com todos os dados, imagens e fotos informados.
        </Text>
      </Box>

      <Box className="mt-4 w-full items-center justify-center">
        <Box className="shadow-xl">
          <Button
            size="lg"
            onPress={() => {
              if (newPropertyId) {
                // Navigate to the property details screen
                router.push({
                  pathname: "/imoveis/[id]" as const,
                  params: { id: newPropertyId },
                });
                onClose?.();
              }
            }}
          >
            <BuildingIcon className="-ml-1 mr-2 h-5 w-5 text-text-inverse" />
            <Text>Ir para a página do imóvel</Text>
          </Button>
        </Box>
        <Button className="mt-2" variant="link" onPress={onClose}>
          <Text>Fechar este painel</Text>
        </Button>
      </Box>
    </Box>
  );
};

export default PropertyCreationSuccess;
