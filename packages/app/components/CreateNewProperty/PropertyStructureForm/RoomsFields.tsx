import { Box, FormField, FormInput, FormSelect, LabelSeparator, SelectItem } from "@/components/ui";
import type { PropertyType } from "@/types";
import type { Control } from "react-hook-form";

export type RoomsFields = "rooms_count" | "suites_count" | "bathrooms_count" | "parking_spots_count" | "office_rooms";

interface RoomsFieldsProps {
  control: Control;
  propertyType: PropertyType;
}

type PropertyTypeRoomsFields = Record<PropertyType, RoomsFields[]>;

const fieldsPerPropertyType: PropertyTypeRoomsFields = {
  // residential
  apartment: ["rooms_count", "suites_count", "bathrooms_count", "parking_spots_count"],
  house: ["rooms_count", "suites_count", "bathrooms_count", "parking_spots_count"],
  condominium_house: ["rooms_count", "suites_count", "bathrooms_count", "parking_spots_count"],
  penthouse: ["rooms_count", "suites_count", "bathrooms_count", "parking_spots_count"],
  flat: ["rooms_count", "suites_count", "bathrooms_count", "parking_spots_count"],
  studio: ["rooms_count", "suites_count", "bathrooms_count", "parking_spots_count"],
  lot: [],
  townhouse: ["rooms_count", "suites_count", "bathrooms_count", "parking_spots_count"],
  residential_building: ["rooms_count", "suites_count", "bathrooms_count", "parking_spots_count"],
  rural_property: ["rooms_count", "suites_count", "bathrooms_count", "parking_spots_count"],
  // commercial
  medical_office: ["office_rooms", "bathrooms_count", "parking_spots_count"],
  warehouse: ["bathrooms_count", "parking_spots_count"],
  commercial_property: ["office_rooms", "bathrooms_count", "parking_spots_count"],
  commercial_lot: [],
  store: ["bathrooms_count", "parking_spots_count"],
  office: ["office_rooms", "bathrooms_count", "parking_spots_count"],
  commercial_building: ["office_rooms", "bathrooms_count", "parking_spots_count"],
};

const RoomsFields = ({ control, propertyType }: RoomsFieldsProps) => {
  const roomsFields = {
    rooms_count: (
      <FormField
        control={control}
        name="rooms_count"
        render={({ field }) => <FormInput label="Quartos" placeholder="Qtd. de quartos" min={0} step={1} {...field} />}
      />
    ),
    suites_count: (
      <FormField
        control={control}
        name="suites_count"
        render={({ field }) => <FormInput label="Suites" placeholder="Qtd. de suites" min={0} step={1} {...field} />}
      />
    ),
    bathrooms_count: (
      <FormField
        control={control}
        name="bathrooms_count"
        render={({ field }) => (
          <FormInput label="Banheiros" placeholder="Qtd. de banheiros" min={0} step={1} {...field} />
        )}
      />
    ),
    office_rooms: (
      <FormField
        control={control}
        name="office_rooms"
        render={({ field }) => <FormInput label="Salas" placeholder="Qtd. de salas" min={0} step={1} {...field} />}
      />
    ),
    parking_spots_count: (
      <FormField
        control={control}
        name="parking_spots_count"
        render={({ field }) => (
          <FormInput label="Vagas de garagem" placeholder="Qtd. de vagas" min={0} step={1} {...field} />
        )}
      />
    ),
  };

  const propertyTypeRoomsFields = fieldsPerPropertyType[propertyType];

  return (
    propertyTypeRoomsFields.length > 0 && (
      <>
        <LabelSeparator className="pl-4" label="Disposição do imóvel" labelPosition="left" />
        <Box className="flex-row flex-wrap pr-2 pl-8">
          {propertyTypeRoomsFields.map((field, index) => (
            <Box className="mb-6 w-1/3 pr-6" key={`roomsFields-${field}`}>
              {roomsFields[field]}
            </Box>
          ))}
        </Box>
      </>
    )
  );
};

export default RoomsFields;
