import ArrowRightIcon from "@/assets/icons/arrow-right.svg";
import BoundingBoxIcon from "@/assets/icons/duotone/bounding-box.svg";
import { DrawerFooter } from "@/components/Drawer";
import DrawerHeader from "@/components/Drawer/DrawerHeader";
import { Box, FormField, FormSelect, LabelSeparator, SelectItem } from "@/components/ui";
import { COMMERCIAL_PROPERTY_TYPES, PROPERTY_TYPE_VALUE_LABEL_MAP, RESIDENTIAL_PROPERTY_TYPES } from "@/constants";
import { usePartialFormValidation } from "@/hooks";
import { omit } from "@/utils";
import { useFormContext } from "react-hook-form";
import RoomsFields from "./RoomsFields";
import SizeFields from "./SizeFields";

const purposeOptions = {
  residential: "Apenas residencial",
  commercial: "Apenas comercial",
  mixed: "Residencial e comercial",
};

const PropertyStructureForm = ({
  onSubmit,
  onBack,
  onCancel,
}: {
  onSubmit?: () => void;
  onBack?: () => void;
  onCancel?: () => void;
}) => {
  const { control, resetField, setValue, watch, trigger } = useFormContext();

  const propertyPurpose = watch("purpose");
  const propertyType = watch("type");

  const { isValid } = usePartialFormValidation({
    name: ["purpose", "type"],
    control,
  });

  return (
    <Box className="pt-12">
      <DrawerHeader
        title="Tipo, tamanho e disposição"
        description="Informações gerais como tipo de imóvel, tamanho, etc."
        icon={BoundingBoxIcon}
      />

      <LabelSeparator className="pl-4" label="Tipo do imóvel" labelPosition="left" />

      <Box className="my-8 gap-8">
        <Box className="flex-1 flex-row gap-6 px-8">
          <Box className="flex-1">
            <FormField
              control={control}
              name="purpose"
              render={({ field }) => (
                <FormSelect
                  label="Finalidade do imóvel"
                  placeholder="Selecione a finalidade"
                  onChange={(value) => {
                    field.onChange(value);
                    resetField("type");
                    resetField("building_amenities");
                    resetField("property_amenities");
                    // Manually trigger validation for the purpose field to clear errors
                    setTimeout(() => trigger("purpose"), 0);
                  }}
                  {...omit(field, ["onChange"])}
                >
                  {Object.entries(purposeOptions).map(([value, label]) => (
                    <SelectItem key={value} label={label} value={value} />
                  ))}
                </FormSelect>
              )}
            />
          </Box>
          <Box className="flex-1">
            <FormField
              control={control}
              name="type"
              render={({ field }) => (
                <FormSelect
                  label="Tipo de imóvel"
                  placeholder={!propertyPurpose ? "Selecione a finalidade primeiro" : "Selecione o tipo do imóvel"}
                  isDisabled={!propertyPurpose}
                  {...field}
                >
                  {propertyPurpose !== "commercial" &&
                    RESIDENTIAL_PROPERTY_TYPES.map((type) => (
                      <SelectItem key={type.value} label={type.label} value={type.value} />
                    ))}
                  {propertyPurpose !== "residential" &&
                    COMMERCIAL_PROPERTY_TYPES.map((type) => (
                      <SelectItem key={type.value} label={type.label} value={type.value} />
                    ))}
                </FormSelect>
              )}
            />
          </Box>
        </Box>
        {propertyType && (
          <>
            <LabelSeparator className="pl-4" label="Estrutura e dimensões" labelPosition="left" />
            <Box className="flex-row">
              {propertyType && <SizeFields control={control} propertyType={propertyType} />}
            </Box>
            {propertyType && <RoomsFields control={control} propertyType={propertyType} />}
          </>
        )}
      </Box>
      <DrawerFooter
        primaryAction={{
          disabled: !isValid,
          label: "Continuar",
          iconAfter: ArrowRightIcon,
          onPress: async () => {
            const output = await trigger(["type", "purpose"], {
              shouldFocus: true,
            });

            if (!output) return;

            onSubmit?.();
          },
        }}
        secondaryAction={{
          label: "Voltar",
          iconBefore: ArrowRightIcon,
          onPress: () => {
            onBack?.();
          },
        }}
        tertiaryAction={{
          label: "Cancelar",
          onPress: () => {
            onCancel?.();
          },
        }}
      />
    </Box>
  );
};

export default PropertyStructureForm;
