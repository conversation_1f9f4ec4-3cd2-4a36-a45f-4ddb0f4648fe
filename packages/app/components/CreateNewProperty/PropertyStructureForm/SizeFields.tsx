import { Box, FormField, FormInput, FormSelect, SelectItem } from "@/components/ui";
import type { PropertyType } from "@/types";
import type { Control } from "react-hook-form";

export type SizeFields = "built_area" | "total_area" | "floors_count" | "floor";

interface SizeFieldsProps {
  control: Control;
  propertyType: PropertyType;
}

type PropertyTypeFields = Record<PropertyType, SizeFields[]>;

const fieldsPerPropertyType: PropertyTypeFields = {
  // residential
  apartment: ["built_area", "floor"],
  house: ["built_area", "total_area", "floors_count"],
  condominium_house: ["built_area", "total_area", "floors_count"],
  penthouse: ["built_area", "total_area", "floor"],
  flat: ["built_area", "floor"],
  studio: ["built_area", "floor"],
  lot: ["total_area"],
  townhouse: ["built_area", "total_area", "floors_count"],
  residential_building: ["built_area", "total_area", "floors_count"],
  rural_property: ["built_area", "total_area", "floors_count"],
  // commercial
  medical_office: ["built_area", "floor"],
  warehouse: ["built_area", "total_area"],
  commercial_property: ["built_area", "total_area", "floors_count"],
  commercial_lot: ["total_area"],
  store: ["built_area", "floors_count"],
  office: ["built_area", "floor"],
  commercial_building: ["built_area", "total_area", "floors_count"],
};

const SizeFields = ({ control, propertyType }: SizeFieldsProps) => {
  const sizeFields = {
    built_area: (
      <FormField
        control={control}
        name="built_area"
        render={({ field }) => (
          <FormInput label="Área construida (m2)" placeholder="Área em m2" min={0} step={1} {...field} />
        )}
      />
    ),
    total_area: (
      <FormField
        control={control}
        name="total_area"
        render={({ field }) => (
          <FormInput label="Área total (m2)" placeholder="Área em m2" min={0} step={1} {...field} />
        )}
      />
    ),
    floor: (
      <FormField
        control={control}
        name="floor"
        render={({ field }) => (
          <FormSelect
            label="Andar"
            {...field}
            selectedValue={field?.value?.toString() || null}
            placeholder="Selecione o andar"
          >
            {Array.from({ length: 31 }, (_, i) => i).map((num) => (
              <SelectItem key={num} label={num === 0 ? "Térreo" : `${num.toString()}º andar`} value={num.toString()} />
            ))}
          </FormSelect>
        )}
      />
    ),
    floors_count: (
      <FormField
        control={control}
        name="floors_count"
        render={({ field }) => (
          <FormSelect label="Pavimentos" {...field} selectedValue={field.value.toString()} placeholder="Pavimentos">
            {Array.from({ length: 20 }, (_, i) => i + 1).map((num) => (
              <SelectItem key={num} label={num.toString()} value={num.toString()} />
            ))}
          </FormSelect>
        )}
      />
    ),
  };

  return (
    <Box className="-mb-6 flex-1 flex-row flex-wrap pr-2 pl-8">
      {fieldsPerPropertyType[propertyType].map((field, index) => (
        <Box className="mb-6 w-1/3 pr-6" key={`sizeFields-${field}`}>
          {sizeFields[field]}
        </Box>
      ))}
    </Box>
  );
};

export default SizeFields;
