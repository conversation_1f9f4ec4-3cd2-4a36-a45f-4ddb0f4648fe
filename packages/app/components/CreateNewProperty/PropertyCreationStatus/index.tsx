import { Box, Button, Heading, Text } from "@/components/ui";
import LoadingIllustration from "@/assets/illustrations/storyset/Loading-rafiki.svg";
import React from "react";
import { cx } from "@/utils";
import StatefulSpinner from "@/components/StatefulSpinner";
import AlertTriangleIcon from "@/assets/icons/alert-02.svg";
import RefreshCwIcon from "@/assets/icons/refresh.svg";

export interface PropertyCreationStatusProps {
  propertyCreationState?: "idle" | "loading" | "success" | "error";
  mediaUploadState?: "idle" | "loading" | "success" | "error";
  mediaCount?: number;
  uploadedMediaCount?: number;
  onRetryMediaUpload?: () => void;
}

export const PropertyCreationStatus = ({
  propertyCreationState = "idle",
  mediaUploadState = "idle",
  mediaCount = 0,
  uploadedMediaCount = 0,
  onRetryMediaUpload,
}: PropertyCreationStatusProps) => {
  return (
    <Box className="h-[85vh] bg-background p-8">
      <Box className="items-center p-8">
        <LoadingIllustration className="h-60 w-60" />

        <Heading size="lg" className="mb-4 text-center">
          Estamos criando seu imóvel
        </Heading>
        <Text className="w-[60%] text-center text-sm text-text-tertiary">
          Isso pode levar alguns instantes. Você pode acompanhar o progresso abaixo.
        </Text>
      </Box>
      <Box
        className={cx(
          "mb-4 w-full flex-row items-center gap-4 rounded border border-border-light bg-background-dark p-4",
          { "opacity-50": propertyCreationState === "success" },
        )}
      >
        <StatefulSpinner size={18} state={propertyCreationState} />
        <Text className="font-medium text-sm text-text">
          {propertyCreationState === "success"
            ? "Dados do imóvel cadastrados com sucesso"
            : "Cadastrando dados do imóvel"}
        </Text>
      </Box>
      {mediaCount > 0 && (
        <Box
          className={cx(
            "w-full flex-row items-center gap-4 rounded border border-border-light bg-background-dark p-4",
            {
              "opacity-50": mediaUploadState === "idle" || !mediaUploadState,
              "border-destructive border-opacity-50 bg-destructive bg-opacity-5": mediaUploadState === "error",
            },
          )}
        >
          {mediaUploadState === "error" ? (
            <AlertTriangleIcon size={18} color="#ef4444" />
          ) : (
            <StatefulSpinner size={18} state={mediaUploadState} />
          )}

          <Box className="flex-1 flex-row items-center justify-between">
            <Text className={cx("font-medium text-sm text-text", { "text-destructive": mediaUploadState === "error" })}>
              {mediaUploadState === "success"
                ? "Fotos/videos do imóvel enviadas com sucesso"
                : mediaUploadState === "error"
                  ? `Erro ao enviar fotos/videos (${uploadedMediaCount} de ${mediaCount})`
                  : `Enviando fotos/videos do imóvel (${uploadedMediaCount} de ${mediaCount})`}
            </Text>

            {mediaUploadState === "error" && onRetryMediaUpload && (
              <Button variant="outline" size="xs" onPress={onRetryMediaUpload} className="ml-2 flex-row items-center">
                <RefreshCwIcon size={14} className="mr-1 text-text" />
                <Text className="font-medium text-xs">Tentar novamente</Text>
              </Button>
            )}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default PropertyCreationStatus;
