import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { PROPERTY_TYPE_VALUES, PROPERTY_AMENITY_VALUES, BUILDING_AMENITY_VALUES } from "@/constants";
import type { PropertyType } from "@/types";
import type { PropertyAmenity, BuildingAmenity } from "@/types";

const requiredFieldsPerPropertyType = {
  apartment: ["rooms_count", "floor"],
  condominium_house: ["rooms_count", "floor"],
  flat: ["rooms_count", "floor"],
  house: ["rooms_count"],
  lot: ["rooms_count"],
  medical_office: ["rooms_count"],
  office: ["rooms_count"],
};

export const schema = z.object({
  title: z.string().min(6, {
    message: "O título deve conter ao menos 6 caracteres.",
  }),
  description: z
    .string()
    // .min(20, {
    //   message: "A descrição deve conter ao menos 20 caracteres.",
    // })
    .optional(),
  media: z.array(z.any()).optional(),
  purpose: z.enum(["residential", "commercial", "mixed"]),
  type: z.enum(PROPERTY_TYPE_VALUES as [PropertyType, ...PropertyType[]]),
  built_area: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined) return undefined;
    const num = Number(val);
    return Number.isNaN(num) ? undefined : num;
  }, z.number().optional()),
  total_area: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined) return undefined;
    const num = Number(val);
    return Number.isNaN(num) ? undefined : num;
  }, z.number().optional()),
  floors_count: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined) return undefined;
    const num = Number(val);
    return Number.isNaN(num) ? undefined : num;
  }, z.number().optional()),
  floor: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined) return undefined;
    const num = Number(val);
    return Number.isNaN(num) ? undefined : num;
  }, z.number().optional()),
  rooms_count: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined) return undefined;
    const num = Number(val);
    return Number.isNaN(num) ? undefined : num;
  }, z.number().optional()),
  bathrooms_count: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined) return undefined;
    const num = Number(val);
    return Number.isNaN(num) ? undefined : num;
  }, z.number().optional()),
  parking_spots_count: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined) return undefined;
    const num = Number(val);
    return Number.isNaN(num) ? undefined : num;
  }, z.number().optional()),
  suites_count: z.preprocess((val) => {
    if (val === "" || val === null || val === undefined) return undefined;
    const num = Number(val);
    return Number.isNaN(num) ? undefined : num;
  }, z.number().optional()),
  pets_allowed: z.boolean().default(false),
  available_for_rent: z.boolean().default(true),
  available_for_sale: z.boolean().default(true),
  available_for_bnb: z.boolean().default(true),
  sale_price: z.string().optional(),
  rent_price: z.string().optional(),
  furnished: z.boolean().default(false),
  condominium_monthly_tax: z.string().optional(),
  iptu_monthly_tax: z.string().optional(),
  insurance_monthly_tax: z.string().optional(),
  other_monthly_tax: z.string().optional(),
  property_amenities: z
    .object(Object.fromEntries(PROPERTY_AMENITY_VALUES.map((key) => [key, z.boolean().optional().default(false)])))
    .optional(),
  building_amenities: z
    .object(Object.fromEntries(BUILDING_AMENITY_VALUES.map((key) => [key, z.boolean().optional().default(false)])))
    .optional(),
  address_id: z.string(),
  google_maps_place_id: z.string().optional(),
  address_complement: z.string().optional(),
});

export const defaultValues = {
  condominium_monthly_tax: "0",
  iptu_monthly_tax: "0",
  insurance_monthly_tax: "0",
  other_monthly_tax: "0",
  available_for_sale: true,
  available_for_rent: true,
  pets_allowed: false,
  title: "",
  description: "",
  media: [],
  floors_count: 1,
  rooms_count: 1,
  bathrooms_count: 0,
  parking_spots_count: 0,
  suites_count: 0,
  address_id: undefined,
  google_maps_place_id: undefined,
  address_complement: undefined,
  built_area: undefined,
  total_area: undefined,
  property_amenities: Object.fromEntries(PROPERTY_AMENITY_VALUES.map((key) => [key, false])),
  building_amenities: Object.fromEntries(BUILDING_AMENITY_VALUES.map((key) => [key, false])),
};

export const resolver = zodResolver(schema);

export type FormFields = z.infer<typeof schema>;
