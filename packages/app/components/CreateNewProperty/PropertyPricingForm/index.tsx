import ArrowRightIcon from "@/assets/icons/arrow-right.svg";
import CoinsIcon from "@/assets/icons/duotone/coins.svg";
import { DrawerFooter } from "@/components/Drawer";
import DrawerHeader from "@/components/Drawer/DrawerHeader";
import { Box, Center, FormField, FormInput, FormSwitch, LabelSeparator } from "@/components/ui";
import { cn } from "@/utils";
import { useFormContext } from "react-hook-form";

const PropertyPricingForm = ({
  onSubmit,
  onCancel,
  onBack,
}: {
  onSubmit?: () => void;
  onCancel?: () => void;
  onBack?: () => void;
}) => {
  const { control, watch, trigger, setError } = useFormContext();

  const isForSale = watch("available_for_sale");
  const isForRent = watch("available_for_rent");
  const salePrice = watch("sale_price");
  const rentPrice = watch("rent_price");

  const otherTaxes = [
    {
      name: "condominium_monthly_tax",
      label: "Condomínio (valor mensal)",
    },
    {
      name: "iptu_monthly_tax",
      label: "IPTU (valor mensal)",
    },
    {
      name: "insurance_monthly_tax",
      label: "Seguro (valor mensal)",
    },
    {
      name: "other_monthly_tax",
      label: "Outras taxas (valor somado mensal)",
    },
  ];

  return (
    <Box className="pt-20">
      <DrawerHeader
        title="Valores, taxas e impostos"
        description="Informações sobre valores e taxas do imóvel."
        icon={CoinsIcon}
      />
      <Box className="gap-4 px-8">
        <Center
          className={cn("flex-1 flex-row rounded-xl border border-border-lighter bg-background-dark p-4", {
            "border-primary-100 bg-primary-50/40": isForSale,
          })}
        >
          <Box className="mr-32 w-1/3">
            <FormField
              control={control}
              name="available_for_sale"
              render={({ field }) => <FormSwitch label="Disponível para venda" {...field} />}
            />
          </Box>
          <Box className={cn("flex-1", { "pointer-events-none opacity-60": !isForSale })}>
            <FormField
              control={control}
              name="sale_price"
              render={({ field }) => <FormInput inputType="currency" label="Valor de venda" {...field} />}
            />
          </Box>
        </Center>
        <Center
          className={cn("flex-1 flex-row rounded-xl border border-border-lighter bg-background-dark p-4", {
            "border-primary-100 bg-primary-50/40": isForRent,
          })}
        >
          <Box className="mr-32 w-1/3">
            <FormField
              control={control}
              name="available_for_rent"
              render={({ field }) => <FormSwitch label="Disponível para locação" {...field} />}
            />
          </Box>
          <Box className={cn("flex-1", { "pointer-events-none opacity-60": !isForRent })}>
            <FormField
              control={control}
              name="rent_price"
              render={({ field }) => <FormInput inputType="currency" label="Valor de locação" {...field} />}
            />
          </Box>
        </Center>
      </Box>
      <LabelSeparator className="my-8 pl-4" label="Outras taxas e impostos" labelPosition="left" />
      <Box className="-mb-8 flex-row flex-wrap pl-8">
        {otherTaxes.map((tax, index) => (
          <Box className="mb-8 w-1/2 pr-8" key={`property-tax-${tax.name}-${index}`}>
            <FormField
              control={control}
              name={tax.name}
              render={({ field }) => (
                <FormInput inputType="currency" label={tax.label} {...field} placeholder="Digite o valor da taxa" />
              )}
            />
          </Box>
        ))}
      </Box>
      <DrawerFooter
        primaryAction={{
          disabled: false,
          label: "Continuar",
          iconAfter: ArrowRightIcon,
          onPress: async () => {
            const output = await trigger(["sale_price", "rent_price"], {
              shouldFocus: true,
            });

            if (!output) return;

            if (isForSale && !salePrice) {
              setError("sale_price", {
                message: 'O valor de venda é obrigatório quando "Disponível para venda" estiver ativado.',
              });
            }

            if (isForRent && !rentPrice) {
              setError("rent_price", {
                message: 'O valor de locação é obrigatório quando "Disponível para locação" estiver ativado.',
              });
            }

            if ((isForSale && !salePrice) || (isForRent && !rentPrice)) {
              return;
            }

            onSubmit?.();
          },
        }}
        secondaryAction={{
          label: "Voltar",
          // iconBefore: ArrowLeftIcon,
          onPress: async () => {
            onBack?.();
          },
        }}
        tertiaryAction={{
          label: "Cancelar",
          // iconBefore: ArrowLeftIcon,
          onPress: async () => {
            onCancel?.();
          },
        }}
      />
    </Box>
  );
};

export default PropertyPricingForm;
