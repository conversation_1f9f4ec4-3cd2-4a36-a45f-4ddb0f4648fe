import { cva } from "@/utils";

export const modalVariants = cva({
  base: ["relative", "mx-auto", "h-auto", "w-auto", "max-h-[90vh]", "max-w-[90vw]", "min-h-[200px]", "min-w-[300px]", "rounded-lg", "flex", "flex-col", "items-center", "justify-center", "z-50", "mx-auto my-auto overflow-hidden border border-border-light bg-background"],
  variants: {
    size: {
      xs: ["w-[320px]", "rounded-lg", "shadow-sm"],
      sm: ["w-[400px]", "rounded-lg", "shadow-sm"],
      md: ["w-[512px]", "rounded-2xl", "shadow-md"],
      lg: ["w-[640px]", "rounded-2xl", "shadow-md"],
      xl: ["w-[768px]", "rounded-2xl", "shadow-lg"],
      "2xl": ["w-[1200px]", "rounded-2xl, shadow-lg"],
      "full": ["w-screen]", "h-screen", "max-h-screen", "max-w-screen", "rounded-xl"],
    },
  },
  defaultVariants: {
    size: "md",
  },
});