import { Portal } from "@gorhom/portal";
import { Box, Button, Heading, Text } from "../ui"; // Added Button, Heading
import MultiplicationSignIcon from "@/assets/icons/multiplication-sign-02.svg"; // Corrected icon import

// Update ModalHeaderProps based on DrawerHeaderProps
export type ModalHeaderProps = {
  title: string; // Title is now required
  description?: string;
  icon?: React.FC<React.SVGProps<SVGSVGElement>>;
  children?: React.ReactNode; // Children are now optional
  onClose?: () => void; // Added onClose prop
};

export const ModalHeader = ({
  children,
  title,
  description,
  icon: HeaderIcon,
  onClose,
}: ModalHeaderProps) => {
  // Conditional rendering: Render children if provided, otherwise render structured header
  return children ? (
    <Portal hostName="modal-header">{children}</Portal>
  ) : (
    <Portal hostName="modal-header">
      {/* Structure adapted from DrawerHeader, added close button */}
      <Box className="flex-row items-center justify-between border-b border-border bg-background py-2 px-3">
        <Box className="flex flex-row items-center">
          {HeaderIcon && (
             // Style similar to DrawerHeader's icon container
            <Box className="mr-3 flex h-8 w-8 items-center shadow-sm justify-center rounded border border-border bg-background">
              <HeaderIcon className="h-4 w-4 text-text" />
            </Box>
          )}
          <Box>
            <Heading size="sm" className="text-foreground">
              {title}
            </Heading>
            {description && <Text className="text-sm text-muted-foreground">{description}</Text>}
          </Box>
        </Box>
        {onClose && (
          // Removed size="icon"
          <Button variant="destructive-outline" onPress={onClose} className="p-0.5 h-auto"> {/* Added padding for better touch area */}
            <MultiplicationSignIcon className="h-4 w-4 text-error-900" />
          </Button>
        )}
      </Box>
    </Portal>
  );
};