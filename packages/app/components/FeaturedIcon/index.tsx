import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Box } from '@/components/ui/custom/box';
import { cn } from '@/utils/cn';
import { LinearGradient } from 'expo-linear-gradient';
import { useGradient } from '@/hooks/useGradient';
import { Center } from '../ui';
import { cx } from '@/utils';

const baseBackgrounds = {
  primary: 'bg-primary',
  warning: 'bg-warning',
  success: 'bg-success',
  error: 'bg-error',
}

const featuredIconVariants = cva(
  'inline-flex items-center justify-center rounded-full',
  {
    variants: {
      variant: {
        light: 'bg-primary-100 text-primary-600',
        gradient: 'text-white',
        dark: 'bg-primary-900 text-white',
        outline: 'border border-border text-foreground',
        modern: 'border border-border bg-background text-foreground',
        'modern-neue': 'bg-primary-600 text-white',
      },
      size: {
        sm: 'h-10 w-10',
        md: 'h-12 w-12',
        lg: 'h-14 w-14',
      },
    },
    defaultVariants: {
      variant: 'light',
      size: 'md',
    },
  }
);

const featuredIconChildrenVariants = cva('', {
  variants: {
    size: {
      sm: 'h-5 w-5',
      md: 'h-6 w-6',
      lg: 'h-7 w-7',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

export interface FeaturedIconProps
  extends React.ComponentProps<typeof Box>,
  VariantProps<typeof featuredIconVariants> {
  children: React.ReactNode;
  color?: 'primary' | 'warning' | 'success' | 'error';
}

const FeaturedIcon = React.forwardRef<any, FeaturedIconProps>(
  ({ className, variant, size, children, color = 'primary', ...props }, ref) => {
    const gradient = useGradient(color);
    const childrenWithProps = React.Children.map(children, (child) => {
      return React.cloneElement(child as React.ReactElement, {
        className: cn(
          featuredIconChildrenVariants({ size }),
          (child as React.ReactElement).props.className
        ),
      });
    });

    if (variant === 'gradient') {
      return (
        <Box
          className={cn(featuredIconVariants({ variant, size, className }))}
          ref={ref}
          {...props}
        >
          <LinearGradient
            colors={gradient['200']}
            className="flex items-center justify-center w-full h-full rounded-full p-[1px]"
          >
            <LinearGradient
              colors={gradient['50']}
              className="flex items-center justify-center w-full h-full rounded-full p-[15%]"
            >
              <Center className={cx("w-full h-full m-2 rounded-full", {
                [baseBackgrounds[color]]: variant === 'gradient',
              })}>
                {childrenWithProps}
              </Center>
            </LinearGradient>
          </LinearGradient>
        </Box>
      );
    }

    return (
      <Box
        className={cn(featuredIconVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      >
        {childrenWithProps}
      </Box>
    );
  }
);

FeaturedIcon.displayName = 'FeaturedIcon';

export { FeaturedIcon, featuredIconVariants };