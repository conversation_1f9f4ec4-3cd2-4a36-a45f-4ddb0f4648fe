import { Box, Button, ColorPicker, Input, Text } from "@/components/ui";
import { useCurrentTeamId } from "@/hooks/useCurrentTeamId";
import { useWebsitesApi } from "@/hooks/useWebsitesApi";
import { useState } from "react";

export const CreateWebsiteForm = ({ onSuccess }: { onSuccess?: () => void }) => {
  const [title, setTitle] = useState("");
  const [subdomain, setSubdomain] = useState("");
  const [primaryColor, setPrimaryColor] = useState("#3B82F6");
  const [secondaryColor, setSecondaryColor] = useState("#10B981");
  const { createWebsite, isCreatingWebsite, createWebsiteError } = useWebsitesApi();
  const [teamAccountId] = useCurrentTeamId();

  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async () => {
    if (!teamAccountId) return;

    setError(null);
    try {
      await createWebsite({
        team_account_id: teamAccountId as string,
        title,
        subdomain,
        published: false,
        status: "draft",
        theme: {
          primary_color: primaryColor,
          secondary_color: secondaryColor,
          font_family: "Inter",
        },
      });
      onSuccess?.();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to create website");
    }
  };

  return (
    <Box className="space-y-4">
      <Box className="mb-4">
        <Text className="mb-2">Website Title</Text>
        <Input
          id="title"
          raw
          value={title}
          onChangeText={(text) => setTitle(text)}
          placeholder="Your website title"
          variant="outline"
          size="md"
          fullWidth
          editable={!isCreatingWebsite}
        />
      </Box>

      <Box className="mb-4">
        <Text className="mb-2">Subdomain</Text>
        <Input
          id="subdomain"
          raw
          value={subdomain}
          onChangeText={(text) => setSubdomain(text)}
          placeholder="your-subdomain"
          variant="outline"
          size="md"
          fullWidth
          editable={!isCreatingWebsite}
        />
      </Box>

      <Box className="mb-4">
        <Text className="mb-2">Primary Color</Text>
        <ColorPicker value={primaryColor} onChange={(color) => setPrimaryColor(color)} placeholder="#3B82F6" />
      </Box>

      <Box className="mb-4">
        <Text className="mb-2">Secondary Color</Text>
        <ColorPicker value={secondaryColor} onChange={(color) => setSecondaryColor(color)} placeholder="#10B981" />
      </Box>

      {(error || createWebsiteError) && (
        <Text className="mb-4 text-error">
          {error || (createWebsiteError instanceof Error ? createWebsiteError.message : "An error occurred")}
        </Text>
      )}

      <Button
        onPress={() => handleSubmit()}
        disabled={isCreatingWebsite || !title || !subdomain}
        isLoading={isCreatingWebsite}
        loadingMessage="Creating..."
      >
        Create Website
      </Button>
    </Box>
  );
};
