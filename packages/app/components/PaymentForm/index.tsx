"use client";

import WarningIcon from "@/assets/icons/alert-02.svg";
import Modal from "@/components/Modal";
import { Box, Button, Center, Heading, Text } from "@/components/ui";
import { useAxios, useCurrentTeamId } from "@/hooks";
import { useBillingStatus } from "@/hooks/useBillingStatus";
import type { StripeProduct } from "@/types";
import { getStripePrice, getTotalTieredPrice, getUnitTieredPrice } from "@/utils";
import { Elements, useElements } from "@stripe/react-stripe-js";
import type { Stripe, StripeCardCvcElement } from "@stripe/stripe-js";
import {
  loadStripe,
  type SetupIntent,
  type StripeCardExpiryElement,
  type StripeCardNumberElement,
  type StripeElementsOptions,
  type StripeError,
} from "@stripe/stripe-js";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { ActivityIndicator } from "react-native";
import { InteractionManager } from "react-native";

const stripePromise = loadStripe(process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY || "");

import { CardCvcElement, CardExpiryElement, CardNumberElement } from "@stripe/react-stripe-js";

const MemoCardNumber = React.memo(CardNumberElement);
const MemoCardExpiry = React.memo(CardExpiryElement);
const MemoCardCvc = React.memo(CardCvcElement);

export type PaymentFormProps = {
  onSuccess: () => void;
  onCancel: () => void;
  product: StripeProduct;
  isYearly: boolean;
  quantity: number;
};

function PaymentFormContent({ onSuccess, onCancel, product, isYearly, quantity }: PaymentFormProps) {
  const [stripe, setStripe] = useState<Stripe | null>(null);
  const elements = useElements();
  const [error, setError] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [currentTeamId] = useCurrentTeamId();
  const { data: billingStatus } = useBillingStatus();
  const { axios } = useAxios(); // Use the authenticated axios instance
  const cardNumberRef = useRef<StripeCardNumberElement | null>(null);
  const cvvRef = useRef<StripeCardCvcElement | null>(null);
  const expiryRef = useRef<StripeCardExpiryElement | null>(null);
  const sharedStripeElementOptions = useMemo(
    () => ({
      style: {
        base: {
          fontSize: "16px",
          fontFamily: "Inter, sans-serif",
        },
      },
      classes: {
        base: "rounded border border-border p-4 py-3 cursor-text transition-all duration-150 ease-in-out",
        focus: "border-primary-600/60 outline-none ring-4 ring-primary/15 ring-offset-0",
      },
    }),
    [],
  );

  const price = getStripePrice(product, isYearly);
  const lookupKey = price?.lookup_key;
  const { subscription_id } = billingStatus ?? {};

  const handleSuccessClose = useCallback(() => {
    setShowSuccessDialog(false);
    onSuccess();
  }, [onSuccess]);

  const handleSubmit = useCallback(
    async (event: React.FormEvent) => {
      event.preventDefault();

      if (!stripe || !elements) {
        setError("Payment processing is not ready yet. Please try again.");
        return;
      }

      setProcessing(true);
      setError(null);

      try {
        // First, create a SetupIntent on the server
        const response = await axios.post("/billing/payment-setup", {
          account_id: currentTeamId,
          lookup_key: lookupKey,
          quantity: quantity,
          subscription_id: subscription_id,
          is_yearly: isYearly,
        });

        const setupIntentData = response.data;

        if (!setupIntentData?.clientSecret) {
          throw new Error("Failed to initialize payment setup");
        }

        const cardElement = elements.getElement(CardNumberElement);
        const cvvElement = elements.getElement(CardCvcElement);
        const expiryElement = elements.getElement(CardExpiryElement);
        if (!cardElement || !cvvElement || !expiryElement) {
          throw new Error("Payment form elements not properly initialized");
        }

        // Add a retry mechanism with delays to ensure the webhook has time to process the setup intent
        console.log("Preparing to confirm setup intent...");

        let setupIntent: SetupIntent | undefined;
        let confirmError: StripeError | undefined;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            console.log(`Attempt ${retryCount + 1}/${maxRetries} to confirm setup intent...`);
            // Add a delay that increases with each retry
            await new Promise((resolve) => setTimeout(resolve, 1000 + retryCount * 500));

            // Confirm the SetupIntent with the card details
            const result = await stripe.confirmCardSetup(setupIntentData.clientSecret, {
              payment_method: {
                card: cardElement,
              },
            });

            setupIntent = result.setupIntent;
            confirmError = result.error;

            if (confirmError) {
              console.error(`Attempt ${retryCount + 1} failed:`, confirmError.message);
              if (confirmError.message?.includes("No such setupintent") && retryCount < maxRetries - 1) {
                retryCount++;
                continue;
              }
              throw confirmError;
            }

            console.log("Setup intent confirmation successful:", setupIntent?.id);
            break;
          } catch (err: unknown) {
            console.error(`Attempt ${retryCount + 1} error:`, err);
            if (
              typeof err === "object" &&
              err !== null &&
              "message" in err &&
              typeof err.message === "string" &&
              err.message.includes("No such setupintent") &&
              retryCount < maxRetries - 1
            ) {
              retryCount++;
              continue;
            }
            throw err;
          }
        }

        if (confirmError) {
          throw confirmError;
        }

        if (!setupIntent) {
          throw new Error("Failed to setup payment method");
        }

        // Handle the successful setup - notify our backend about the setup intent
        // The actual processing will be done by webhooks
        const paymentSuccessResponse = await axios.post("/billing/payment-success", {
          setup_intent_id: setupIntent.id,
        });

        const paymentResult = paymentSuccessResponse.data;

        if (paymentResult?.error) {
          console.error("Payment processing error:", paymentResult.error);
          throw new Error(paymentResult.error);
        }

        console.log("Payment setup successful, processing via webhooks:", paymentResult);

        // Add a small delay to allow webhooks to process
        await new Promise((resolve) => setTimeout(resolve, 1500));

        // Show success dialog
        setShowSuccessDialog(true);
      } catch (err: unknown) {
        console.error("Payment submission error:", err);

        let errorMessage = "An unexpected error occurred. Please try again.";
        let errorDetails = "";

        if (err instanceof Error) {
          errorMessage = err.message;
          errorDetails = err.stack || "";
        } else if (err && typeof err === "object") {
          if ("message" in err) {
            errorMessage = String(err.message);
          }
          // Log the full error object for debugging
          console.error("Detailed error object:", JSON.stringify(err, null, 2));

          // For Stripe errors, they often have more details
          if ("type" in err && "code" in err) {
            errorDetails = `Type: ${String(err.type)}, Code: ${String(err.code)}`;
          }
        } else if (typeof err === "string") {
          errorMessage = err;
        }

        console.error(`Payment error: ${errorMessage}${errorDetails ? ` (${errorDetails})` : ""}`);
        setError(errorMessage);
      } finally {
        setProcessing(false);
      }
    },
    [stripe, elements, currentTeamId, lookupKey, quantity, isYearly, subscription_id, axios.post],
  );

  const initializeStripe = useCallback(async () => {
    const stripeInstance = await stripePromise;
    setStripe(stripeInstance);
  }, []);

  useEffect(() => {
    const interaction = InteractionManager.runAfterInteractions(initializeStripe);
    return () => interaction.cancel();
  }, [initializeStripe]);

  return (
    <Box className="will-change-transform">
      <form onSubmit={handleSubmit} className="w-full">
        <Box className="w-full gap-4 rounded-lg p-16">
          <Heading size="lg">Dados para pagamento</Heading>
          <Box className="space-y-1.5">
            <Text
              className="-mb-1 cursor-default pb-1 font-medium text-sm text-text-secondary"
              onPress={() => cardNumberRef.current?.focus()}
              accessibilityRole="button"
            >
              Número do cartão
            </Text>
            <Box>
              <MemoCardNumber
                options={{ ...sharedStripeElementOptions, disableLink: true }}
                onReady={(element) => {
                  cardNumberRef.current = element;
                  element.focus();
                }}
              />
            </Box>
          </Box>

          <Box className="flex flex-row gap-4">
            <Box className="flex-1 space-y-1.5">
              <Text
                onPress={() => expiryRef.current?.focus()}
                className="cursor-default font-medium text-sm text-text-secondary"
              >
                Vencimento
              </Text>
              <MemoCardExpiry
                options={{ ...sharedStripeElementOptions, placeholder: "MM/AA" }}
                onReady={(element) => {
                  expiryRef.current = element;
                }}
              />
            </Box>

            <Box className="flex-1 space-y-1.5">
              <Text
                onPress={() => cvvRef.current?.focus()}
                className="cursor-default font-medium text-sm text-text-secondary"
              >
                CVV
              </Text>
              <MemoCardCvc
                options={sharedStripeElementOptions}
                onReady={(element) => {
                  cvvRef.current = element;
                }}
              />
            </Box>
          </Box>
        </Box>

        {error && (
          <Box className="mx-8 mb-8 flex-row items-center gap-3 rounded border border-warning-200 bg-warning-50 p-2">
            <WarningIcon className="h-4 w-4 text-warning" />
            <Text className="font-medium text-sm text-warning">{error}</Text>
          </Box>
        )}

        <Box className="flex-row items-end justify-end gap-4 px-16 pb-8">
          <Button variant="link" onPress={onCancel} disabled={processing}>
            <Text>Cancelar</Text>
          </Button>
          <button type="submit">
            <Button asChild isLoading={processing} loadingMessage="Processando...">
              <Text>{processing ? "Processando..." : `Assinar ${product.name}`}</Text>
            </Button>
          </button>
        </Box>
      </form>

      <Modal isOpen={showSuccessDialog} onClose={handleSuccessClose}>
        <Box className="p-6">
          <Heading>Pagamento efetuado com sucesso</Heading>
          <Text className="mt-2 text-text-secondary">
            O seu cartão foi cadastrado e o pagamento referente à assinatura do {product.name} foi efetuado com sucesso!
          </Text>
          <Text className="text-text-secondary">
            Você já pode acessar todas as funcionalidades disponíveis no seu plano.
          </Text>
          <Button className="mt-4" onPress={handleSuccessClose}>
            <Text>Close</Text>
          </Button>
        </Box>
      </Modal>
    </Box>
  );
}

export default function PaymentForm(props: PaymentFormProps) {
  const price = getStripePrice(props.product, props.isYearly);
  const interval = props.isYearly ? "ano" : "mês";
  const quantity = props.quantity || 1;
  const unitAmount = !price?.tiers ? (price?.unit_amount || 0) / 100 : getUnitTieredPrice(quantity, price?.tiers ?? []);
  const totalAmount = price?.tiers
    ? getTotalTieredPrice(quantity, price.tiers)
    : ((price?.unit_amount || 0) * quantity) / 100;
  const yearlySavings = props.isYearly ? (unitAmount * quantity * 2) / 1000 : 0; // 2 months free for yearly
  const [isFormReady, setIsFormReady] = useState(false);

  console.log(price, quantity, unitAmount);

  useEffect(() => {
    setTimeout(() => {
      setIsFormReady(true);
    }, 500);
  }, []);

  if (!props.product || props.isYearly === undefined) {
    return null;
  }

  const options: StripeElementsOptions = {
    appearance: {
      theme: "flat",
      variables: {
        fontFamily: "Inter var, system-ui, sans-serif",
        fontSizeBase: "16px",
      },
    },
  };

  return (
    <Box className="flex min-h-full w-full flex-col md:flex-row">
      <Box className="flex-1 border-border-light border-r bg-background-dark">
        <Box>
          <Box className="px-16 py-12">
            <Box>
              <Heading size="lg" className="my-4 text-primary-800">
                Resumo da assinatura
              </Heading>
            </Box>
            <Box className="flex-row items-start justify-between">
              <Box>
                <Heading>{props.product.name}</Heading>
                <Text className="text-sm text-text-secondary">
                  Cobrado {props.isYearly ? "anualmente" : "mensalmente"}
                </Text>
              </Box>
              <Box className="items-end justify-end">
                <Text className="font-medium">R${unitAmount}</Text>
                <Text className="-mb-0.5 ml-2 text-text-secondary text-xs">por usuário</Text>
                <Text className="ml-2 text-text-secondary text-xs">por {interval === "ano" ? "ano" : "mês"}</Text>
              </Box>
            </Box>
          </Box>

          <Box className="flex-1 flex-row items-center justify-between border-border-light border-t px-16 py-12">
            <Heading>Número de usuários</Heading>
            <Text className="font-medium">{quantity}</Text>
          </Box>

          <Box className="mb-4 border-border-light border-t px-16 py-12">
            {props.isYearly && (
              <Box className="mb-4 rounded border border-success-100 bg-success-50 p-3">
                <Text className="text-center text-sm text-success-900">
                  Aplicamos o desconto de R${(yearlySavings * 100).toFixed(2)} pela assinatura anual!
                </Text>
              </Box>
            )}
            <Box className="flex-row items-center justify-between">
              <Box>
                <Heading>Valor total</Heading>
                <Text className="text-sm text-text-secondary">Valor que será cobrado agora</Text>
              </Box>
              <Text className="font-bold text-text-primary text-xl">
                {totalAmount.toFixed(2)}
                <Text className="text-sm text-text-tertiary">/{interval}</Text>
              </Text>
            </Box>
          </Box>
        </Box>
      </Box>

      <Box className="flex-1">
        {isFormReady ? (
          <Elements stripe={stripePromise} options={options}>
            <PaymentFormContent
              onSuccess={props.onSuccess}
              onCancel={props.onCancel}
              product={props.product}
              isYearly={props.isYearly}
              quantity={props.quantity}
            />
          </Elements>
        ) : (
          <Center className="h-full w-full">
            <ActivityIndicator size="large" color="blue" />
          </Center>
        )}
      </Box>
    </Box>
  );
}
