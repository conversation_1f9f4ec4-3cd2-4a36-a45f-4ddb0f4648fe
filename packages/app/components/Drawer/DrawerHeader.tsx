import { Portal } from "@gorhom/portal";
import { Box, Center, Button, type <PERSON><PERSON>Variants, Text, Heading } from "../ui";
import AspectRatioIcon from "@/assets/icons/duotone/aspect-ratio.svg";

export type DrawerHeaderActionProps = {
  label: string;
  variant?: ButtonVariants["variant"];
  onPress: () => void;
};

export type DrawerHeaderProps = {
  title: string;
  description?: string;
  icon?: React.FC<React.SVGProps<SVGSVGElement>>;
  featuredBackground?: boolean;
  children?: React.ReactNode;
  onClose?: () => void;
};

export const DrawerHeader = ({
  children,
  title,
  description,
  icon: HeaderIcon,
  onClose,
  featuredBackground,
}: DrawerHeaderProps) => {
  return children ? (
    <Portal hostName="drawer-header">{children}</Portal>
  ) : (
    <Portal hostName="drawer-header">
      <Box className="absolute top-0 left-0 z-50 w-full">
        <Box
          className={
            "flex-row items-center rounded-xs rounded-b-none border-primary-900/5 border-b bg-background-dark p-2 shadow-xs"
          }
        >
          {HeaderIcon && (
            <Center className="mr-4 h-8 w-8 rounded-lg border border-primary-800/20 bg-background shadow-xs">
              <HeaderIcon className="h-4 w-4 stroke-[3] text-text opacity-90" />
            </Center>
          )}
          <Box>
            <Heading size="sm" className="text-text leading-[1.1em] tracking-[-0.02em]">
              {title}
            </Heading>
            {/* {description && <Text className="text-text-tertiary text-xs">{description}</Text>} */}
          </Box>
        </Box>
      </Box>
    </Portal>
  );
};

export default DrawerHeader;
