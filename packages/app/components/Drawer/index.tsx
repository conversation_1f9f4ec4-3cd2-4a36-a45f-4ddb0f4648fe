import { Portal, PortalHost } from "@gorhom/portal";
import type { VariantProps } from "class-variance-authority";
import { memo } from "react";
import { ScrollView } from "react-native";
import Reanimated, { FadeIn, FadeInRight, FadeOut, FadeOutRight } from "react-native-reanimated";
import { Box } from "../ui";
import DrawerSteps from "./DrawerSteps";
import { drawerVariants } from "./style";

export type FormProgressStep = { isComplete: boolean; percentage: number };

export type FormProgress = Record<number, Array<FormProgressStep>>;

export type DrawerProps = {
  steps?: number;
  currentStep?: number;
  currentStepProgress?: number;
  children: React.ReactNode;
  title?: string;
  isOpen?: boolean;
  onClose?: () => void;
  progress?: FormProgress;
  size?: VariantProps<typeof drawerVariants>["size"];
};

const getCurrentStepConsolidatedProgress = (progress: FormProgress | undefined, currentStep: number | undefined) => {
  if (!progress || currentStep === undefined || !progress[currentStep] || progress[currentStep].length === 0) return 0;
  return progress[currentStep].reduce((acc, curr) => {
    return acc + (curr.isComplete ? curr.percentage : 0);
  }, 0);
};

const MemoizedScrollView = memo(({ children }: { children: React.ReactNode }) => (
  <ScrollView className="">{children}</ScrollView>
));

const DrawerContent = memo(
  ({
    children,
    steps,
    currentStep,
    currentStepProgress,
  }: {
    children: React.ReactNode;
    steps?: number;
    currentStep?: number;
    currentStepProgress?: number;
  }) => (
    <Box className="flex-1 bg-background">
      <MemoizedScrollView>{children}</MemoizedScrollView>
      <DrawerSteps steps={steps} currentStep={currentStep} currentStepProgress={currentStepProgress} />
    </Box>
  ),
);

const Drawer = ({
  children,
  isOpen = false,
  steps: stepsProp,
  currentStep,
  currentStepProgress: currentStepProgressProp,
  progress,
  size,
}: DrawerProps) => {
  const steps = stepsProp ? stepsProp : progress ? Object.keys(progress).length : 0;
  const currentStepProgress = currentStepProgressProp
    ? currentStepProgressProp
    : getCurrentStepConsolidatedProgress(progress, currentStep);

  return isOpen ? (
    <Portal>
      <Box className="absolute top-0 right-0 z-50 flex h-full w-full items-end">
        <Reanimated.View
          entering={FadeIn.duration(150)}
          exiting={FadeOut.duration(150)}
          style={{
            position: "absolute",
            top: 0,
            right: 0,
            zIndex: 10,
            height: "100%",
            width: "100%",
          }}
        >
          <Box className="h-full w-full bg-background-inverse/90" />
        </Reanimated.View>
        <Reanimated.View
          entering={FadeInRight.springify().damping(20).stiffness(100)}
          exiting={FadeOutRight.springify().damping(20).stiffness(100)}
          style={{ flex: 1, zIndex: 20 }}
        >
          <Box className={drawerVariants({ size })}>
            <PortalHost name="drawer-header" />
            <DrawerContent steps={steps} currentStep={currentStep} currentStepProgress={currentStepProgress}>
              {children}
            </DrawerContent>
            <PortalHost name="drawer-footer" />
          </Box>
        </Reanimated.View>
      </Box>
    </Portal>
  ) : null;
};

export default memo(Drawer);

export * from "./DrawerFooter";
