import { Portal } from "@gorhom/portal";
import { Box, Button, type ButtonVariants, Text } from "../ui";

export type DrawerFooterActionProps = {
  disabled?: boolean;
  label: string;
  iconBefore?: React.FC<React.SVGProps<SVGSVGElement>>;
  iconAfter?: React.FC<React.SVGProps<SVGSVGElement>>;
  variant?: ButtonVariants["variant"];
  onPress: () => void;
};

export type DrawerFooterProps = {
  children?: React.ReactNode;
  primaryAction?: DrawerFooterActionProps;
  secondaryAction?: DrawerFooterActionProps;
  tertiaryAction?: DrawerFooterActionProps;
};

export const DrawerFooter = ({ children, primaryAction, secondaryAction, tertiaryAction }: DrawerFooterProps) => {
  return children ? (
    <Portal hostName="drawer-footer">{children}</Portal>
  ) : (
    <Portal hostName="drawer-footer">
      <Box className="h-[70px] flex-row items-center justify-between border-border-light border-t-[1px] bg-background-dark px-4">
        <Box>
          {tertiaryAction && (
            <Button
              variant={tertiaryAction.variant || "destructive-link"}
              onPress={tertiaryAction.onPress}
              disabled={tertiaryAction.disabled}
            >
              <Text>{tertiaryAction?.label}</Text>
            </Button>
          )}
        </Box>
        <Box className="flex-row gap-2">
          {secondaryAction && (
            <Button
              variant={secondaryAction.variant || "outline"}
              onPress={secondaryAction.onPress}
              disabled={secondaryAction.disabled}
            >
              <Text>{secondaryAction?.label}</Text>
            </Button>
          )}
          {primaryAction && (
            <Button
              variant={primaryAction.variant || "default"}
              onPress={primaryAction.onPress}
              disabled={primaryAction.disabled}
            >
              {primaryAction.iconBefore && (
                <Box className="mr-2">
                  <primaryAction.iconBefore className="h-5 w-5 text-text-inverse" />
                </Box>
              )}
              <Text>{primaryAction.label}</Text>
              {primaryAction.iconAfter && (
                <Box className="ml-2">
                  <primaryAction.iconAfter className="h-5 w-5 text-text-inverse" />
                </Box>
              )}
            </Button>
          )}
        </Box>
      </Box>
    </Portal>
  );
};

export default DrawerFooter;
