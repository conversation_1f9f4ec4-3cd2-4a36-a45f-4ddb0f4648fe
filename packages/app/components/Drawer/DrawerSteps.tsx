import { gray } from "@/theme/colors";
import { LinearGradient } from "expo-linear-gradient";
import { memo } from "react";
import { Box } from "../ui";

type DrawerStepsProps = {
  steps?: number;
  currentStep?: number;
  currentStepProgress?: number;
};

const StepIndicator = memo(
  ({ isActive, isComplete, progress }: { isActive: boolean; isComplete: boolean; progress: number }) => {
    return (
      <Box
        className={`h-full overflow-hidden rounded-full ${isActive ? "bg-primary-400" : "bg-primary-600"} shadow-sm transition-all duration-300 ease-in-out`}
        style={{
          width: `${progress || 0}%`,
        }}
      >
        {!isComplete && (
          <LinearGradient
            className="absolute right-0 bottom-0 z-[-1] h-full w-[32px] rounded-tr-full rounded-br-full opacity-25"
            start={[0, 0]}
            end={[1, 0]}
            colors={["rgba(10,10,10,0)", "rgba(10,10,10,1)"]}
          />
        )}
      </Box>
    );
  },
);

const Step = memo(
  ({
    isCurrentStep,
    isComplete = false,
    progress,
  }: {
    isCurrentStep: boolean;
    isComplete?: boolean;
    progress: number;
  }) => (
    <Box
      className={`h-[6px] transition-all duration-300 ease-in-out ${isCurrentStep ? "flex-[2]" : "flex-[1]"} rounded-full ${isComplete ? "opacity-30" : ""} bg-border-light shadow-xs`}
    >
      <StepIndicator isActive={isCurrentStep} isComplete={isComplete} progress={progress} />
    </Box>
  ),
);

const DrawerSteps = memo(({ steps = 0, currentStep = 1, currentStepProgress = 0 }: DrawerStepsProps) => {
  if (steps <= 0) return null;

  return (
    <Box className="absolute bottom-0 left-0 w-full">
      <Box className="flex-row gap-2 px-4 py-2">
        {(() => {
          const items: JSX.Element[] = [];
          for (let i = 1; i <= steps; i++) {
            items.push(
              <Step
                key={`drawer-step-${i}`}
                isCurrentStep={currentStep === i}
                isComplete={currentStep > i}
                progress={currentStep > i ? 100 : currentStep < i ? 0 : currentStepProgress}
              />,
            );
          }
          return items;
        })()}
      </Box>
      {/* <LinearGradient
        className="absolute bottom-0 left-0 z-[-1] h-[24px] w-full"
        colors={[
          "rgba(0,0,0,0)",
          `rgba(${gray["50"]},0.7)`,
          `rgba(${gray["50"]},0.8)`,
          `rgba(${gray["50"]},0.9)`,
          `rgba(${gray["50"]},1)`,
        ]}
      /> */}
    </Box>
  );
});

DrawerSteps.displayName = "DrawerSteps";
Step.displayName = "Step";
StepIndicator.displayName = "StepIndicator";

export default DrawerSteps;
