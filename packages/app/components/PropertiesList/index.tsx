import { PageRow } from "@/components/PageLayout";
import { Box, Button, Heading, Text } from "@/components/ui";
import { MasonryFlashList } from "@shopify/flash-list";
import { useWindowDimensions } from "react-native";
import AddIcon from "@/assets/icons/add.svg";
import FilterIcon from "@/assets/icons/filter-horizontal.svg";
import { useDrawers } from "@/context/drawer";
import { useState, useMemo } from "react";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { router } from "expo-router";

import { usePropertiesList } from "@/hooks";
import type { Property } from "@/types";
import PropertyCard from "./PropertyCard";
import StatefulSpinner from "../StatefulSpinner";

export const PropertiesList = () => {
  const { openCreatePropertyDrawer } = useDrawers();
  const [selectedProperties, setSelectedProperties] = useState<string[]>([]);
  const { data: properties, isLoading, error } = usePropertiesList();
  const [showPropertyList, setShowPropertyList] = useState(true);
  const { width } = useWindowDimensions();

  // Calculate number of columns based on screen width
  const numColumns = useMemo(() => {
    if (width >= 1600) return 4; // xl breakpoint at 1600px
    if (width >= 1024) return 3; // lg breakpoint at 1024px
    if (width >= 640) return 2; // sm breakpoint at 640px
    return 1;
  }, [width]);

  const handlePropertySelect = (propertyId: string) => {
    setSelectedProperties((prev) =>
      prev.includes(propertyId) ? prev.filter((id) => id !== propertyId) : [...prev, propertyId],
    );
  };

  if (error) {
    return (
      <PageRow className="mb-12">
        <Box className="mt-16 w-full items-center justify-center rounded-lg border border-border py-8">
          <Text className="text-error">Failed to load properties. Please try again.</Text>
          <Text className="mt-2 text-sm text-text-tertiary">{error.message}</Text>
        </Box>
      </PageRow>
    );
  }

  return (
    <PageRow className="mb-12">
      <Box className="w-full">
        <Box className="mb-8 flex-row items-center gap-4">
          <Heading size="lg" className="mr-4">
            Lista de imóveis
          </Heading>
          <Button size="xs" key="add-property-header-action" onPress={openCreatePropertyDrawer}>
            <AddIcon className="mr-1 w-3 text-text-inverse" />
            <Text>Cadastrar imovel</Text>
          </Button>
          <Button
            variant="outline"
            size="xs"
            key="property-list-filters"
            onPress={() => setShowPropertyList(!showPropertyList)}
          >
            <FilterIcon className="mr-1 h-[16px] w-[16px] text-text" />
            <Text>Filtros</Text>
          </Button>
        </Box>
        <Box className="flex-1 flex-row">
          {showPropertyList && properties.length > 0 && (
            <MasonryFlashList
              estimatedItemSize={260}
              data={properties}
              numColumns={numColumns}
              renderItem={({ item: property, index }: { item: Property; index: number }) => (
                <PropertyCard
                  key={`property-list-${property.id}-${index}`}
                  property={property}
                  index={index}
                  numColumns={numColumns}
                  onPress={(propertyId) => router.push(`/imoveis/${propertyId}`)}
                />
              )}
            />
          )}
        </Box>
      </Box>
    </PageRow>
  );
};

export default PropertiesList;
