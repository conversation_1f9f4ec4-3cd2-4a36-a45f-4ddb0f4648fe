import { Badge, Box, Center, Heading, Text } from "@/components/ui";
import { Image } from "expo-image";
import { LinearGradient } from "expo-linear-gradient";
import { cx } from "@/utils";
import { usePropertyMedia } from "@/hooks/usePropertyMedia";
import { formatCurrency } from "@/utils";
import { Pressable } from "react-native";
import type { Property } from "@/types";
import CameraIcon from "@/assets/icons/camera-01.svg";

// Icons
import LocationIcon from "@/assets/icons/location-04.svg";
import BuiltAreaIcon from "@/assets/icons/built-area.svg";
import TotalAreaIcon from "@/assets/icons/total-area.svg";
import BedDoubleIcon from "@/assets/icons/bed-double.svg";
import BedSingle from "@/assets/icons/bed-single.svg";
import ToiletIcon from "@/assets/icons/toilet.svg";
import CarIcon from "@/assets/icons/car-05.svg";
import type { FC, SVGProps } from "react";

type PropertyCardProps = {
  property: Property;
  index: number;
  numColumns: number;
  onPress?: (propertyId: string) => void;
};

// Component to fetch and display the property image
const PropertyImage = ({ propertyId }: { propertyId: string }) => {
  const { data: mediaItems, isLoading } = usePropertyMedia(propertyId);
  const firstImage = mediaItems && mediaItems.length > 0 ? mediaItems[0] : null;

  if (isLoading || !firstImage) {
    return (
      <Center className="h-[180px] w-full items-center justify-center bg-primary-800/40">
        <Box className="rounded-full p-4">
          <CameraIcon className="h-16 w-16 text-text-inverse" />
        </Box>
      </Center>
    );
  }

  // Extract URL and format it properly for the custom domain
  const imageUrl = firstImage.url;

  return (
    <Box className="relative w-full">
      <Image
        source={{ uri: imageUrl }}
        style={{ width: "100%", height: 180 }}
        contentFit="cover"
        transition={200}
        cachePolicy="memory-disk"
      />
    </Box>
  );
};

const PropertyBadgeList = ({
  badges,
}: { badges: { key: string; icon: FC<SVGProps<SVGSVGElement>>; count: number }[] }) => {
  return (
    <Box className="flex-row gap-2 bg-background-dark px-3 py-2">
      {badges.map((badge) => (
        <Badge
          key={badge.key}
          beforeIcon={badge.icon}
          variant="outline"
          rounded="full"
          className="bg-background/90 shadow"
          size="sm"
        >
          <Text className="font-medium">{badge.count}</Text>
        </Badge>
      ))}
    </Box>
  );
};

const PropertyPricesList = ({ prices }: { prices: { label: string; value: number }[] }) => {
  return (
    <Box className="flex-row border-border border-t">
      {prices.map((price, index) => (
        <Box key={price.label} className={cx("flex-1 p-3", { "border-border border-r": index !== prices.length - 1 })}>
          <Text className="font-medium text-3xs text-text-tertiary uppercase">{price.label}</Text>
          <Text className="text-text-secondary tracking-tighter" numberOfLines={1}>
            {formatCurrency(price.value, { hideCurrency: true, hideDecimals: true })}
          </Text>
        </Box>
      ))}
    </Box>
  );
};

export const PropertyCard = ({ property, index, numColumns, onPress }: PropertyCardProps) => {
  // Dynamically calculate margin based on column position
  const isLastInRow = (index + 1) % numColumns === 0;
  const { data: mediaItems, isLoading } = usePropertyMedia(property.id);

  const hasNoMedia = !mediaItems || mediaItems.length === 0;

  const handlePress = () => {
    if (onPress) {
      onPress(property.id);
    }
  };

  return (
    <Pressable onPress={onPress ? handlePress : undefined}>
      <Box className={cx("mb-4 rounded-lg bg-background-dark p-0.5", { "mr-2": !isLastInRow })}>
        <Box className="overflow-hidden rounded-lg border border-border bg-background shadow-xs">
          <Box>
            <Box className="absolute top-0 right-0 bottom-0 left-0 z-10 h-full w-full">
              <Box className="absolute top-0 right-0 bottom-0 left-0 z-10 h-full w-full justify-between">
                <Badge
                  variant="outline"
                  rounded="full"
                  className="m-2 w-fit bg-background/90 shadow"
                  size="sm"
                  beforeIcon={LocationIcon}
                >
                  <Text>
                    {property.address?.neighborhood?.name}, {property.address?.city?.name}
                  </Text>
                </Badge>
                <Box className="m-4">
                  <Box className="mb-2 w-fit flex-row rounded border border-background-inverse/20 bg-background-inverse/30">
                    <Box className="flex-row items-center gap-2 border-background-inverse/20 border-r px-2 py-1.5">
                      <BuiltAreaIcon className="h-4 w-4 text-text-inverse" />
                      <Text className="text-text-inverse">{property?.built_area || 0} m²</Text>
                    </Box>
                    <Box className="flex-row items-center gap-2 px-2 py-1.5">
                      <TotalAreaIcon className="h-4 w-4 text-text-inverse" />
                      <Text className="text-text-inverse">{property?.total_area || 0} m²</Text>
                    </Box>
                  </Box>
                  <Heading size="sm" className="text-text-inverse">
                    {property?.title}
                  </Heading>
                </Box>
              </Box>
              <LinearGradient
                colors={["rgba(0, 0, 0, 0)", "rgba(0, 0, 0, 0.15)", "rgba(0,0,0,0.6)", "rgba(0, 0, 0, 0.9)"]}
                locations={[0, 0.4, 0.8, 1]}
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
                className={cx("h-full w-full", { "opacity-80": hasNoMedia })}
              />
            </Box>
            <PropertyImage propertyId={property.id} />
          </Box>
          <Box>
            <PropertyBadgeList
              badges={[
                { key: `bed-double-${property.id}-badge`, icon: BedDoubleIcon, count: property?.suites_count || 0 },
                { key: `bed-single-${property.id}-badge`, icon: BedSingle, count: property?.rooms_count || 0 },
                { key: `toilet-${property.id}-badge`, icon: ToiletIcon, count: property?.bathrooms_count || 0 },
                { key: `car-${property.id}-badge`, icon: CarIcon, count: property?.parking_spots_count || 0 },
              ]}
            />
            <PropertyPricesList
              prices={[
                { label: "Venda (R$)", value: property?.prices?.sale_price || 0 },
                { label: "Locação (R$)", value: property?.prices?.rent_price || 0 },
                { label: "Diária (R$)", value: property?.prices?.bnb_price || 0 },
              ]}
            />
          </Box>
        </Box>
      </Box>
    </Pressable>
  );
};

export default PropertyCard;
