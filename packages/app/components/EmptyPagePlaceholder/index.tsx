import AddIcon from "@/assets/icons/add.svg";
import ArrowR<PERSON> from "@/assets/icons/arrow-right.svg";
import type { SvgProps } from "react-native-svg";
import { Box, Button, Center, Heading, Text } from "../ui";

type ButtonIconType = "add" | "navigate";

const buttonIcons: Record<ButtonIconType, React.FC<SvgProps>> = {
  add: AddIcon,
  navigate: ArrowRight,
};

export interface EmptyPagePlaceholderProps {
  title: string;
  description: string;
  illustration: React.FC<SvgProps>;
  button?: {
    position: "before" | "after";
    label: string;
    icon?: ButtonIconType | React.FC<SvgProps>;
    onPress: () => void;
  };
}

export const EmptyPagePlaceholder = ({
  title,
  description,
  illustration: IllustrationComponent,
  button,
}: EmptyPagePlaceholderProps) => {
  const ButtonIcon = button?.icon ? (typeof button.icon === "string" ? buttonIcons[button.icon] : button.icon) : null;
  const buttonPosition = button?.position || "after";

  return (
    <Center className="w-full h-full flex-1 overflow-hidden">
      <IllustrationComponent className="mb-[1vw] max-h-[18rem] w-1/2 max-w-[18rem] stroke-[3px] text-primary" />
      <Heading size="xl">{title}</Heading>
      <Text className="mt-2 mb-8 max-w-[480px] text-center text-text-tertiary">{description}</Text>
      {button && (
        <Button size="lg" onPress={button.onPress}>
          {ButtonIcon && buttonPosition === "before" && <ButtonIcon className="mr-2 w-[18px] text-text-inverse" />}
          <Text>{button.label}</Text>
          {ButtonIcon && buttonPosition === "after" && <ButtonIcon className="ml-2 w-[18px] text-text-inverse" />}
        </Button>
      )}
    </Center>
  );
};
