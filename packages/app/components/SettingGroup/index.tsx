import { Box, Heading, Text } from "@/components/ui";
import React from "react";

interface SettingGroupProps {
  title: string;
  description: string;
  children: React.ReactNode;
}

export const SettingGroup = ({ title, description, children }: SettingGroupProps) => {
  return (
    <Box className="xl:flex-row border-b border-border-light">
      <Box className="w-full xl:w-1/3 min-w-[300px] pr-6 bg-background-dark/30 p-6 md:p-8 lg:p-12 xl:p-16 border-b xl:border-b-0 xl:border-r border-border-lighter">
        <Heading size="lg">{title}</Heading>
        <Text className="mt-2 text-sm text-text-secondary w-[80%]">{description}</Text>
      </Box>
      <Box className="flex-1 p-6 md:p-8 lg:p-12 xl:p-16">{children}</Box>
    </Box>
  );
};