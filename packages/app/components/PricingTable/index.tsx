"use client";

import { useState, useEffect, type FC, type SVGProps } from "react";
import { Box, Button, Center, Heading, Label, Switch, Text } from "../ui";
import { cx, getStripePrice, getTotalTieredPrice, getUnitTieredPrice, parseFeatureText } from "@/utils";
import HutIcon from "@/assets/icons/duotone/hut.svg";
import HouseIcon from "@/assets/icons/duotone/house-01.svg";
import CityIcon from "@/assets/icons/duotone/city-01.svg";

import FeatureIcon from "@/assets/icons/checkmark-circle-02.svg";

import type { StripeProductFeature } from "@/types";
import { usePricingProducts } from "@/hooks/usePricingProducts";
import useTeamAccountMembers from "@/hooks/useTeamAccountMembers";
import PaymentForm from "@/components/PaymentForm";
import Modal from "../Modal";

const featureIcons: Record<string, FC<SVGProps<SVGSVGElement>>> = {
  checkmark: FeatureIcon,
  y: FeatureIcon,
};

const planStyles = [
  {
    card: "",
    title: "text-text-secondary",
    price: "text-text-secondary",
    features: "text-text-secondary",
    featureIcon: "text-gray-500",
    buttonPlanName: "text-text-secondary",
    icon: "text-text-secondary",
    buttonCard: "border-border-light bg-background",
  },
  {
    card: "bg-white border-gray-200",
    title: "text-primary",
    price: "text-text-secondary",
    features: "text-text-secondary",
    featureIcon: "text-primary-500",
    buttonPlanName: "text-primary",
    icon: "text-primary",
    buttonCard: "border-border-light bg-background",
  },
  {
    card: "bg-background border border-primary-600/10",
    title: "text-primary-700",
    price: "text-text-secondary",
    features: "text-text-secondary",
    featureIcon: "text-primary-700",
    buttonPlanName: "text-primary-700",
    icon: "text-primary-700",
    buttonCard: "border-primary-600/10 bg-primary-50/10",
  },
];

const planIcons = [HutIcon, HouseIcon, CityIcon];

export default function PricingTable() {
  const [selectedProductIndex, setSelectedProductIndex] = useState<number | null>(null);
  const [isYearly, setIsYearly] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { data: teamMembers, isLoading: isTeamMembersLoading, error: teamMembersError } = useTeamAccountMembers();

  const {
    data: pricingProducts,
    isLoading: isPricingProductsLoading,
    error: pricingProductsError,
  } = usePricingProducts(isYearly);

  if (!pricingProducts) {
    return (
      <Box className="w-full text-center">
        <Text>Loading pricing plans...</Text>
      </Box>
    );
  }

  if (error) {
    return <Box className="w-full text-center text-red-600">{error}</Box>;
  }

  if (pricingProducts && pricingProducts.length === 0) {
    return <Box className="w-full text-center">No pricing plans available.</Box>;
  }

  return (
    <>
      <Modal isOpen={selectedProductIndex !== null} size="xl">
        <PaymentForm
          quantity={teamMembers.length}
          isYearly={isYearly}
          onSuccess={() => {
            setSelectedProductIndex(null);
          }}
          onCancel={() => {
            setSelectedProductIndex(null);
          }}
          product={pricingProducts[selectedProductIndex || 0] ?? {}}
        />
      </Modal>
      <Box className="w-full">
        <Box className="flex lg:flex-row lg:items-center">
          <Heading size="xl" className="mr-8 text-center">
            Escolha o plano que melhor para as suas necessidades
          </Heading>
          <Center
            className={cx("flex-row rounded-full p-2", {
              "border-primary-300 bg-primary-50 ring-primary-100": isYearly,
            })}
          >
            <Switch id="billing-toggle" checked={isYearly} onCheckedChange={setIsYearly} className="mr-4" />
            <Text className={cx("-mt-0.5", { "text-primary": isYearly })}>
              Pagamento anual{" "}
              <Text
                className={cx(
                  "ml-2 rounded-full border border-primary-100 bg-background-50 px-2 py-1 text-primary text-sm",
                  {
                    "border-primary-200 bg-background ring-4 ring-primary-500/10": isYearly,
                  },
                )}
              >
                20% de desconto
              </Text>
            </Text>
          </Center>
        </Box>

        {pricingProducts && (
          <Box className="mt-4 gap-6 rounded-2xl border border-primary-800/10 bg-background-dark p-4 md:flex-row">
            {pricingProducts.map((product, productIndex) => {
              const price = getStripePrice(product, isYearly);
              if (!price) return null;

              const PlanIcon = planIcons[productIndex];

              return (
                <Box
                  key={product.id}
                  className={cx("flex-1 overflow-hidden rounded-xl p-4", planStyles[productIndex].card)}
                >
                  {productIndex === pricingProducts.length - 1 && (
                    <Box
                      className="-z-10 -top-[8rem] -right-[8rem] absolute h-[8rem] w-[8rem] rounded-full bg-primary-400"
                      style={{ filter: "blur(8rem)" }}
                    />
                  )}
                  <Box className="w-full flex-row items-center justify-between">
                    <Box>
                      <Heading size="sm" className={planStyles[productIndex].title}>
                        {product.name}
                      </Heading>
                      <Text className={planStyles[productIndex].price}>
                        <Text>R$</Text>
                        <Text className={cx("ml-1 font-bold text-3xl")}>
                          {price.tiers
                            ? getUnitTieredPrice(teamMembers.length, price.tiers)
                            : price.unit_amount && price.unit_amount / 100}
                        </Text>
                        {isYearly ? "/ano" : "/mês"}
                        {product.name !== "Plano Lite" && " por usuário"}
                      </Text>
                    </Box>
                    <PlanIcon className={cx("h-10 w-10 opacity-50", planStyles[productIndex].icon)} />
                  </Box>
                  <Box className="mt-4 w-full">
                    {product.features?.map((feature: StripeProductFeature, index: number) => {
                      const { name, description, icon } = parseFeatureText(feature.name);
                      const IconComponent = icon ? featureIcons[icon] : null;

                      return (
                        <Center className="mb-2 w-fit flex-row" key={index}>
                          {IconComponent && (
                            <IconComponent
                              className={cx("mr-2 h-5 w-5 text-primary", planStyles[productIndex].featureIcon)}
                            />
                          )}
                          <Box className="flex-col">
                            <Text className={cx("text-text-tertiary", planStyles[productIndex].features)}>{name}</Text>
                            {description && (
                              <Text className="mt-1 rounded bg-background-darker px-2 py-1 text-text-secondary text-xs">
                                {description}
                              </Text>
                            )}
                          </Box>
                        </Center>
                      );
                    })}
                  </Box>
                  <Box className={cx("mt-4 w-full rounded-xl border p-4", planStyles[productIndex].buttonCard)}>
                    <Box className="flex-row justify-between px-2">
                      <Text className="text-text-secondary">
                        {teamMembers.length} usuário{teamMembers.length > 1 ? "s" : ""}
                      </Text>
                      <Text className="font-bold text-text-secondary">
                        Total R$
                        {price.tiers
                          ? getTotalTieredPrice(teamMembers.length, price.tiers).toFixed(2)
                          : price.unit_amount && (price.unit_amount / 100).toFixed(2)}
                      </Text>
                    </Box>
                    <Button
                      variant="outline"
                      className={cx("mt-8 w-full")}
                      onPress={() => setSelectedProductIndex(productIndex)}
                    >
                      <Text className="text-text-tertiary">
                        Selecionar <Text className={planStyles[productIndex].buttonPlanName}>{product.name}</Text>
                      </Text>
                    </Button>
                  </Box>
                </Box>
              );
            })}
          </Box>
        )}
      </Box>
    </>
  );
}
