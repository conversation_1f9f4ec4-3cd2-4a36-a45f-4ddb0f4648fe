import AddIcon from "@/assets/icons/add-circle.svg";
import Modal from "@/components/Modal";
import TeamCard from "@/components/TeamCard";
import { Box, Button, Heading, Text } from "@/components/ui";
import { useDrawers } from "@/context/drawer";
import { useSwitchTeamAccount, useTeamAccountsList } from "@/hooks";
import type { FC } from "react";
import { Pressable } from "react-native";

export interface TeamPickerProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTeam?: (teamId: string) => void;
}

const TeamPicker: FC<TeamPickerProps> = ({ isOpen, onClose, onSelectTeam }) => {
  const drawers = useDrawers();
  const switchTeamAccount = useSwitchTeamAccount();
  const { data: teamAccounts = [], isLoading: isUserOrgsLoading, error: userOrgsError } = useTeamAccountsList();

  return (
    <Modal isOpen={isOpen} size="sm" onClose={onClose}>
      <Box className="p-6">
        <Heading>Selecione um negócio</Heading>
        <Text className="mt-1 text-sm text-text-tertiary">Selecione um dos negócios dos quais você é membro:</Text>
        <Box className="mt-4">
          {teamAccounts.map((team) => (
            <Pressable
              key={team.id}
              className="mb-2 rounded-lg border border-border p-4 hover:border-border-dark"
              onPress={() => {
                onClose();
                switchTeamAccount(team.id);
                if (onSelectTeam) onSelectTeam(team.id);
              }}
            >
              <TeamCard name={team.name} role={team.role} avatarUrl={team.avatar_url || ""} />
            </Pressable>
          ))}
          <Pressable
            onPress={() => {
              onClose(); // Close the current modal
              setTimeout(() => {
                drawers.openCreateTeamDrawer(); // Open the create team drawer
              }, 350);
            }}
          >
            <Box className="rounded-lg border border-border-darker border-dashed bg-background-dark p-2 hover:bg-background-darkest">
              <Box className="flex-row items-center justify-center">
                <AddIcon className="mr-1.5 h-5 w-5 text-text-secondary" />
                <Text className="font-bold text-sm text-text-secondary">Criar novo negócio</Text>
              </Box>
            </Box>
          </Pressable>
          <Button size="sm" variant="link" className="mt-4" onPress={onClose}>
            <Text>Cancelar e continuar com a conta atual</Text>
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default TeamPicker;
