import type { AddressAutocompletePrediction, AddressAutocompleteResponse } from "@/types";
import { debounce } from "lodash-es";

export const fetchAddressAutocompleteOptions = debounce(
  async (
    partialAddress: string,
    accessToken: string | undefined,
    callback: (addressOptions: AddressAutocompletePrediction[] | []) => void,
  ) => {
    if (!partialAddress || partialAddress.length < 16) {
      callback([]);
      return;
    }

    try {
      const baseUrl = process.env.EXPO_PUBLIC_API_URL || "";
      const workerUrl = baseUrl.startsWith("http")
        ? `${baseUrl}/addresses/autocomplete`
        : `https://${baseUrl}/addresses/autocomplete`;

      const response = await fetch(workerUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({ query: partialAddress }),
      });

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}`);
      }

      const result = (await response.json()) as AddressAutocompleteResponse;
      // Transform the new API response format to match our existing interface
      const predictions = result?.suggestions.length <= 0 ? [] : result?.suggestions;
      callback(predictions);
    } catch (error) {
      console.error("Address autocomplete error:", error);
      callback([]);
    }
  },
  2000,
);

export const getAutocompleteOptionLabel = (address: AddressAutocompletePrediction) => {
  console.log({ address });
  // Since we're using the new Google Places API, we'll just return the full address
  return address.placePrediction.text.text;
};
