import { Badge, Box, Center, Input, Label, SimpleRadio, Text, ToggleGroup, ToggleGroupItem } from "../ui";
import { useEffect, useRef, useState } from "react";
import { cn } from "@/utils";
import { SkeletonLoader } from "../Skeleton";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import type { AddressAutocompletePrediction } from "@/types";
import { fetchAddressAutocompleteOptions, fetchPlaceDetails, getAutocompleteOptionLabel } from "./utils";
import { ActivityIndicator, type TextInput } from "react-native";
import { useThemeColor } from "@/theme";
import StarIcon from "@/assets/icons/star.svg";

export type AddressAutocompleteProps = {
  label?: string;
  onAddressSelected: (address: string, googlePlaceId: string) => void;
  onAddressSearchStart?: () => void;
  onAddressSearchEnd?: () => void;
};

export const AddressAutocomplete = ({
  label,
  onAddressSelected,
  onAddressSearchStart,
  onAddressSearchEnd,
}: AddressAutocompleteProps) => {
  const autocompleteInputRef = useRef<TextInput>(null);
  const { session } = useSupabaseAuth();
  const [activeAddressSearchQuery, setActiveAddressSearchQuery] = useState<string>("");
  const [selectedAddressIndex, setSelectedAddressIndex] = useState<string>();
  const [addressOptions, setAddressOptions] = useState<AddressAutocompletePrediction[] | []>([]);
  const [isAutocompleteLoading, setIsAutocompleteLoading] = useState(false);
  const primaryColor = useThemeColor({ color: "primary", opacity: 1 });

  useEffect(() => {
    if (autocompleteInputRef.current) {
      setTimeout(() => {
        autocompleteInputRef.current?.focus();
      }, 300);
    }

    return undefined;
  }, []);

  return (
    <Box className="px-12">
      <Box className={selectedAddressIndex ? "pointer-events-none opacity-90" : undefined}>
        <Label className="mb-2">{label || "Buscar por endereço"}</Label>
        <Box className="mb-8 w-full justify-center">
          <Input
            ref={autocompleteInputRef}
            autoFocus={true}
            placeholder="Exemplo: avenida santo amaro 1280, sao paulo"
            size="lg"
            value={activeAddressSearchQuery}
            onChange={(e) => {
              const partialAddress = e.nativeEvent.text;
              setActiveAddressSearchQuery(partialAddress);

              if (partialAddress) {
                onAddressSearchStart?.();
                setIsAutocompleteLoading(true);
                setAddressOptions([]);
                setSelectedAddressIndex(undefined);

                fetchAddressAutocompleteOptions(
                  partialAddress,
                  session?.access_token,
                  (result: AddressAutocompletePrediction[] | []) => {
                    setAddressOptions(result);
                    if (result.length === 0) {
                      onAddressSearchEnd?.();
                    }
                    setIsAutocompleteLoading(false);
                  },
                );
              } else {
                setIsAutocompleteLoading(false);
                onAddressSearchEnd?.();
              }
            }}
          />

          {isAutocompleteLoading && (
            <ActivityIndicator color={primaryColor} size="small" className="absolute right-4" />
          )}
        </Box>

        {(addressOptions.length > 0 || isAutocompleteLoading) && (
          <Box>
            <Label className="mb-4">Selecione um endereço:</Label>
            {isAutocompleteLoading ? (
              <ToggleGroup type="single" value="loader" onValueChange={() => {}} className="pointer-events-none gap-4">
                {(() => {
                  const items: JSX.Element[] = [];
                  for (let i = 0; i < 3; i++) {
                    items.push(
                      <ToggleGroupItem
                        key={`loader-item-${i}`}
                        className={cn(
                          "w-full flex-row gap-4 rounded-lg border-[1px] border-border-lighter bg-background px-4 py-3 shadow-xs transition duration-300 ease-in-out",
                        )}
                        size="custom"
                        value={`loader-item-${i}`}
                        aria-label="Toggle bold"
                      >
                        <SkeletonLoader style={{ borderRadius: 16 }} width={16} height={16} />
                        <SkeletonLoader style={{ borderRadius: 10, flex: 1 }} width={"70%"} height={16} />
                      </ToggleGroupItem>,
                    );
                  }
                  return items;
                })()}
              </ToggleGroup>
            ) : (
              <ToggleGroup
                value={selectedAddressIndex}
                onValueChange={(newAddressIndex) => {
                  if (!newAddressIndex) return;
                  setSelectedAddressIndex(newAddressIndex);

                  const selectedAddress = addressOptions[Number.parseInt(newAddressIndex as string)];

                  onAddressSelected(selectedAddress.placePrediction.text.text, selectedAddress.placePrediction.placeId);
                  setTimeout(() => {
                    onAddressSearchEnd?.();
                  }, 1000);
                }}
                type="single"
                className="w-full flex-column gap-4"
              >
                {addressOptions.map((addressOption, addressOptionIndex) => {
                  const isOptionActive = selectedAddressIndex === `${addressOptionIndex}`;

                  return (
                    addressOptionIndex < 5 && (
                      <Box className="w-full" key={`address-option-${addressOptionIndex}`}>
                        <ToggleGroupItem
                          className={cn(
                            "w-full rounded-lg border-[1px] border-border bg-background px-4 py-3 shadow-xs transition duration-300 ease-in-out",
                            {
                              "web:pointer-events-none web:ring-2 web:ring-primary-500 web:ring-offset-4":
                                isOptionActive,
                              "native:border-primary": isOptionActive,
                              "web:hover:scale-[1.02] web:hover:border-border-darker": !isOptionActive,
                              "web:pointer-events-none scale-[1.03]": isOptionActive,
                            },
                          )}
                          size="custom"
                          value={`${addressOptionIndex}`}
                          aria-label="Toggle bold"
                        >
                          <Box className="flex w-full flex-row items-center gap-2">
                            {addressOptionIndex === 0 && !isOptionActive && addressOptions.length > 1 && (
                              <Badge
                                beforeIcon={StarIcon}
                                variant="primary-outline"
                                className="-bottom-5 absolute right-2"
                                size="2xs"
                              >
                                <Text className="text-xs">Recomendado</Text>
                              </Badge>
                            )}
                            <SimpleRadio checked={isOptionActive} />
                            <Center className="flex-1 flex-row">
                              <Box className="flex-1 flex-column">
                                <Text
                                  className={cn("truncate text-sm text-text-secondary", {
                                    "text-primary-800": isOptionActive,
                                    "font-medium text-primary-950": addressOptionIndex === 0 && !isOptionActive,
                                  })}
                                >
                                  {getAutocompleteOptionLabel(addressOption)}
                                </Text>
                              </Box>
                            </Center>
                          </Box>
                        </ToggleGroupItem>
                      </Box>
                    )
                  );
                })}
              </ToggleGroup>
            )}
          </Box>
        )}
        {addressOptions.length > 0 && (
          <Text className="mt-4 mb-16 px-12 text-center text-text-tertiary text-xs">
            Não encontrou o endereço que procurava? Tente adicionar mais informações como bairro ou cidade no campo de
            busca
          </Text>
        )}
      </Box>
    </Box>
  );
};
