import { Avatar, AvatarFallback, AvatarImage, Badge, Box, Center, Text } from "@/components/ui";
import { ROLES_LABELS, type RoleType } from "@/constants/roles";
import type { GetAccountResponse } from "@/types";
import { getInitials } from "@/utils/getInitials";

export interface TeamCardProps {
  name: string;
  role: RoleType;
  avatarUrl: string;
  darkMode?: boolean;
}

const TeamCard = ({ name, role, avatarUrl, darkMode }: TeamCardProps) => {
  return (
    <Center className="flex-1 flex-row items-center gap-3">
      <Avatar alt={name} className="rounded-full bg-primary-500">
        <AvatarImage source={{ uri: avatarUrl }} />
        <AvatarFallback>
          <Text className="font-bold text-sm text-text-inverse">{getInitials(name)}</Text>
        </AvatarFallback>
      </Avatar>
      <Box className="flex-1">
        <Box className="mb-1 flex-row items-center">
          <Text className="truncate pr-4 font-medium text-sm text-text">{name}</Text>
        </Box>
        <Box className="-mt-0.5 flex-row items-center">
          <Text className="font-medium text-2xs text-text-tertiary">{ROLES_LABELS[role]}</Text>
          <Badge size="2xs" variant="primary-outline" className="ml-2 bg-background">
            <Text>PRO</Text>
          </Badge>
        </Box>
      </Box>
    </Center>
  );
};

export default TeamCard;
