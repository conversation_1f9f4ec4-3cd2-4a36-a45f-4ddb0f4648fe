import ArrowLeftIcon from "@/assets/icons/circle-arrow-left.svg";
import MailSentIllustration from "@/assets/illustrations/storyset/New message-amico.svg";
import { Box, Center, Heading, HStack } from "@/components/ui";
import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { cx } from "@/utils";
// @ts-expect-error - input-otp-native module resolution issue
import type { OTPInputRef } from "input-otp-native";
// @ts-expect-error - input-otp-native module resolution issue
import { OTPInput, type SlotProps } from "input-otp-native";
import * as React from "react";
import Reanimated, {
  FadeIn,
  FadeOut,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
} from "react-native-reanimated";

// OTP components

function FakeDash() {
  return (
    <Box className="w-6 items-center justify-center">
      <Box className="h-0.5 w-3 rounded-sm bg-border-darker" />
    </Box>
  );
}

function Slot({ char, isActive, hasFakeCaret }: SlotProps) {
  return (
    <Box
      className={cx(
        "h-12 w-12 items-center justify-center rounded-lg border border-border-darker bg-background ring-4 ring-background-darker transition-all duration-300 ease-in-out",
        {
          "border-2 border-primary-400 ring-primary-50": isActive,
        },
      )}
    >
      {char !== null && <Text className="font-medium text-gray-900 text-xl">{char}</Text>}
      {hasFakeCaret && <FakeCaret />}
    </Box>
  );
}

function FakeCaret() {
  const opacity = useSharedValue(1);

  React.useEffect(() => {
    opacity.value = withRepeat(
      withSequence(withTiming(0, { duration: 500 }), withTiming(1, { duration: 500 })),
      -1,
      true,
    );
  }, [opacity]);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const baseStyle = {
    width: 2,
    height: 24,
  };

  return (
    <Box className="absolute h-full w-full items-center justify-center">
      <Reanimated.View style={[baseStyle, animatedStyle]}>
        <Box className="h-full w-full rounded-xs bg-primary-900" />
      </Reanimated.View>
    </Box>
  );
}

interface RevoltOTPInputProps {
  value: string;
  onChange: (value: string) => void;
  onComplete?: (value: string) => void;
  error?: string | null;
  onDismissError?: () => void;
}

function RevoltOTPInput({ value, onChange, onComplete, error, onDismissError }: RevoltOTPInputProps) {
  const ref = React.useRef<OTPInputRef>(null);
  const opacity = useSharedValue(1);
  const errorTimerRef = React.useRef<NodeJS.Timeout | null>(null);
  const blinkCountRef = React.useRef(0);

  // Track previous error to detect changes
  const prevErrorRef = React.useRef<string | null | undefined>(null);

  // Simple timeout to focus when mounted
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (ref.current) {
        ref.current.focus();
      }
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Error effect to handle blinking animation and error dismissal
  React.useEffect(() => {
    // Only run when there's an error
    if (!error) return;

    console.log("Error detected:", error);

    // Set up blinking animation
    let blinkCount = 0;
    let timeoutId: NodeJS.Timeout | null = null;

    const performBlink = () => {
      // Fade out
      opacity.value = withTiming(0.3, { duration: 300 }, () => {
        // Fade in
        opacity.value = withTiming(1, { duration: 300 }, () => {
          blinkCount++;
          console.log(`Completed blink ${blinkCount} of 3`);

          if (blinkCount < 3) {
            // Continue blinking
            timeoutId = setTimeout(performBlink, 50);
          } else {
            // After 3 blinks, wait then dismiss
            console.log("All 3 blinks completed, will dismiss shortly");
            timeoutId = setTimeout(() => {
              console.log("Dismissing error now");

              // Reset input
              onChange("");

              // Focus input
              if (ref.current?.focus) {
                ref.current.focus();
              }

              // Finally dismiss the error
              if (onDismissError) {
                onDismissError();
              }

              // Reset blink count
              blinkCountRef.current = 0;
              // Reset opacity
              opacity.value = 1;
              // Reset previous error
              prevErrorRef.current = null;
              // Reset error
              errorTimerRef.current = null;

              ref.current.clear();
            }, 1500);
          }
        });
      });
    };

    // Start the animation
    performBlink();

    // Cleanup function
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [error, opacity, onChange, onDismissError]);

  const handleComplete = (code: string) => {
    console.log({ code });
    if (onComplete) {
      onComplete(code);
    }
  };

  // Create animated style for the error box
  const errorAnimatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  return (
    <Box>
      <OTPInput
        ref={ref}
        value={value}
        onChange={onChange}
        onComplete={handleComplete}
        maxLength={6}
        autoFocus
        render={({ slots }: any) => (
          <Box className="my-4 flex-row justify-center gap-3">
            {slots.map((slot: any, idx: number) => (
              <Slot key={`otp-slot-${idx}`} {...slot} />
            ))}
          </Box>
        )}
      />
      {error && (
        <Reanimated.View style={errorAnimatedStyle}>
          <Box className="-ml-[3%] w-[106%] flex-row items-center rounded border border-error-700/20 bg-error-50 p-2">
            {/* <CircularProgressBar progress={0.3} size={20} variant="error" strokeWidth={4} /> */}
            <Text className="ml-3 text-error-900 text-sm">{error}</Text>
          </Box>
        </Reanimated.View>
      )}
    </Box>
  );
}

// The main OTP component
interface OTPComponentProps {
  email: string;
  otpValue: string;
  onOtpChange: (value: string) => void;
  onSubmit: (otp: string) => void;
  onCancel: () => void;
  onResend: () => void;
  error: string | null;
  onDismissError: () => void;
  isVerifying: boolean;
  isResending: boolean;
  countdown: number;
}

export default function OTPComponent({
  email,
  otpValue,
  onOtpChange,
  onSubmit,
  onCancel,
  onResend,
  error,
  onDismissError,
  isVerifying,
  isResending,
  countdown,
}: OTPComponentProps) {
  return (
    <Reanimated.View
      entering={FadeIn.duration(300).delay(300)}
      exiting={FadeOut.duration(300)}
      style={{
        display: "flex",
        height: "100%",
        width: "100%",
        alignItems: "center",
        justifyContent: "center",
        position: "absolute",
      }}
    >
      <Center className="mb-8">
        <MailSentIllustration className="mb-4 h-72 w-72" />
        <Heading size="xl" className="mb-4 font-medium text-gray-900">
          Verifique seu email
        </Heading>
        <Text className="max-w-[60%] text-center text-text-secondary leading-5">
          Digite abaixo o <Text className="font-medium text-text">Código de 6 digitos</Text> que enviamos para o seu
          email <Text className="font-medium text-text">{email}</Text>.
        </Text>
      </Center>

      <Reanimated.View className="w-full max-w-[360px] gap-6">
        <RevoltOTPInput
          value={otpValue}
          onChange={onOtpChange}
          onComplete={onSubmit}
          error={error}
          onDismissError={onDismissError}
        />
        <HStack className="w-full items-center justify-center space-x-4">
          <Button variant="destructive-link" onPress={onCancel} disabled={isVerifying}>
            <ArrowLeftIcon className="h-5 w-5 text-error" />
            <Text className="ml-2">Cancelar</Text>
          </Button>

          <Button
            variant="outline"
            onPress={onResend}
            isLoading={isResending || countdown > 0}
            loadingMessage={isResending ? "Reenviando código..." : undefined}
            disabled={isVerifying || countdown > 0}
          >
            <Text>{countdown > 0 ? `Reenvio do código é possível em ${countdown}s` : "Enviar novo código"}</Text>
          </Button>
        </HStack>
      </Reanimated.View>
    </Reanimated.View>
  );
}
