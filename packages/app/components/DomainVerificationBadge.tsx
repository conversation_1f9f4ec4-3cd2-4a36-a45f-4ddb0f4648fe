import RecordIcon from "@/assets/icons/solid/record.svg";
import { Badge, Box, Text } from "@/components/ui";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { useDomainVerificationStatus } from "@/hooks/useDomainVerificationStatus";
import type { DomainVerificationStatus } from "@/types/websites";
import { useEffect, useState } from "react";

interface DomainVerificationBadgeProps {
  domainId: string;
  initialStatus: DomainVerificationStatus;
}

export function DomainVerificationBadge({ domainId, initialStatus }: DomainVerificationBadgeProps) {
  const [status, setStatus] = useState<DomainVerificationStatus>(initialStatus);

  // Fetch the latest verification status
  const { data, isLoading, isError } = useDomainVerificationStatus({
    domainId,
    enabled: true,
    refetchInterval: status === "pending" ? 60000 : false, // Refetch every minute if pending
  });

  // Update the status when data is received
  useEffect(() => {
    if (data?.domain?.verification_status) {
      setStatus(data.cloudflare_data.status);
    }
  }, [data]);

  // Determine the badge variant based on status
  const getBadgeVariant = () => {
    if (isError) return "outline";
    if (isLoading) return "outline";

    switch (status) {
      case "verified":
        return "primary-outline";
      case "active":
        return "success";
      case "failed":
        return "danger";
      default:
        return "outline";
    }
  };

  // Get the display text based on status
  const getStatusText = () => {
    if (isLoading) return "Verificando...";
    if (isError) return "Erro ao verificar";

    switch (status) {
      case "verified":
        return "Verificado";
      case "failed":
        return "Falha na verificação";
      case "active":
        return "Ativo";
      default:
        return "Configuração pendente";
    }
  };

  // Generate verification instructions based on Cloudflare data
  const getVerificationInstructions = () => {
    if (!data?.cloudflare_data) return null;

    const result = data.cloudflare_data;

    // Check for TXT verification
    if (result.ownership_verification && result.ownership_verification.type === "txt") {
      return {
        type: "txt",
        instructions: `Adicione um registro TXT com nome ${result.ownership_verification.name} e valor ${result.ownership_verification.value} nas configurações de DNS.`,
      };
    }

    // Check for HTTP verification
    if (result.ownership_verification_http) {
      return {
        type: "http",
        instructions: `Crie um arquivo em ${result.ownership_verification_http.http_url} com o conteúdo: ${result.ownership_verification_http.http_body}`,
      };
    }

    // Check SSL validation records if available
    if (result.ssl?.validation_records && result.ssl.validation_records.length > 0) {
      const record = result.ssl.validation_records[0];

      if (record.txt_name && record.txt_value) {
        return {
          type: "txt",
          instructions: `Adicione um registro TXT com nome ${record.txt_name} e valor ${record.txt_value} nas configurações de DNS.`,
        };
      }

      if (record.http_url && record.http_body) {
        return {
          type: "http",
          instructions: `Crie um arquivo em ${record.http_url} com o conteúdo: ${record.http_body}`,
        };
      }
    }

    return null;
  };

  console.log({ status });

  const instructions = getVerificationInstructions();
  const hasInstructions = status === "pending" && instructions !== null;

  return (
    <Tooltip>
      <TooltipTrigger>
        <Badge beforeIcon={RecordIcon} className="ml-2 w-fit" variant={getBadgeVariant()} size="xs" rounded="full">
          <Text>{getStatusText()}</Text>
        </Badge>
      </TooltipTrigger>
      {hasInstructions && (
        <TooltipContent>
          <Box className="max-w-xs p-2">
            <Text className="mb-1 font-medium">Instruções de verificação:</Text>
            <Text className="text-xs">{instructions?.instructions}</Text>
          </Box>
        </TooltipContent>
      )}
    </Tooltip>
  );
}
