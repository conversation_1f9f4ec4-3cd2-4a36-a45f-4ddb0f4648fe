import { useFormContext } from "react-hook-form";
import {
  Box,
  Center,
  FormField,
  FormInput,
  Label,
  SimpleRadio,
  Text,
  ToggleGroup,
  ToggleGroupItem,
} from "@/components/ui";
import BuildingAlt from "@/assets/icons/building-06.svg";
import ManagerIcon from "@/assets/icons/manager.svg";
import UserIcon from "@/assets/icons/user-02.svg";
import { cn, cx } from "@/utils";
import { Platform } from "react-native";
import type { FC, SVGProps } from "react";
import DrawerHeader from "@/components/Drawer/DrawerHeader";
import NewReleasesIcon from "@/assets/icons/new-releases.svg";

type BusinessType = {
  name: string;
  value: "agent" | "agency" | "owner";
  description: string;
  icon: FC<SVGProps<SVGSVGElement>>;
};

const businessTypes: BusinessType[] = [
  {
    name: "Corretor",
    description: "Você é um corretor profissional que vende ou aluga imóveis diretamente para seus clientes.",
    value: "agent",
    icon: ManagerIcon,
  },
  {
    name: "Imobiliária",
    value: "agency",
    description: "Você é dono ou faz parte de uma imobiliária onde 2 ou mais corretores vendem ou alugam imóveis.",
    icon: BuildingAlt,
  },
  {
    name: "Proprietário",
    value: "owner",
    description:
      "Você é proprietário de imóveis, que aluga ou vende seus próprios imóveis diretamente para seus clientes.",
    icon: UserIcon,
  },
];

const getBusinessTypeByValue = (value?: string) => {
  return value ? businessTypes.find((type) => type.value === value) : undefined;
};

const DOMAIN_PREFIX = ["@"];
const SLUG_PATTERN = /[a-z0-9_-]/i;

const normalizeText = (text: string) => {
  return text
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "") // Remove diacritics
    .toLowerCase();
};

const generateMask = (prefixChars: string[], pattern: RegExp, length: number) => {
  return [...prefixChars, ...Array(length).fill(pattern)];
};

export default function OrganizationStep() {
  const {
    control,
    register,
    setValue,
    watch,
    formState: { isValid },
  } = useFormContext();

  const businessType = watch("type");

  // const submitOrganization = (data: z.infer<typeof formSchema>) => {
  //   // @ts-ignore
  //   form.reset();
  // };

  // const finishOnboarding = (values: z.infer<typeof formSchema>) => {};

  return (
    <>
      <DrawerHeader
        title="Perfil de negócio"
        description="Configure sua conta de negócios para visualizar o painel"
        icon={NewReleasesIcon}
      />
      <Center className="h-full w-full max-w-[100%] native:p-6 pt-16">
        <Center className="w-full max-w-[1100px] flex-row">
          <Box className="flex-1 flex-column gap-12">
            <FormField
              control={control}
              name="name"
              render={({ field }) => (
                <FormInput
                  className="w-full"
                  autoFocus={Platform.OS === "web"}
                  label="Qual é o nome do seu negócioo?"
                  placeholder="Exemplo: Imobiliária Benifica ou Joca Negócios Imobiliários"
                  {...field}
                />
              )}
            />
            <FormField
              control={control}
              name="slug"
              render={({ field }) => (
                <FormInput
                  className="w-full lowercase"
                  label="Escolha um nome de usuário para o seu negócio"
                  description="O nome de usuário ajudará a identificar o seu negócio na plataforma."
                  placeholder="Exemplo: imobiliaria-benfica ou corretor-joca"
                  mask={generateMask(DOMAIN_PREFIX, SLUG_PATTERN, 40)}
                  onChangeText={(masked, unmasked, obfuscated) => {
                    field.onChange(normalizeText(unmasked));
                  }}
                  value={field.value}
                  name={field.name}
                  onChange={field.onChange}
                  onBlur={field.onBlur}
                />
              )}
            />
            <Box>
              <Label className="mb-2">Selecione a opção que melhor descreve o seu negócio:</Label>
              <Text className="mb-4 text-sm text-text-tertiary">
                Personalizaremos o seu painel de negócio com base no seu tipo de negócio
              </Text>

              <ToggleGroup
                value={businessType}
                onValueChange={(selectedBusinessType) => {
                  setValue("type", selectedBusinessType, {
                    shouldDirty: true,
                    shouldTouch: true,
                    shouldValidate: true,
                  });
                }}
                type="single"
                className="w-full flex-column gap-4"
              >
                {businessTypes.map((type) => (
                  <ToggleGroupItem
                    className={cn(
                      "w-full rounded-2xl border-[1px] border-border bg-background px-6 py-4 shadow-[0_1.5px_0px_0_rgba(var(--color-border-light))] transition duration-300 ease-in-out",
                      {
                        "pointer-events-none border-primary-400 bg-primary/5 shadow-[0_1.5px_0px_0_rgba(var(--color-border-light)),inset_0_0_0_0.5px_rgba(var(--color-primary-400))]":
                          type.value === businessType,
                        "web:hover:scale-[1.01] web:hover:border-border-darker": type.value !== businessType,
                      },
                    )}
                    size="custom"
                    value={type.value}
                    aria-label="Toggle bold"
                    key={type.value}
                  >
                    <Box className="flex w-full flex-row items-center gap-2">
                      {/* <SimpleRadio checked={type.value === businessType} /> */}
                      <Center className="flex-1 flex-row">
                        <Center
                          className={cn(
                            "h-[48px] w-[48px] rounded-xl border border-border-lighter bg-background-darker transition-all duration-300 ease-in-out",
                            {
                              "bg-background": type.value === businessType,
                            },
                          )}
                        >
                          <type.icon
                            className={cx("h-6 w-6 text-text", { "text-primary": type.value === businessType })}
                          />
                        </Center>
                        <Box className="ml-4 flex-1 flex-column">
                          <Text
                            className={cn("mb-0.5 font-medium text-text-secondary", {
                              "text-text": type.value === businessType,
                            })}
                          >
                            {type.name}
                          </Text>
                          <Text className="font-base text-sm text-text-tertiary">{type.description}</Text>
                        </Box>
                      </Center>
                    </Box>
                  </ToggleGroupItem>
                ))}
              </ToggleGroup>
            </Box>
          </Box>
        </Center>
      </Center>
    </>
  );
}
