import { useSwitchTeamAccount } from "@/hooks";
import { useCreateTeamAccount } from "@/hooks/mutations";
import { useTeamAccountsList } from "@/hooks/useTeamAccountsList";
import { useState } from "react";
import { useForm } from "react-hook-form";
import Drawer, { DrawerFooter } from "../Drawer";
import { Box, Form } from "../ui";
import { defaultValues, type FormFields, resolver } from "./form";
import OrganizationStep from "./OrganizationStep";
import TeamCreationSuccess from "./TeamCreationSuccess";

export const CreateNewTeam = ({
  mode = "default",
  isOpen = false,
  onClose,
}: {
  mode?: "default" | "drawer";
  isOpen?: boolean;
  onClose?: () => void;
}) => {
  const [isCreationSuccess, setIsCreationSuccess] = useState(false);
  const [createdTeamId, setCreatedTeamId] = useState<string | null>(null);

  const createTeamAccount = useCreateTeamAccount();
  const switchAccount = useSwitchTeamAccount();
  const { refetch } = useTeamAccountsList();

  const onboardingForm = useForm<FormFields>({
    resolver,
    defaultValues,
  });
  onboardingForm.register("name");
  onboardingForm.register("type");

  const onSubmit = onboardingForm.handleSubmit(async (data) => {
    try {
      const { data: createdTeamAccount } = await createTeamAccount.mutateAsync({
        ...data,
        slug: data.slug || data.name.toLowerCase().replace(/\s+/g, "-"),
      });

      console.log("Team created successfully:", createdTeamAccount);

      // The API response structure is different from what we expected
      // Extract the team ID from the response
      const teamId = createdTeamAccount?.id;

      if (!teamId) {
        console.error("Failed to get team ID from response:", createdTeamAccount);
        throw new Error("Failed to get team ID from response");
      }

      // Store the new team ID
      setCreatedTeamId(teamId);

      // First show success screen
      setIsCreationSuccess(true);

      // Then switch to the new team account without redirecting
      // We're passing a callback to prevent the default redirect behavior
      switchAccount(teamId, () => {
        // After switching, refetch the team accounts list
        refetch();
      });
    } catch (error: unknown) {
      console.error("Failed to create team account:", error);
      onboardingForm.setError("root", {
        type: "submit",
        message: error instanceof Error ? error.message : "Failed to create team account",
      });
    }
  });

  const dirtyFields = onboardingForm.formState.dirtyFields;

  const formProgress = {
    1: [
      { isComplete: !!dirtyFields.name, percentage: 40 },
      { isComplete: !!dirtyFields.type, percentage: 40 },
      { isComplete: onboardingForm.formState.isValid, percentage: 10 },
      { isComplete: true, percentage: 10 },
    ],
  };

  const consolidatedProgress = formProgress[1].reduce((acc, curr) => {
    return acc + (curr.isComplete ? curr.percentage : 0);
  }, 0);

  if (mode === "drawer") {
    return isOpen ? (
      <Drawer
        title={isCreationSuccess ? "" : "Complete seu cadastro"}
        isOpen={true}
        size="sm"
        onClose={onClose}
        steps={1}
        currentStep={1}
        currentStepProgress={isCreationSuccess ? 100 : consolidatedProgress}
      >
        {isCreationSuccess ? (
          <TeamCreationSuccess teamId={createdTeamId} onClose={onClose} />
        ) : (
          <Form {...onboardingForm}>
            <Box className="flex-1 p-6">
              <OrganizationStep />
            </Box>
            <DrawerFooter
              primaryAction={{
                disabled: !onboardingForm.formState.isValid || createTeamAccount.isPending,
                label: createTeamAccount.isPending ? "Criando..." : "Concluir cadastro",
                onPress: onSubmit,
              }}
              tertiaryAction={{
                label: "Cancelar",
                onPress: () => onClose?.(),
              }}
            />
          </Form>
        )}
      </Drawer>
    ) : null;
  }

  return (
    <Form {...onboardingForm}>
      <OrganizationStep />
    </Form>
  );
};

export default CreateNewTeam;
