import * as React from "react";
import { TextInput } from "react-native";
import { cn } from "@/utils";

const Textarea = React.forwardRef<React.ElementRef<typeof TextInput>, React.ComponentPropsWithoutRef<typeof TextInput>>(
  ({ className, multiline = true, numberOfLines = 4, placeholderClassName, ...props }, ref) => {
    return (
      <TextInput
        ref={ref}
        className={cn(
          "web:flex min-h-[80px] w-full rounded-lg border border-border bg-background px-4 py-3 font-base native:text-lg text-text native:leading-[1.25] shadow-xs transition-all duration-150 ease-in-out file:border-0 file:bg-transparent file:font-medium placeholder:text-text-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-400/80 focus-visible:ring-offset-2",
          props.editable === false && "web:cursor-not-allowed opacity-50",
          className,
        )}
        placeholderClassName={cn("text-text-placeholder", placeholderClassName)}
        multiline={multiline}
        numberOfLines={numberOfLines}
        textAlignVertical="top"
        {...props}
      />
    );
  },
);

Textarea.displayName = "Textarea";

export { Textarea };
