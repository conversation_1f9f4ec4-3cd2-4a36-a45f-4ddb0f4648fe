import { cn } from "@/utils";
import * as React from "react";
import { Center } from "./center";
import { Box } from "./box";

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
type ComponentProps<T extends React.ElementType<any>> =
	React.ComponentPropsWithoutRef<T> & {
		indicatorClassName?: string;
		checked?: boolean;
	};
type CenterRef = React.ElementRef<typeof Center>;
type SlottableViewProps = ComponentProps<typeof Center>;

const SimpleRadio = React.forwardRef<CenterRef, SlottableViewProps>(
	({ className, indicatorClassName, checked, ...props }, ref) => {
		return (
			<Center
				className={cn(
					"h-[12px] w-[12px] rounded-full border-[1px] border-border-darkest shadow-xs transition duration-300 ease-in-out",
					{ "border-primary": checked },
					className,
				)}
				ref={ref}
				{...props}
			>
				<Box
					className={cn(
						"h-[8px] w-[8px] scale-0 rounded-full bg-primary opacity-0 transition duration-300 ease-in-out",
						{ "scale-100": checked, "opacity-100": checked },
						indicatorClassName,
					)}
				/>
			</Center>
		);
	},
);
SimpleRadio.displayName = "SimpleRadio";

export { SimpleRadio };
