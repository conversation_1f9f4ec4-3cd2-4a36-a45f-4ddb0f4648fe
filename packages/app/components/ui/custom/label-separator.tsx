import { cn, cx } from "@/utils";
import * as React from "react";
import { Center } from "./center";
import { Separator } from "../separator";
import { Text } from "../text";

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
type ComponentPropsWithLabel<T extends React.ElementType<any>> = React.ComponentPropsWithoutRef<T> & {
  label?: string;
  labelPosition?: "left" | "right" | "center";
  variant?: "filled" | "border";
  size?: "xs" | "sm" | "md";
};
type CenterRef = React.ElementRef<typeof Center>;
type SlottableViewProps = ComponentPropsWithLabel<typeof Center>;

const LabelSeparator = React.forwardRef<CenterRef, SlottableViewProps>(
  ({ className, variant = "filled", label = null, labelPosition = "center", size = "sm", ...props }, ref) => {
    return (
      <Center
        className={cx(
          "w-full flex-row",
          { "justify-start border-border-light border-y bg-background-dark py-2": variant === "filled" },
          className,
        )}
        ref={ref}
        {...props}
      >
        {labelPosition !== "left" && variant !== "filled" && <Separator className="w-[unset] flex-1" />}
        {label && (
          <Text
            className={cn("mx-4 font-medium text-text-tertiary uppercase", {
              "text-2xs": size === "xs",
              "text-xs": size === "sm",
              "text-sm": size === "md",
            })}
          >
            {label}
          </Text>
        )}
        {labelPosition !== "right" && variant !== "filled" && <Separator className="w-[unset] flex-1" />}
      </Center>
    );
  },
);
LabelSeparator.displayName = "LabelSeparator";

export { LabelSeparator };
