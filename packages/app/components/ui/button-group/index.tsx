import * as React from "react";
import type { View } from "react-native";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/utils";
import type { VariantProps } from "class-variance-authority";
import { Text } from "../text";
import { Box } from "../custom";

export interface ButtonGroupItem extends Omit<React.ComponentProps<typeof Button>, "className"> {
  label: string;
}

interface ButtonGroupProps extends React.ComponentPropsWithoutRef<typeof View> {
  buttons: ButtonGroupItem[];
  variant?: VariantProps<typeof Button>["variant"];
  size?: VariantProps<typeof Button>["size"];
  rounded?: VariantProps<typeof Button>["rounded"];
  activeButtonIndex?: number;
}

export const ButtonGroup = React.forwardRef<React.ElementRef<typeof View>, ButtonGroupProps>(
  (
    { className, buttons, variant = "default", size = "default", rounded = "full", activeButtonIndex, ...props },
    ref,
  ) => {
    return (
      <Box
        ref={ref}
        className={cn(
          "flex flex-row border border-border-light bg-background-darker p-1",
          `rounded-${rounded}`,
          className,
        )}
        {...props}
      >
        {buttons.map((button, index) => {
          const isFirst = index === 0;
          const isLast = index === buttons.length - 1;
          const isMiddle = !isFirst && !isLast;
          const isActive = activeButtonIndex === index;

          const customButtonClasses = cn({
            // "rounded-r-none border-r": !isLast,
            // "rounded-l-none": !isFirst,
            "-ml-px": isMiddle || isLast,
            "pointer-events-none": isActive,
          });

          return (
            <Button
              key={index}
              variant={isActive ? variant : "ghost"}
              size={size}
              rounded={rounded || size}
              className={customButtonClasses}
              {...button}
            >
              <Text>{button.label}</Text>
            </Button>
          );
        })}
      </Box>
    );
  },
);

ButtonGroup.displayName = "ButtonGroup";
