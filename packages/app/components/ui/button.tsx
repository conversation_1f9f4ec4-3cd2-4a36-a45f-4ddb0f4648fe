import { Text, TextClassContext } from "@/components/ui/text";
import { useThemeColor } from "@/theme";
import { cn, cx } from "@/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { cssInterop } from "nativewind";
import * as React from "react";
import { ActivityIndicator, Pressable, type PressableProps } from "react-native";
import { View } from "react-native";

cssInterop(ActivityIndicator, {
  className: {
    target: "style",
    nativeStyleToProp: { color: true },
  },
});

const buttonVariants = cva(
  "transition-all duration-300 ease-in-out group flex flex-row items-center justify-center web:focus-visible:ring-ring web:focus-visible:ring-offset-2 active:hover:scale-[0.98] hover:scale-[1.01]",
  {
    variants: {
      variant: {
        default: "bg-background-inverse hover:bg-background-inverse/75",
        brand:
          "border shadow-[inset_1px_0_0.5px_0_var(--color-primary-600),inset_0_1px_0.5px_0_var(--color-primary-500),inset_-1px_0_0.5px_0_var(--color-primary-600),inset_0_-1px_0.5px_0_var(--color-primary-700)] border-primary-700 bg-primary",
        destructive: "bg-error-600",
        "destructive-link": "",
        outline:
          "border border-border-light bg-background hover:opacity-60 active:bg-dark web:shadow-xs",
        "destructive-outline": "border border-error-950/20 bg-error-50 web:hover:bg-error-950/5 active:bg-error-600",
        outlinePrimary: "border border-primary-100 bg-background web:hover:bg-brand-600 active:bg-brand-600",
        secondary: "bg-background-darkest/60 hover:bg-background-darkest border border-border-light",
        ghost: "web:hover:bg-accent web:hover:text-accent-foreground active:bg-accent",
        link: "",
      },
      size: {
        "2xs": "h-5 px-1",
        xs: "h-7 px-2.5",
        sm: "h-9 px-3.5",
        md: "h-10 px-5",
        lg: "h-12 px-6",
      },
      rounded: {
        "2xs": "rounded-sm",
        xs: "rounded-sm",
        sm: "rounded-md",
        md: "rounded-lg",
        lg: "rounded-xl",
        xl: "rounded-xl",
        full: "rounded-full",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      rounded: "md",
    },
  },
);

const buttonTextVariants = cva(
  "web:whitespace-nowrap flex-row text-sm native:text-base text-text transition-all duration-150 ease-in-out",
  {
    variants: {
      variant: {
        default: "text-text-on-brand",
        destructive: "text-[#FFF]",
        "destructive-link": "text-error group-hover:underline web:underline-offset-4 group-hover:focus:underline",
        outline: "group-active:text-text text-text-secondary",
        "destructive-outline": "text-primary",
        outlinePrimary: "text-primary",
        secondary: "text-secondary-foreground group-active:text-secondary-foreground",
        ghost: "text-text-secondary",
        link: "text-text-secondary group-hover:underline web:underline-offset-4 group-hover:focus:underline",
      },
      size: {
        default: "text-sm",
        "2xs": "text-xs",
        xs: "text-xs",
        sm: "text-sm",
        md: "text-sm",
        lg: "text-md",
        xl: "text-lg",
        icon: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

type ButtonProps = React.ComponentPropsWithoutRef<typeof View> & {
  disabled?: boolean;
  buttonTextClassName?: string;
  asChild?: boolean;
  isLoading?: boolean;
  loadingMessage?: string;
} & VariantProps<typeof buttonVariants> &
  VariantProps<typeof buttonTextVariants>;

const ButtonBase = React.forwardRef<React.ElementRef<typeof Pressable>, ButtonProps>(
  ({ className, buttonTextClassName, variant, size, rounded, asChild, isLoading, loadingMessage, ...props }, ref) => {
    return (
      <TextClassContext.Provider
        value={cn(
          (loadingMessage && isLoading) || props.disabled ? "pointer-events-none" : "",
          buttonTextVariants({ variant, size }),
          buttonTextClassName,
        )}
      >
        {asChild ? (
          <View
            className={cx(
              buttonVariants({ variant, size, rounded: rounded || size, className }),
              (isLoading || props.disabled) && "pointer-events-none opacity-50 hover:opacity-50 active:opacity-50",
              "overflow-hidden",
            )}
            ref={ref}
            role="button"
            {...props}
          />
        ) : (
          <Pressable
            className={cx(
              buttonVariants({ variant, size, rounded: rounded || size, className }),
              (isLoading || props.disabled) && "pointer-events-none opacity-50 hover:opacity-50 active:opacity-50",
              "overflow-hidden",
            )}
            ref={ref}
            role="button"
            {...props}
          />
        )}
      </TextClassContext.Provider>
    );
  },
);

const Button = React.forwardRef<React.ElementRef<typeof Pressable>, ButtonProps & PressableProps>(
  ({ children, ...props }, ref) => {
    const primaryColor = useThemeColor({ color: "primary", opacity: 1 });
    const primary600Color = useThemeColor({ color: "primary-600", opacity: 0.25 });
    const primary500Color = useThemeColor({ color: "primary-500", opacity: 0.25 });
    const gray50 = useThemeColor({ color: "gray-50", opacity: 0.5 });

    const spinnerColorMap = {
      primary: "#FFFFFF",
      outline: primaryColor,
    };

    const spinnerColor = props.variant
      ? spinnerColorMap[props.variant as keyof typeof spinnerColorMap]
      : spinnerColorMap.primary;

    return (
      <ButtonBase {...props} ref={ref}>
        {props.isLoading && props.loadingMessage ? (
          <>
            <ActivityIndicator size="small" color={spinnerColor} style={{ marginRight: 16 }} />
            <Text>{props.loadingMessage}</Text>
          </>
        ) : (
          children
        )}
      </ButtonBase>
    );
  },
);

Button.displayName = "Button";

export { Button };
export type { ButtonProps };
export type ButtonVariants = VariantProps<typeof buttonVariants> & VariantProps<typeof buttonTextVariants>;
