import { cva } from "@/utils";

export const inputVariants = cva({
  base: [
    "web:flex",
    "rounded",
    "border",
    "border-border",
    "text-sm",
    "font-base",
    "native:text-lg",
    "text-text",
    "native:leading-[1.25]",
    "shadow-xs",
    "transition-all",
    "duration-150",
    "ease-in-out",
    "file:border-0",
    "file:bg-transparent",
    "file:font-medium",
    "placeholder:text-text-placeholder",
  ],
  variants: {
    variant: {
      default: [
        "bg-background",
        "focus-visible:border",
        "focus-visible:border-gray-500",
        "focus-visible:outline-none",
        "focus-visible:ring-4",
        "focus-visible:ring-gray-500/25",
        "focus-visible:ring-offset-0",
      ],
      outline: ["border", "border-input", "bg-transparent", "web:hover:bg-accent", "active:bg-accent"],
    },
    size: {
      xs: ["h-7", "p-2 py-0 text-xs"],
      sm: ["h-9", "p-3 py-0"],
      md: ["h-10", "p-4 py-0 text-md"],
      lg: ["h-12", "p-5 py-0"],
      xl: ["h-14", "p-6 py-0"],
    },
    beforeContentSize: {
      xs: ["pl-8"],
      sm: ["pl-9"],
      default: ["pl-10"],
      lg: ["pl-16"],
      xl: ["pl-24"],
      "2xl": ["pl-36"],
      "3xl": ["pl-42"],
      none: "",
    },
    afterContentSize: {
      xs: ["pl-8"],
      sm: ["pl-9"],
      default: ["pl-10"],
      lg: ["pl-16"],
      xl: ["pl-24"],
      "2xl": ["pl-36"],
      "3xl": ["pl-42"],
      none: "",
    },
  },
  defaultVariants: {
    variant: "default",
    size: "default",
    beforeContentSize: "none",
    afterContentSize: "none",
  },
});

export const inputIconVariants = cva({
  base: ["text-text-secondary"],
  variants: {
    size: {
      xs: ["w-3", "h-3"],
      sm: ["w-3", "h-3"],
      default: ["w-4", "h-4"],
      lg: ["w-5", "h-5"],
      xl: ["w-6", "h-6"],
    },
  },
  defaultVariants: {
    size: "default",
  },
});

export const inputIconContentContainerVariants = cva({
  base: ["pointer-events-none", "absolute", "top-0", "left-0", "h-full", "p-[1px]"],
  variants: {
    size: {
      xs: "w-8",
      sm: "w-9",
      default: "w-10",
      lg: "w-16",
      xl: "w-24",
      "2xl": "w-36",
      "3xl": "w-42",
    },
  },
  defaultVariants: {
    size: "default",
  },
});
