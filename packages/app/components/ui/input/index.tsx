import PlusIcon from "@/assets/icons/add.svg";
import MinusIcon from "@/assets/icons/remove.svg";
import { cx } from "@/utils";
import type { VariantProps } from "class-variance-authority";
import * as React from "react";
import { Pressable, type PressableProps, TextInput, type TextInputProps } from "react-native";
import MaskInput, { MaskInputProps } from "react-native-mask-input";
import { createNumberMask, type Mask, Masks } from "react-native-mask-input";
import type { SvgProps } from "react-native-svg";
import { Box } from "../custom/box";
import { Center } from "../custom/center";
import { Separator } from "../separator";
import { inputIconContentContainerVariants, inputIconVariants, inputVariants } from "./style";

// Extract the variant types from inputVariants
type InputVariantProps = VariantProps<typeof inputVariants>;

const baseMasks: Record<string, Mask> = {
  currency: Masks.BRL_CURRENCY,
  number: createNumberMask({}),
};

const NumberActionButton = ({ action, ...props }: PressableProps & { action: "increment" | "decrement" }) => {
  const ActionIcon = action === "increment" ? PlusIcon : MinusIcon;
  return (
    <Pressable
      className="flex-1 items-center justify-center rounded-tr-md border-border border-l-0 border-none bg-transparent pt-[2px] hover:bg-accent hover:text-accent-foreground disabled:pointer-events-none disabled:opacity-50"
      {...props}
    >
      <Center className="text-muted-foreground">
        <ActionIcon
          className={`absolute h-2 w-2 text-text-tertiary ${action === "increment" ? "mb-[3px]" : "mt-[2px]"}`}
        />
      </Center>
    </Pressable>
  );
};

export const getPlaceholderClassName = ({
  editable,
  otherClassNames,
  size,
}: {
  editable?: boolean;
  otherClassNames?: string;
  size?: string;
}) => {
  return cx(
    `text-muted-foreground text-${size || "base"} placeholder:text-muted-foreground`,
    editable === false && "web:cursor-not-allowed opacity-80 pointer-events-none",
    otherClassNames,
  );
};

// Define a union type that can be either TextInput or MaskInput
type InputRef = TextInput | typeof MaskInput;

// Define the props for our Input component
type InputProps = {
  inputType?: keyof typeof baseMasks;
  beforeContent?: React.ReactNode;
  beforeIcon?: React.FC<SvgProps>;
  afterContent?: React.ReactNode;
  afterIcon?: React.FC<SvgProps>;
  fullWidth?: boolean;
  numberStep?: number;
  min?: number;
  max?: number;
  onChange?: (masked: string, unmasked: string, obfuscated: string) => void;
  editable?: boolean;
  raw?: boolean;
  mask?: Mask;
  value?: string;
  onChangeText?: (text: string, rawText: string, maskedText: string) => void;
  className?: string;
  placeholderClassName?: string;
  beforeContentSize?: InputVariantProps["beforeContentSize"];
  afterContentSize?: InputVariantProps["afterContentSize"];
} & Omit<TextInputProps, "ref"> &
  InputVariantProps;

const Input = React.forwardRef<InputRef, InputProps>(
  (
    {
      className,
      variant,
      size = "md",
      inputType,
      fullWidth = false,
      placeholderClassName,
      numberStep = 1,
      min = 0,
      max = Number.POSITIVE_INFINITY,
      raw = false,
      ...props
    },
    ref,
  ) => {
    const isInputNumeric = inputType === "number" || inputType === "currency";

    const beforeContentSize = props.beforeContentSize ? props.beforeContentSize : props.beforeIcon ? size : "none";
    const afterContentSize = props.afterContentSize ? props.afterContentSize : props.afterIcon ? size : "none";

    const handleIncrement = () => {
      if (!props.onChangeText || !isInputNumeric) return;

      const newValue = Number.parseFloat(props.value || "0") + numberStep;
      if (newValue <= max) {
        console.log("increment value", newValue);
        props.onChangeText(newValue.toString(), newValue.toString(), newValue.toString());
      }
    };

    const handleDecrement = () => {
      if (!props.onChangeText || !isInputNumeric) return;

      const newValue = Number.parseFloat(props.value || "0") - numberStep;
      if (newValue >= min) {
        console.log("decrement value", newValue);
        props.onChangeText(newValue.toString(), newValue.toString(), newValue.toString());
      }
    };

    return (
      <Box className={fullWidth ? "w-full" : ""}>
        {(props.beforeContent || props.beforeIcon) && (
          <Box className={inputIconContentContainerVariants({ size: beforeContentSize })}>
            <Center className="h-full w-full rounded-tl-lg rounded-bl-lg">
              {props.beforeIcon ? (
                <props.beforeIcon className={cx(inputIconVariants({ size }), "")} />
              ) : (
                props.beforeContent
              )}
            </Center>
          </Box>
        )}
        {raw ? (
          // @ts-ignore - Using ts-ignore to bypass ref type compatibility issues
          <TextInput
            ref={ref}
            selectTextOnFocus={isInputNumeric}
            className={cx(
              inputVariants({
                variant,
                size: size as any,
                beforeContentSize: beforeContentSize as any,
                afterContentSize: afterContentSize as any,
              }),
              props.editable === false && "pointer-events-none web:cursor-not-allowed opacity-80",
              className,
            )}
            placeholderClassName={getPlaceholderClassName({
              editable: props.editable,
              otherClassNames: placeholderClassName,
            })}
            value={props.value}
            onChangeText={(text) => {
              // When in raw mode, call onChangeText with the same value for all three parameters
              // to maintain compatibility with the MaskInput interface
              if (props.onChangeText) {
                props.onChangeText(text, text, text);
              }
            }}
            keyboardType={props.keyboardType}
            placeholder={props.placeholder}
            autoCapitalize={props.autoCapitalize}
            autoCorrect={props.autoCorrect}
            autoFocus={props.autoFocus}
            editable={props.editable}
            maxLength={props.maxLength}
            secureTextEntry={props.secureTextEntry}
            onFocus={props.onFocus}
            onBlur={props.onBlur}
            onKeyPress={props.onKeyPress}
          />
        ) : (
          // @ts-ignore - Using ts-ignore to bypass ref type compatibility issues
          <MaskInput
            ref={ref}
            mask={inputType && baseMasks[inputType] ? baseMasks[inputType] : props.mask || undefined}
            selectTextOnFocus={isInputNumeric}
            className={cx(
              inputVariants({
                variant,
                size: size as any,
                beforeContentSize: beforeContentSize as any,
                afterContentSize: afterContentSize as any,
              }),
              props.editable === false && "pointer-events-none web:cursor-not-allowed opacity-80",
              className,
            )}
            placeholderClassName={getPlaceholderClassName({
              editable: props.editable,
              otherClassNames: placeholderClassName,
            })}
            {...props}
          />
        )}
        {(props.afterContent || props.afterIcon) && (
          <Box className="pointer-events-none absolute top-0 right-0 h-full w-12 p-[1px]">
            <Center className="h-full w-full rounded-tl-lg rounded-bl-lg border-border border-r bg-background-dark">
              {props.afterIcon ? (
                <props.afterIcon className={cx(inputIconVariants({ size }), "")} />
              ) : (
                props.afterContent
              )}
            </Center>
          </Box>
        )}
        {isInputNumeric && !props.afterContent && (
          <Box className="absolute top-0 right-0 h-full w-8 border-border border-l">
            <NumberActionButton
              onPress={handleDecrement}
              action="decrement"
              // disabled={min !== undefined && Number.parseFloat(props.value) <= min}
            />
            <Separator />
            <NumberActionButton
              onPress={handleIncrement}
              action="increment"
              // disabled={max !== undefined && Number.parseFloat(props.value) >= max}
            />
          </Box>
        )}
      </Box>
    );
  },
);

Input.displayName = "Input";

export { Input };
