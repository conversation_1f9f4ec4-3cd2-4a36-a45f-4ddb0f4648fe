import * as TogglePrimitive from "@rn-primitives/toggle";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";
import { cn } from "@/utils";
import { TextClassContext } from "@/components/ui/text";

const toggleVariants = cva(
	"web:group web:inline-flex items-center justify-center rounded-md web:ring-offset-background transition web:hover:bg-muted active:bg-muted web:focus-visible:outline-none web:focus-visible:ring-2 web:focus-visible:ring-ring web:focus-visible:ring-offset-2",
	{
		variants: {
			variant: {
				default: "bg-transparent",
				outline:
					"border border-input bg-transparent web:hover:bg-accent active:bg-accent active:bg-accent",
			},
			size: {
				default: "h-20 px-3 native:h-24 native:px-4",
				sm: "h-9 px-2.5 native:h-10 native:px-[9]",
				lg: "h-32 px-4 native:h-36 native:px-6",
				custom: "",
			},
		},
		defaultVariants: {
			variant: "default",
			size: "default",
		},
	},
);

const toggleTextVariants = cva("text-foreground", {
	variants: {
		variant: {
			default: "",
			outline:
				"web:group-hover:text-accent-foreground web:group-active:text-accent-foreground",
		},
		size: {
			default: "",
			sm: "",
			lg: "",
		},
	},
	defaultVariants: {
		variant: "default",
		size: "default",
	},
});

const Toggle = React.forwardRef<
	TogglePrimitive.RootRef,
	TogglePrimitive.RootProps & VariantProps<typeof toggleVariants>
>(({ className, variant, size, ...props }, ref) => (
	<TextClassContext.Provider
		value={cn(
			toggleTextVariants({ variant, size }),
			props.pressed
				? "text-accent-foreground"
				: "web:group-hover:text-muted-foreground",
			className,
		)}
	>
		<TogglePrimitive.Root
			ref={ref}
			className={cn(
				toggleVariants({ variant, size }),
				props.disabled && "web:pointer-events-none opacity-50",
				props.pressed && "bg-accent",
				className,
			)}
			{...props}
		/>
	</TextClassContext.Provider>
));

Toggle.displayName = TogglePrimitive.Root.displayName;

export { Toggle, toggleTextVariants, toggleVariants };
