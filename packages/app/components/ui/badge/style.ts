import { cva } from "@/utils";

export const badgeVariants = cva({
  base: ["inline-flex", "flex-row", "items-center", "font-medium", "transition-all", "duration-150", "ease-in-out"],
  variants: {
    variant: {
      primary: ["bg-primary", "text-white"],
      "primary-outline": ["bg-background", "border", "border-primary-200"],
      outline: ["bg-background", "border", "border-gray-300", "text-text-secondary"],
      danger: ["bg-danger", "text-white"],
      "outline-success": ["bg-success-50 border border-success-100", "text-success-700"],
      success: ["bg-success-100"],
    },
    size: {
      "2xs": ["px-1", "py-[1px]", "shadow-xs", "rounded-sm", "gap-0.5"],
      xs: ["px-1", "py-0.5", "shadow-xs", "rounded-md", "gap-1"],
      sm: ["px-2", "py-1", "shadow-xs", "rounded-md", "gap-1.5"],
      md: ["px-3", "py-1.5", "shadow-sm", "rounded-lg", "gap-10"],
      lg: ["px-3", "py-2", "shadow-sm", "rounded-lg", "gap-10"],
    },
    rounded: {
      "2xs": "rounded-sm",
      xs: "rounded-md",
      sm: "rounded-md",
      md: "rounded-lg",
      lg: "rounded-lg",
      full: "rounded-full",
    },
  },
  defaultVariants: {
    variant: "outline",
    size: "md",
  },
});

export const badgeTextVariants = cva({
  base: "font-bold",
  variants: {
    variant: {
      primary: "text-white",
      "primary-outline": "text-primary-600",
      outline: "text-text-tertiary",
      danger: "text-white",
      success: "text-success-800",
      "outline-success": "text-success-800",
    },
    size: {
      "2xs": "text-3xs",
      xs: "text-2xs",
      sm: "text-xs",
      md: "text-sm",
      lg: "text-base",
    },
  },
  defaultVariants: {
    variant: "outline",
    size: "sm",
  },
});

export const badgeIconVariants = cva({
  base: ["text-text-secondary"],
  variants: {
    variant: {
      primary: ["text-white"],
      "primary-outline": ["text-primary-600"],
      outline: ["text-text-tertiary"],
      danger: ["text-white"],
      success: ["text-success-700"],
      "outline-success": ["text-success-600"],
    },
    size: {
      "2xs": ["w-3", "h-3"],
      xs: ["w-3.5", "h-3.5"],
      sm: ["w-4", "h-4"],
      md: ["w-4.5", "h-4.5"],
      lg: ["w-5", "h-5"],
    },
  },
  defaultVariants: {
    size: "md",
  },
});
