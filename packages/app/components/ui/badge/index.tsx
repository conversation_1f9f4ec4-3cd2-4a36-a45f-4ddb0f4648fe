import type React from "react";
import { badgeIconVariants, badgeVariants, badgeTextVariants } from "./style";
import { cn, cx } from "@/utils";
import { Box } from "../custom/box";
import type { FC, SVGProps } from "react";
import type { VariantProps } from "class-variance-authority";
import { TextClassContext } from "../text";

export type BadgeProps = React.ComponentProps<typeof Box> &
  VariantProps<typeof badgeVariants> & {
    beforeIcon?: FC<SVGProps<SVGSVGElement>>;
    afterIcon?: FC<SVGProps<SVGSVGElement>>;
    children: React.ReactNode;
  };

export const Badge: FC<BadgeProps> = ({
  variant = "primary",
  size = "md",
  rounded,
  children,
  className,
  iconSize,
  ...props
}: BadgeProps) => {
  return (
    <Box className={cx(badgeVariants({ variant, size, rounded: rounded || size }), className)} {...props}>
      {props.beforeIcon && (
        <props.beforeIcon className={cx(badgeIconVariants({ variant, size: iconSize || size }), "")} />
      )}
      <TextClassContext.Provider value={cn(badgeTextVariants({ variant, size }))}>{children}</TextClassContext.Provider>
      {props.afterIcon && (
        <props.afterIcon className={cx(badgeIconVariants({ variant, size: iconSize || size }), "")} />
      )}
    </Box>
  );
};

Badge.displayName = "Badge";
