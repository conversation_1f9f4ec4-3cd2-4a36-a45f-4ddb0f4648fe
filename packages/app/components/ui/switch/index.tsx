import * as SwitchPrimitives from "@rn-primitives/switch";
import * as React from "react";
import { Platform } from "react-native";
import Animated, { interpolateColor, useAnimatedStyle, useDerivedValue, withTiming } from "react-native-reanimated";
import { useColorScheme } from "nativewind";
import { cn, cx } from "@/utils";
import { nativeSwitchContainerVariants, nativeSwitchThumbVariants, switchThumbVariants, switchVariants } from "./style";
import type { VariantProps } from "class-variance-authority";

const SwitchWeb = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root> & VariantProps<typeof switchVariants>
>(({ className, variant, size, checked = false, disabled, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cx(
      switchVariants({
        variant,
        size,
        checked: checked,
        disabled: disabled,
      }),
      className,
    )}
    checked={checked}
    disabled={disabled}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={cx(
        switchThumbVariants({
          size,
          checked: checked,
        }),
      )}
    />
  </SwitchPrimitives.Root>
));

SwitchWeb.displayName = "SwitchWeb";

const RGB_COLORS = {
  light: {
    primary: "rgb(24, 24, 27)",
    input: "rgb(228, 228, 231)",
  },
  dark: {
    primary: "rgb(250, 250, 250)",
    input: "rgb(39, 39, 42)",
  },
} as const;

const SwitchNative = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root> & VariantProps<typeof switchVariants>
>(({ className, size, checked = false, disabled, ...props }, ref) => {
  const { colorScheme } = useColorScheme();
  const translateX = useDerivedValue(() => {
    if (size === "sm") return checked ? 12 : 0;
    if (size === "lg") return checked ? 24 : 0;
    return checked ? 18 : 0; // Default for medium size
  });
  const animatedRootStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: interpolateColor(
        translateX.value,
        [0, size === "sm" ? 12 : size === "lg" ? 24 : 18],
        [RGB_COLORS[colorScheme || "light"].input, RGB_COLORS[colorScheme || "light"].primary],
      ),
    };
  });
  const animatedThumbStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: withTiming(translateX.value, { duration: 200 }) }],
  }));
  return (
    <Animated.View
      style={animatedRootStyle}
      className={cx(nativeSwitchContainerVariants({ disabled }))}
    >
      <SwitchPrimitives.Root
        className={cx("shrink-0 flex-row items-center rounded-full border-2 border-transparent", className)}
        {...props}
        ref={ref}
      >
        <Animated.View style={animatedThumbStyle}>
          <SwitchPrimitives.Thumb className={cx(nativeSwitchThumbVariants({ size }))} />
        </Animated.View>
      </SwitchPrimitives.Root>
    </Animated.View>
  );
});
SwitchNative.displayName = "SwitchNative";

const SwitchBase = Platform.select({
  web: SwitchWeb,
  default: SwitchNative,
});

type SwitchProps = React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root> & VariantProps<typeof switchVariants>;

const Switch = React.forwardRef<React.ElementRef<typeof SwitchPrimitives.Root>, SwitchProps>((props, ref) => {
  const Component = SwitchBase as React.ForwardRefExoticComponent<
    SwitchProps & React.RefAttributes<React.ElementRef<typeof SwitchPrimitives.Root>>
  >;
  return <Component {...props} ref={ref} />;
});

Switch.displayName = "Switch";

export { Switch };
