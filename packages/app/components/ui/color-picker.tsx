"use client";

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, TooltipTrigger } from "@/components/ui/tooltip";
import * as React from "react";
import { forwardRef, useMemo, useState } from "react";
import { HexColorPicker } from "react-colorful";
import { Pressable } from "react-native";
import { Box } from "./custom/box";
import { Input } from "./input";
import { Text } from "./text";

interface ColorPickerProps {
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  label?: string;
}

const ColorPicker = forwardRef<React.ElementRef<typeof Input>, ColorPickerProps>(
  ({ value, onChange, onBlur, placeholder, label }, ref) => {
    const [open, setOpen] = useState(false);
    const triggerRef = React.useRef<React.ElementRef<typeof TooltipTrigger>>(null);

    // Ensure the value is a valid hex color
    const parsedValue = useMemo(() => {
      // If value is empty or invalid, default to white
      if (!value || !/^#([0-9A-F]{3}){1,2}$/i.test(value)) {
        return "#FFFFFF";
      }
      return value;
    }, [value]);

    const handleInputChange = (text: string) => {
      // Basic validation for hex color
      if (/^#([0-9A-F]{3}){1,2}$/i.test(text) || /^#([0-9A-F]{6})$/i.test(text) || text === "#") {
        onChange(text);
      } else if (text === "") {
        onChange("#");
      }
    };

    const toggleColorPicker = () => {
      if (open) {
        triggerRef.current?.close();
        setOpen(false);
      } else {
        triggerRef.current?.open();
        setOpen(true);
      }
    };

    // Color palette for quick selection - memoized to avoid recreation on each render
    const colorPalette = useMemo(
      () => [
        // Primary colors
        "#f44336",
        "#e91e63",
        "#9c27b0",
        "#673ab7",
        "#3f51b5",
        "#2196f3",
        "#03a9f4",
        "#00bcd4",
        "#009688",
        "#4caf50",
        // Secondary colors
        "#8bc34a",
        "#cddc39",
        "#ffeb3b",
        "#ffc107",
        "#ff9800",
        "#ff5722",
        "#795548",
        "#9e9e9e",
        "#607d8b",
        // Grayscale
        "#000000",
        "#444444",
        "#888888",
        "#cccccc",
        "#ffffff",
      ],
      [],
    );

    return (
      <Box>
        {label && <Text className="mb-1 font-medium">{label}</Text>}
        <Box className="flex-row items-center">
          <Tooltip onOpenChange={setOpen} delayDuration={0}>
            <TooltipTrigger ref={triggerRef}>
              <Pressable
                className="mr-2 h-8 w-8 overflow-hidden rounded-md border border-border"
                style={{ backgroundColor: parsedValue }}
                onPress={toggleColorPicker}
              />
            </TooltipTrigger>
            <TooltipContent className="w-72 p-0" sideOffset={5}>
              <Box className="p-3">
                <Text className="mb-2 font-medium">Selecionar Cor</Text>

                {/* HexColorPicker from react-colorful */}
                <Box className="mb-4">
                  <HexColorPicker
                    color={parsedValue}
                    onChange={(color) => {
                      onChange(color);
                    }}
                    style={{ width: "100%", height: 170 }}
                  />
                </Box>

                {/* Color palette for quick selection */}
                <Box className="flex-row flex-wrap gap-2 p-2">
                  {colorPalette.map((color) => (
                    <Pressable
                      key={color}
                      className="h-8 w-8 overflow-hidden rounded-md border border-border"
                      style={{ backgroundColor: color }}
                      onPress={() => {
                        onChange(color);
                        setOpen(false);
                      }}
                    />
                  ))}
                </Box>

                {/* Hex input field */}
                <Box className="mt-4">
                  <Text className="mb-1 font-medium">Código da Cor</Text>
                  <Input
                    value={parsedValue}
                    onChangeText={handleInputChange}
                    placeholder={placeholder || "#000000"}
                    className="w-full"
                    raw
                  />
                </Box>
              </Box>
            </TooltipContent>
          </Tooltip>
          <Input
            ref={ref}
            value={parsedValue}
            onChangeText={handleInputChange}
            placeholder={placeholder || "#000000"}
            className="flex-1"
            onPressIn={toggleColorPicker}
            onBlur={onBlur}
            raw
          />
        </Box>
      </Box>
    );
  },
);

ColorPicker.displayName = "ColorPicker";

export { ColorPicker };
