import * as React from "react";
import { Box } from "@/components/ui/custom";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/utils";
import type { BadgeProps } from "@/components/ui/badge";

export interface BadgeData extends Omit<BadgeProps, "variant" | "size" | "className"> {
  key?: string;
}

export interface BadgeGroupProps extends Omit<React.ComponentProps<typeof Box>, "children"> {
  badges: BadgeData[];
  variant?: BadgeProps["variant"];
  size?: BadgeProps["size"];
  orientation?: "horizontal" | "vertical";
}

export const BadgeGroup = React.forwardRef<React.ElementRef<typeof Box>, BadgeGroupProps>(
  ({ className, badges, variant = "primary", size = "md", orientation = "horizontal", ...props }, ref) => {
    return (
      <Box
        ref={ref}
        className={cn("flex overflow-hidden", orientation === "vertical" ? "flex-col" : "flex-row", className)}
        {...props}
      >
        {badges.map((badge, index) => {
          const isFirst = index === 0;
          const isLast = index === badges.length - 1;

          const borderClass = cn({
            "rounded-r-none border-r-0": orientation === "horizontal" && !isLast,
            "rounded-l-none": orientation === "horizontal" && !isFirst,
            "rounded-b-none border-b-0": orientation === "vertical" && !isLast,
            "rounded-t-none": orientation === "vertical" && !isFirst,
          });

          return <Badge key={badge.key || index} variant={variant} size={size} className={borderClass} {...badge} />;
        })}
      </Box>
    );
  },
);

BadgeGroup.displayName = "BadgeGroup";
