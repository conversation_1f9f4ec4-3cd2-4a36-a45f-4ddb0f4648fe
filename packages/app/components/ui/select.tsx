import React, { useMemo } from "react";
import { tva } from "@gluestack-ui/nativewind-utils/tva";
import { Svg } from "react-native-svg";
import { withStyleContextAndStates } from "@gluestack-ui/nativewind-utils/withStyleContextAndStates";
import { withStyleContext, useStyleContext } from "@gluestack-ui/nativewind-utils/withStyleContext";
import type { VariantProps } from "@gluestack-ui/nativewind-utils";
import { createSelect } from "@gluestack-ui/select";
import { cssInterop } from "nativewind";
import {
  Actionsheet,
  ActionsheetContent,
  ActionsheetItem,
  ActionsheetItemText,
  ActionsheetDragIndicator,
  ActionsheetDragIndicatorWrapper,
  ActionsheetBackdrop,
  ActionsheetScrollView,
  ActionsheetVirtualizedList,
  ActionsheetFlatList,
  ActionsheetSectionList,
  ActionsheetSectionHeaderText,
} from "./actionsheet";
import { Pressable, View, TextInput, Platform } from "react-native";
import ChevronDown from "@/assets/icons/chevron-down.svg";

const SelectTriggerWrapper = React.forwardRef<
  React.ElementRef<typeof Pressable>,
  React.ComponentProps<typeof Pressable>
>(({ ...props }, ref) => {
  return <Pressable disabled={true} {...props} ref={ref} />;
});

const selectIconStyle = tva({
  base: "text-background-500 fill-none",
  parentVariants: {
    size: {
      "2xs": "h-3 w-3",
      xs: "h-3.5 w-3.5",
      sm: "h-4 w-4",
      md: "h-[18px] w-[18px]",
      lg: "h-5 w-5",
      xl: "h-6 w-6",
    },
  },
});

const selectStyle = tva({
  base: "",
});

const selectTriggerStyle = tva({
  base: "transition-all ease-in-out duration-300 flex-row items-center overflow-hidden data-[disabled=true]:opacity-40 data-[disabled=true]:pointer-events-none",
  variants: {
    size: {
      xs: "h-7",
      sm: "h-9",
      md: "h-10 px-4",
      lg: "h-12",
      xl: "h-14",
    },
    variant: {
      underlined:
        "hidden border-0 border-b rounded-none data-[hover=true]:border-primary-700 data-[focus=true]:border-primary-700 data-[focus=true]:web:shadow-[inset_0_-1px_0_0] data-[focus=true]:web:shadow-primary-700 data-[invalid=true]:border-error-700 data-[invalid=true]:web:shadow-error-700",
      outline:
        "bg-background border border-border rounded-lg data-[focus=true]:border-primary-600/60 data-[focus=true]:outline-none data-[focus=true]:ring-4 data-[focus=true]:ring-primary/15 data-[focus=true]:ring-offset-0 data-[invalid=true]:border-error-700 data-[invalid=true]:data-[hover=true]:border-error-700",
      readonly: "pointer-events-none border-none bg-primary",
    },
  },
});

const selectInputStyle = tva({
  base: "py-auto placeholder:text-typography-500 web:w-full h-full text-typography-900 pointer-events-none web:outline-none ios:leading-[0px]",
  parentVariants: {
    size: {
      xl: "text-xl",
      lg: "text-lg",
      md: "text-md",
      sm: "text-sm",
    },
    variant: {
      underlined: "",
      outline: "",
      rounded: "",
    },
  },
});

type IPrimitiveIcon = {
  height?: number | string;
  width?: number | string;
  fill?: string;
  color?: string;
  size?: number | string;
  stroke?: string;
  as?: React.ElementType;
  className?: string;
  classNameColor?: string;
};

const PrimitiveIcon = React.forwardRef<React.ElementRef<typeof Svg>, IPrimitiveIcon>(
  ({ height, width, fill, color, classNameColor, size, stroke = "currentColor", as: AsComp, ...props }, ref) => {
    color = color ?? classNameColor;
    const sizeProps = useMemo(() => {
      if (size) return { size };
      if (height && width) return { height, width };
      if (height) return { height };
      if (width) return { width };
      return {};
    }, [size, height, width]);

    let colorProps = {};
    if (fill) {
      colorProps = { ...colorProps, fill: fill };
    }
    if (stroke !== "currentColor") {
      colorProps = { ...colorProps, stroke: stroke };
    } else if (stroke === "currentColor" && color !== undefined) {
      colorProps = { ...colorProps, stroke: color };
    }

    if (AsComp) {
      return <AsComp ref={ref} {...props} {...sizeProps} {...colorProps} />;
    }
    return <Svg ref={ref} height={height} width={width} {...colorProps} {...props} />;
  },
);

const UISelect = createSelect(
  {
    Root: View,
    Trigger:
      Platform.OS === "web" ? withStyleContext(SelectTriggerWrapper) : withStyleContextAndStates(SelectTriggerWrapper),
    Input: TextInput,
    Icon: PrimitiveIcon,
  },
  {
    Portal: Actionsheet,
    Backdrop: ActionsheetBackdrop,
    Content: ActionsheetContent,
    DragIndicator: ActionsheetDragIndicator,
    DragIndicatorWrapper: ActionsheetDragIndicatorWrapper,
    Item: ActionsheetItem,
    ItemText: ActionsheetItemText,
    ScrollView: ActionsheetScrollView,
    VirtualizedList: ActionsheetVirtualizedList,
    FlatList: ActionsheetFlatList,
    SectionList: ActionsheetSectionList,
    SectionHeaderText: ActionsheetSectionHeaderText,
  },
);

cssInterop(UISelect, { className: "style" });
cssInterop(UISelect.Input, {
  className: { target: "style", nativeStyleToProp: { textAlign: true } },
});
cssInterop(SelectTriggerWrapper, { className: "style" });
//@ts-ignore
cssInterop(UISelect.Icon, {
  className: {
    target: "style",
    nativeStyleToProp: {
      height: true,
      width: true,
      fill: true,
      color: "classNameColor",
      stroke: true,
    },
  },
});

type ISelectProps = VariantProps<typeof selectStyle> &
  React.ComponentProps<typeof UISelect> & {
    className?: string;
  };

const Select = React.forwardRef<React.ElementRef<typeof UISelect>, ISelectProps>(({ className, ...props }, ref) => {
  return <UISelect ref={ref} {...props} className={selectStyle({ class: className })} />;
});

type ISelectTriggerProps = VariantProps<typeof selectTriggerStyle> &
  React.ComponentProps<typeof UISelect.Trigger> & { className?: string };

const SelectTrigger = React.forwardRef<React.ElementRef<typeof UISelect.Trigger>, ISelectTriggerProps>(
  ({ className, size = "md", variant = "outline", ...props }, ref) => {
    return (
      <UISelect.Trigger
        className={selectTriggerStyle({
          class: className,
          size,
          variant,
        })}
        ref={ref}
        context={{ size, variant }}
        {...props}
      />
    );
  },
);

type ISelectInputProps = VariantProps<typeof selectInputStyle> &
  React.ComponentProps<typeof UISelect.Input> & { className?: string };

const SelectInput = React.forwardRef<React.ElementRef<typeof UISelect.Input>, ISelectInputProps>(
  ({ className, ...props }, ref) => {
    const { size: parentSize, variant: parentVariant } = useStyleContext();
    return (
      <UISelect.Input
        className={selectInputStyle({
          class: className,
          parentVariants: {
            size: parentSize,
            variant: parentVariant,
          },
        })}
        ref={ref}
        {...props}
      />
    );
  },
);

type ISelectIcon = VariantProps<typeof selectIconStyle> &
  React.ComponentProps<typeof UISelect.Icon> & { className?: string };

const SelectIcon = React.forwardRef<React.ElementRef<typeof UISelect.Icon>, ISelectIcon>(
  ({ className, size, ...props }, ref) => {
    const { size: parentSize } = useStyleContext();
    if (typeof size === "number") {
      return (
        <UISelect.Icon
          ref={ref}
          as={ChevronDown}
          {...props}
          className={selectIconStyle({ class: className })}
          size={size}
        />
      );
    }
    if ((props?.height !== undefined || props?.width !== undefined) && size === undefined) {
      return <UISelect.Icon ref={ref} as={ChevronDown} {...props} className={selectIconStyle({ class: className })} />;
    }
    return (
      <UISelect.Icon
        as={ChevronDown}
        className={selectIconStyle({
          class: className,
          size,
          parentVariants: {
            size: parentSize,
          },
        })}
        ref={ref}
        {...props}
      />
    );
  },
);

Select.displayName = "Select";
SelectTrigger.displayName = "SelectTrigger";
SelectInput.displayName = "SelectInput";
SelectIcon.displayName = "SelectIcon";

// Actionsheet Components
const SelectPortal = UISelect.Portal;
const SelectBackdrop = UISelect.Backdrop;
const SelectContent = UISelect.Content;
const SelectDragIndicator = UISelect.DragIndicator;
const SelectDragIndicatorWrapper = UISelect.DragIndicatorWrapper;
const SelectItem = UISelect.Item;
const SelectItemText = UISelect.ItemText;
const SelectScrollView = UISelect.ScrollView;
const SelectVirtualizedList = UISelect.VirtualizedList;
const SelectFlatList = UISelect.FlatList;
const SelectSectionList = UISelect.SectionList;
const SelectSectionHeaderText = UISelect.SectionHeaderText;

export {
  Select,
  SelectTrigger,
  SelectInput,
  SelectIcon,
  SelectPortal,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectItem,
  SelectItemText,
  SelectScrollView,
  SelectVirtualizedList,
  SelectFlatList,
  SelectSectionList,
  SelectSectionHeaderText,
};
