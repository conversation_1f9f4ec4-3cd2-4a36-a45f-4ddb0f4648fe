import React, { useEffect, useRef } from 'react';
import { Dimensions, Platform } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  runOnJS,
  useAnimatedGestureHandler,
  interpolate,
  Extrapolation,
} from 'react-native-reanimated';
import { PanGestureHandler, PanGestureHandlerGestureEvent } from 'react-native-gesture-handler';
import { useToaster } from '@backpackapp-io/react-native-toast';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Box, Text } from '@/components/ui';
import { cn } from '@/utils';

const { width: screenWidth } = Dimensions.get('window');
const TOAST_HEIGHT = 60;
const TOAST_MARGIN = 12;
const SWIPE_THRESHOLD = screenWidth * 0.3;

interface ToastData {
  createdAt: number;
  visible: boolean;
  type: string;
  message: string;
  pauseDuration: number;
  position: number;
  providerKey: string;
  isSwipeable: boolean;
  duration: number;
  id: string;
  dismissReason?: string;
  styles: object;
}

interface AnimatedToastProps {
  toast: ToastData;
  index: number;
  onDismiss: (id: string) => void;
}

const AnimatedToast: React.FC<AnimatedToastProps> = ({ toast, index, onDismiss }) => {
  const insets = useSafeAreaInsets();
  const translateX = useSharedValue(screenWidth);
  const translateY = useSharedValue(0);
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.8);
  const isDismissing = useRef(false);

  // Calculate position based on index and position property
  const topOffset = insets.top + 20 + (index * (TOAST_HEIGHT + TOAST_MARGIN));

  useEffect(() => {
    if (toast.visible && !isDismissing.current) {
      // Entrance animation
      translateX.value = withSpring(0, {
        damping: 20,
        stiffness: 300,
      });
      opacity.value = withTiming(1, { duration: 300 });
      scale.value = withSpring(1, {
        damping: 15,
        stiffness: 200,
      });
    } else if (!toast.visible || isDismissing.current) {
      // Exit animation
      translateX.value = withTiming(screenWidth, { duration: 300 });
      opacity.value = withTiming(0, { duration: 300 });
      scale.value = withTiming(0.8, { duration: 300 });
    }
  }, [toast.visible]);

  const handleDismiss = () => {
    if (isDismissing.current) return;
    isDismissing.current = true;
    onDismiss(toast.id);
  };

  const gestureHandler = useAnimatedGestureHandler<
    PanGestureHandlerGestureEvent,
    { startX: number }
  >({
    onStart: (_, context) => {
      context.startX = translateX.value;
    },
    onActive: (event, context) => {
      if (!toast.isSwipeable) return;

      // Only allow swiping to the right (positive direction)
      if (event.translationX > 0) {
        translateX.value = context.startX + event.translationX;

        // Reduce opacity as user swipes
        const progress = Math.min(event.translationX / SWIPE_THRESHOLD, 1);
        opacity.value = 1 - progress * 0.7;
        scale.value = 1 - progress * 0.1;
      }
    },
    onEnd: (event) => {
      if (!toast.isSwipeable) return;

      if (event.translationX > SWIPE_THRESHOLD) {
        // Dismiss the toast
        translateX.value = withTiming(screenWidth, { duration: 200 });
        opacity.value = withTiming(0, { duration: 200 });
        runOnJS(handleDismiss)();
      } else {
        // Snap back to original position
        translateX.value = withSpring(0, {
          damping: 20,
          stiffness: 300,
        });
        opacity.value = withTiming(1, { duration: 200 });
        scale.value = withTiming(1, { duration: 200 });
      }
    },
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
      ],
      opacity: opacity.value,
    };
  });

  const getToastVariant = (type: string) => {
    switch (type.toLowerCase()) {
      case 'success':
        return 'bg-success border-success-border text-success-foreground';
      case 'error':
        return 'bg-error border-error-border text-error-foreground';
      case 'warning':
        return 'bg-warning border-warning-border text-warning-foreground';
      case 'info':
        return 'bg-info border-info-border text-info-foreground';
      default:
        return 'bg-background border-border text-foreground shadow-lg';
    }
  };

  return (
    <PanGestureHandler onGestureEvent={gestureHandler} enabled={toast.isSwipeable}>
      <Animated.View
        style={[
          {
            position: 'absolute',
            top: topOffset,
            left: TOAST_MARGIN,
            right: TOAST_MARGIN,
            minHeight: TOAST_HEIGHT,
            zIndex: 1000 + index,
          },
          animatedStyle,
        ]}
      >
        <Box
          className={cn(
            'flex-row items-center justify-between rounded-lg border px-4 py-3',
            getToastVariant(toast.type)
          )}
        >
          <Box className="flex-1 mr-2">
            <Text className="text-sm font-medium" numberOfLines={2}>
              {toast.message}
            </Text>
          </Box>

          {toast.isSwipeable && (
            <Box className="opacity-60">
              <Text className="text-xs">Swipe →</Text>
            </Box>
          )}
        </Box>
      </Animated.View>
    </PanGestureHandler>
  );
};

export const Toast: React.FC = () => {
  const { toasts, handlers } = useToaster();

  // Filter only visible toasts and sort by position
  const visibleToasts = toasts
    .filter((toast) => toast.visible)
    .sort((a, b) => (a.position || 0) - (b.position || 0));

  const handleDismiss = (id: string) => {
    // Find the toast and call the appropriate handler
    const toast = toasts.find((t) => t.id === id);
    if (toast && handlers[id]) {
      (handlers[id] as any).dismiss();
    }
  };

  return (
    <>
      {visibleToasts.map((toast, index) => (
        <AnimatedToast
          key={toast.id}
          toast={toast}
          index={index}
          onDismiss={handleDismiss}
        />
      ))}
    </>
  );
};

export default Toast;
