import { cva } from "@/utils";

export const fillVariants = cva({
  base: [],
  variants: {
    variant: {
      default: ["stroke-primary"],
      error: ["stroke-error"],
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

export const trackVariants = cva({
  base: [],
  variants: {
    variant: {
      default: ["stroke-primary-100"],
      error: ["stroke-error-100"],
    },
  },
  defaultVariants: {
    variant: "default",
  },
});
