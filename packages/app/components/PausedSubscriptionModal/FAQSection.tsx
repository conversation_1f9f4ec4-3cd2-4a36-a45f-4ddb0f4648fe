import ChevronRightIcon from "@/assets/icons/chevron-right.svg";
import HelpCircleIcon from "@/assets/icons/help-circle.svg";
import { useEffect, useState } from "react";
import { type LayoutChangeEvent, Pressable } from "react-native";
import Reanimated, { useAnimatedStyle, useSharedValue, withTiming } from "react-native-reanimated";
import { Box, Center, Heading, Text } from "../ui";

type FAQItem = {
  id: string;
  title: string;
  description: string;
};

const faqData: FAQItem[] = [
  {
    id: "faq-1",
    title: "O que acontece quando meu período de teste acaba?",
    description:
      "Quando seu período de teste termina, sua conta entra em modo pausado. Seu site continua funcionando, mas você precisa escolher um plano para continuar usando todas as funcionalidades da plataforma Imoblr.",
  },
  {
    id: "faq-2",
    title: "Posso mudar de plano depois?",
    description:
      "Sim, você pode mudar de plano a qualquer momento. Se você fizer upgrade, a mudança é imediata. Se fizer downgrade, a mudança ocorrerá no próximo ciclo de faturamento.",
  },
  {
    id: "faq-3",
    title: "Como funciona a cobrança?",
    description:
      "A cobrança é feita mensalmente ou anualmente, dependendo do plano escolhido. Planos anuais têm 20% de desconto em relação aos mensais. Aceitamos cartões de crédito e boleto bancário.",
  },
  {
    id: "faq-4",
    title: "Preciso fornecer dados de pagamento agora?",
    description:
      "Sim, para ativar sua conta após o período de teste, você precisará fornecer um método de pagamento válido. Sua primeira cobrança será processada imediatamente após a confirmação.",
  },
  {
    id: "faq-5",
    title: "E se eu tiver mais dúvidas?",
    description:
      "Nossa equipe de suporte está disponível para ajudar com qualquer dúvida adicional. Você pode entrar em contato pelo chat no canto inferior direito da tela ou pelo e-mail <EMAIL>.",
  },
];

const FAQItem: React.FC<{
  item: FAQItem;
  isActive: boolean;
  onToggle: () => void;
}> = ({ item, isActive, onToggle }) => {
  const [contentHeight, setContentHeight] = useState(0);
  const height = useSharedValue(0);
  const rotate = useSharedValue("0deg"); // Add a shared value for rotation

  // Update the height and rotation values when isActive changes
  useEffect(() => {
    height.value = withTiming(isActive ? contentHeight : 0, { duration: 300 });
    rotate.value = withTiming(isActive ? "90deg" : "0deg", { duration: 300 }); // Animate rotation
  }, [isActive, contentHeight, height, rotate]);

  const contentStyle = useAnimatedStyle(() => {
    return {
      height: height.value,
      opacity: isActive ? withTiming(1, { duration: 300 }) : withTiming(0, { duration: 150 }),
      overflow: "hidden",
    };
  });

  // Create an animated style for the chevron rotation
  const chevronStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: rotate.value }],
    };
  });

  const handleLayout = (event: LayoutChangeEvent) => {
    const { height: layoutHeight } = event.nativeEvent.layout;
    if (layoutHeight > 0 && layoutHeight !== contentHeight) {
      setContentHeight(layoutHeight);
      if (isActive) {
        height.value = withTiming(layoutHeight, { duration: 300 });
      }
    }
  };

  return (
    <Box className="mb-4 overflow-hidden rounded-4xl border border-border-light bg-background shadow-lg">
      <Pressable onPress={onToggle}>
        <Box className="flex-row items-center justify-between p-8">
          <Text className="flex-1 font-medium text-text-secondary">{item.title}</Text>
          {/* Apply the animated style here */}
          <Reanimated.View style={chevronStyle}>
            <ChevronRightIcon className={`h-5 w-5 ${isActive ? "text-primary" : "text-text-tertiary"}`} />
          </Reanimated.View>
        </Box>
      </Pressable>
      <Reanimated.View style={contentStyle}>
        <Box className="p-8 pt-0" onLayout={handleLayout}>
          <Text className="text-text-tertiary">{item.description}</Text>
        </Box>
      </Reanimated.View>
    </Box>
  );
};

export const FAQSection: React.FC = () => {
  const [activeFaqId, setActiveFaqId] = useState<string | null>(null);

  const handleToggle = (id: string) => {
    setActiveFaqId(activeFaqId === id ? null : id);
  };

  return (
    <Center className="w-full max-w-[1000px] p-16">
      <Box className="mb-4 w-fit flex-row items-center justify-center rounded-full border-8 border-primary-50 bg-primary-100/50 p-2">
        <HelpCircleIcon className="h-12 w-12 text-primary" />
      </Box>
      <Heading size="xl" className="text-center text-text-secondary">
        Dúvidas frequentes
      </Heading>
      <Text className="mt-2 mb-16 text-center text-text-tertiary">
        Listamos abaixo algumas das perguntas mais comuns sobre a nossa plataforma.
      </Text>
      <Box className="w-full">
        {faqData.map((faq) => (
          <FAQItem key={faq.id} item={faq} isActive={activeFaqId === faq.id} onToggle={() => handleToggle(faq.id)} />
        ))}
      </Box>
    </Center>
  );
};

export default FAQSection;
