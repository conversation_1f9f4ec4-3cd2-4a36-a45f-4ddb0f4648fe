import InvoiceIcon from "@/assets/icons/invoice-02.svg";
import axios from "axios";
import { router } from "expo-router";
import <PERSON><PERSON><PERSON>ie<PERSON> from "lottie-react-native";
import type React from "react";
import { useEffect, useRef } from "react";
import { Box, Button, Center, Heading, Text } from "../ui";
import type { StepProps } from "./constants";

interface SuccessStepProps extends StepProps {
  currentTeamId: string;
}

export const SuccessStep: React.FC<SuccessStepProps> = ({ currentTeamId }) => {
  const animationRef = useRef<LottieView>(null);

  useEffect(() => {
    if (animationRef.current) {
      animationRef.current.play();
    }
  }, []);

  const handleGoToDashboard = () => {
    router.replace("/");
  };

  const handleDownloadReceipt = async () => {
    try {
      // Create a portal session to access invoices
      const response = await axios.get(`${process.env.EXPO_PUBLIC_API_URL}/billing/portal-session`, {
        params: {
          account_id: currentTeamId,
          return_url: window.location.href,
        },
      });

      // Redirect to the Stripe portal where the user can download receipts
      if (response.data?.url) {
        window.location.href = response.data.url;
      }
    } catch (error) {
      console.error("Error getting portal session:", error);
      alert("Failed to open customer portal. Please try again.");
    }
  };

  return (
    <Box className="items-center p-6">
      <Center className="mb-8 w-full">
        <Box className="h-[200px] w-[200px]">
          <LottieView
            source={require("@/assets/animations/success-animation.json")}
            style={{ width: "100%", height: "100%" }}
            ref={animationRef}
            autoPlay={false}
            loop={false}
          />
        </Box>
      </Center>

      <Heading size="lg" className="mb-4 text-center">
        Pagamento realizado com sucesso!
      </Heading>

      <Text className="mb-8 text-center text-text-secondary">
        Seu pagamento foi processado e sua assinatura está ativa. Você já pode acessar todas as funcionalidades
        disponíveis no seu plano.
      </Text>

      <Box className="w-full items-center gap-4">
        <Button size="lg" className="w-64" onPress={handleGoToDashboard}>
          <Text>Ir para o dashboard</Text>
        </Button>

        <Button variant="link" className="mt-4" onPress={handleDownloadReceipt}>
          <InvoiceIcon className="mr-2 h-4 w-4" />
          <Text>Baixar recibo</Text>
        </Button>
      </Box>
    </Box>
  );
};

export default SuccessStep;
