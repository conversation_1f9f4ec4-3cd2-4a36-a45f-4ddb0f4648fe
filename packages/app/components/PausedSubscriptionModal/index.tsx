import { useAxios, useCurrentTeamId, useSupabase } from "@/hooks";
import { useBillingStatus } from "@/hooks/useBillingStatus";
import { usePricingProducts } from "@/hooks/usePricingProducts";
import useTeamAccountMembers from "@/hooks/useTeamAccountMembers";
import type { StripeProduct, StripeProductPrice } from "@/types";
import { useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import Modal from "../Modal";
import { Box } from "../ui";
import { ModalStep } from "./constants";
import PaymentFormStep from "./PaymentFormStep";
import PricingTableStep from "./PricingTableStep";
import SuccessStep from "./SuccessStep";

type PausedSubscriptionModalProps = {
  isOpen: boolean;
};

export const PausedSubscriptionModal = ({ isOpen = true }: PausedSubscriptionModalProps) => {
  const [currentTeamId] = useCurrentTeamId();
  const supabase = useSupabase();
  const { axios } = useAxios(); // Use the authenticated axios instance
  const { data: billingStatus, isLoading: isBillingStatusLoading } = useBillingStatus(currentTeamId);
  const { data: teamMembers = [] } = useTeamAccountMembers(currentTeamId);
  const { data: pricingProducts = [] } = usePricingProducts(true); // Default to yearly pricing
  const [isYearly, setIsYearly] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<StripeProduct | null>(null);
  const [currentStep, setCurrentStep] = useState<ModalStep>(ModalStep.PRICING_TABLE);
  const queryClient = useQueryClient();

  // Find the current product based on the subscription
  useEffect(() => {
    if (pricingProducts.length > 0 && billingStatus?.plan?.price_id) {
      // Try to find the product that matches the current subscription
      const currentProduct = pricingProducts.find((product) =>
        product.prices?.some((price: StripeProductPrice) => price.id === billingStatus.plan.price_id),
      );

      if (currentProduct) {
        setSelectedProduct(currentProduct);
        // Set isYearly based on the current subscription
        setIsYearly(billingStatus.plan.interval === "year");
      } else {
        // If no match, default to the first product
        setSelectedProduct(pricingProducts[0]);
      }
    } else if (pricingProducts.length > 0) {
      // If no subscription or products just loaded, default to the first product
      setSelectedProduct(pricingProducts[0]);
    }
  }, [pricingProducts, billingStatus]);

  const handlePaymentSuccess = async () => {
    // Add a delay to allow webhooks to process the payment and update the subscription
    // This is a more robust approach than immediately checking the status
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Invalidate the billing status query to refresh the data
    await queryClient.invalidateQueries({ queryKey: ["billingStatus", currentTeamId] });

    // Fetch the latest billing status
    const updatedBillingStatus = await queryClient.fetchQuery({
      queryKey: ["billingStatus", currentTeamId],
      queryFn: async () => {
        const response = await supabase.rpc("get_team_billing_status", {
          team_id: currentTeamId,
        });
        if (response.error) throw response.error;
        return response.data;
      },
    });

    // If the status is still paused, we'll try again after a longer delay
    // This handles the case where webhooks might take longer to process
    if (updatedBillingStatus?.status === "paused") {
      console.log("Subscription still shows as paused, waiting for webhook processing...");

      // Wait a bit longer and try one more time
      await new Promise((resolve) => setTimeout(resolve, 3000));
      await queryClient.invalidateQueries({ queryKey: ["billingStatus", currentTeamId] });

      // Check one more time
      const finalBillingStatus = await queryClient.fetchQuery({
        queryKey: ["billingStatus", currentTeamId],
        queryFn: async () => {
          const response = await supabase.rpc("get_team_billing_status", {
            team_id: currentTeamId,
          });
          if (response.error) throw response.error;
          return response.data;
        },
      });

      // If it's still paused, we need to manually trigger the invoice payment
      if (finalBillingStatus?.status === "paused") {
        console.log("Subscription is still paused after waiting, manually triggering invoice payment...");

        // Call a function to manually pay any open invoices
        try {
          const response = await axios.post("/billing/pay-invoices", {
            team_id: currentTeamId,
          });

          console.log("Manual invoice payment result:", response.data);
          // Refresh the billing status one more time
          await queryClient.invalidateQueries({ queryKey: ["billingStatus", currentTeamId] });
        } catch (err) {
          console.error("Failed to pay open invoices:", err);
        }
      }
    }

    // Move to the success step
    setCurrentStep(ModalStep.SUCCESS);
  };

  if (isBillingStatusLoading) {
    return null;
  }

  // Render the appropriate step content based on the current step
  const renderStepContent = () => {
    switch (currentStep) {
      case ModalStep.PRICING_TABLE:
        return (
          <PricingTableStep
            teamMembers={teamMembers}
            isYearly={isYearly}
            setIsYearly={setIsYearly}
            pricingProducts={pricingProducts}
            selectedProduct={selectedProduct}
            setSelectedProduct={(product) => {
              setSelectedProduct(product);
              setCurrentStep(ModalStep.PAYMENT_FORM);
            }}
            onNext={() => setCurrentStep(ModalStep.PAYMENT_FORM)}
          />
        );

      case ModalStep.PAYMENT_FORM:
        return (
          <PaymentFormStep
            teamMembers={teamMembers}
            isYearly={isYearly}
            setIsYearly={setIsYearly}
            selectedProduct={selectedProduct}
            setSelectedProduct={setSelectedProduct}
            onBack={() => setCurrentStep(ModalStep.PRICING_TABLE)}
            onSuccess={handlePaymentSuccess}
            onNext={() => setCurrentStep(ModalStep.SUCCESS)}
          />
        );

      case ModalStep.SUCCESS:
        return (
          <SuccessStep
            teamMembers={teamMembers}
            isYearly={isYearly}
            setIsYearly={setIsYearly}
            selectedProduct={selectedProduct}
            setSelectedProduct={setSelectedProduct}
            onNext={() => {}}
            currentTeamId={currentTeamId || ""}
          />
        );

      default:
        return null;
    }
  };

  return <Box className="absolute top-0 left-0 z-50 h-screen w-screen bg-background">{renderStepContent()}</Box>;
};

export default PausedSubscriptionModal;
