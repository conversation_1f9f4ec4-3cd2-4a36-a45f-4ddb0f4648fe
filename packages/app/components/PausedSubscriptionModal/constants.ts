import CellsBulkIcon from "@/assets/icons/bulk/cells.svg";
import NoIcon from "@/assets/icons/cancel.svg";
import CellsStrokeIcon from "@/assets/icons/cells.svg";
import HouseIcon from "@/assets/icons/house-03.svg";
import CellsSolidIcon from "@/assets/icons/solid/cells.svg";
import YesIcon from "@/assets/icons/tick.svg";
import UserIcon from "@/assets/icons/user-03.svg";
import UsersIcon from "@/assets/icons/user-multiple-02.svg";
import WebsiteIcon from "@/assets/icons/web-design-01.svg";
import type { StripeProduct } from "@/types";
import type React from "react";

// Step enum to track the current step of the modal
export enum ModalStep {
  PRICING_TABLE = 1,
  PAYMENT_FORM = 2,
  SUCCESS = 3,
}

export const planIcons = [CellsStrokeIcon, CellsBulkIcon, CellsSolidIcon];

export const featureIcons: Record<string, React.FC<React.SVGProps<SVGSVGElement>>> = {
  checkmark: YesIcon,
  house: HouseIcon,
  y: YesIcon,
  n: NoIcon,
  user: UserIcon,
  users: UsersIcon,
  website: WebsiteIcon,
};

export const planStyles = [
  {
    cardContainer: "bg-background",
    card: "",
    cardHeader: "",
    title: "text-primary-900",
    price: "text-text-secondary",
    features: "text-text-secondary",
    featureIcon: "text-gray-500",
    buttonPlanName: "text-secondary-600",
    iconContainer: "bg-secondary-400 border-secondary shadow-[inset_0_2px_2px_0_rgba(var(--color-secondary-100),0.5),inset_0_-2px_2px_0_rgba(var(--color-secondary-600),0.5),inset_2px_0_2px_0_rgba(var(--color-secondary-100),0.25),inset_-2px_0_2px_0_rgba(var(--color-secondary-600),0.25)]",
    icon: "text-background translate-x-[1.5px]",
  },
  {
    cardContainer: "bg-background",
    card: "",
    cardHeader: "",
    title: "text-primary-900",
    price: "bg-clip-text text-[rgba(0,0,0,0)] [-webkit-background-clip:text] [-webkit-text-fill-color:transparent] [background-image:linear-gradient(45deg,#a163f1,#6363f1_34%,#3498ea_60%,#40dfa3)]",
    features: "text-text-secondary",
    featureIcon: "text-primary-500",
    buttonPlanName: "bg-clip-text text-[rgba(0,0,0,0)] [-webkit-background-clip:text] [-webkit-text-fill-color:transparent] [background-image:linear-gradient(45deg,#a163f1,#6363f1_34%,#3498ea_60%,#40dfa3)]",
    iconContainer: "border-primary [background-image:linear-gradient(45deg,#a163f1,#6363f1_34%,#3498ea_60%,#40dfa3)] shadow-[inset_0_2px_2px_0_rgba(var(--color-gray-50),0.4),inset_0_-2px_2px_0_rgba(var(--color-gray-950),0.4),inset_2px_0_2px_0_rgba(var(--color-gray-50),0.2),inset_-2px_0_2px_0_rgba(var(--color-gray-950),0.2)]",
    icon: "text-background rotate-45 translate-x-[1.5px] translate-y-0.5",
  },
  {
    cardContainer: "bg-background",
    card: "",
    cardHeader: "",
    title: "text-primary-900",
    price: "text-text-secondary",
    features: "text-text-secondary",
    featureIcon: "text-primary-700",
    buttonPlanName: "text-primary-600",
    iconContainer: "bg-primary-400 border-primary shadow-[inset_0_2px_2px_0_rgba(var(--color-primary-100),0.5),inset_0_-2px_2px_0_rgba(var(--color-primary-600),0.5),inset_2px_0_2px_0_rgba(var(--color-primary-100),0.25),inset_-2px_0_2px_0_rgba(var(--color-primary-600),0.25)]",
    icon: "text-background rotate-90 translate-x-[0.5px]",
  },
];

export interface StepProps {
  teamMembers: { id: string }[];
  isYearly: boolean;
  setIsYearly: (value: boolean) => void;
  selectedProduct: StripeProduct | null;
  setSelectedProduct: (product: StripeProduct) => void;
  onNext: () => void;
  onBack?: () => void;
  pricingProducts?: StripeProduct[];
  currentTeamId?: string;
}
