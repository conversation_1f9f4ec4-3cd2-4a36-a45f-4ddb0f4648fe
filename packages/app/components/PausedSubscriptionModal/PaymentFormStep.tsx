import PauseCircleIcon from "@/assets/icons/pause-circle.svg";
import type React from "react";
import { ScrollView } from "react-native";
import PaymentForm from "../PaymentForm";
import { Box, Heading, Text } from "../ui";
import type { StepProps } from "./constants";

export const PaymentFormStep: React.FC<StepProps & { onSuccess: () => void }> = ({
  teamMembers,
  isYearly,
  selectedProduct,
  onBack,
  onSuccess,
}) => {
  if (!selectedProduct) return null;

  return (
    <ScrollView style={{ width: "100%", height: "100%" }}>
      <Box className="min-h-screen min-w-screen items-center">
        <PaymentForm
          quantity={teamMembers.length}
          isYearly={isYearly}
          onSuccess={onSuccess}
          onCancel={onBack || (() => {})} // Go back to pricing table
          product={selectedProduct}
        />
      </Box>
    </ScrollView>
  );
};

export default PaymentFormStep;
