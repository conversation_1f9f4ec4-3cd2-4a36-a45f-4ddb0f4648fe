import ArrowRightIcon from "@/assets/icons/arrow-right.svg";
import UserIcon from "@/assets/icons/user-02.svg";
import ExpiredTrialIllustration from "@/assets/illustrations/storyset/Time management-rafiki.svg";
import type { StripeProduct, StripeProductFeature } from "@/types";
import { cx, getStripePrice, getTotalTieredPrice, getUnitTieredPrice, parseFeatureText } from "@/utils";
import type React from "react";
import { ScrollView } from "react-native";
import { Badge, Box, Button, Center, Heading, Switch, Text } from "../ui";
import type { StepProps } from "./constants";
import { featureIcons, planIcons, planStyles } from "./constants";
import { FAQSection } from "./FAQSection";

export const PricingTableStep: React.FC<StepProps> = ({
  teamMembers,
  isYearly,
  setIsYearly,
  pricingProducts = [],
  setSelectedProduct,
  onNext,
}) => {
  const handleSelectPlan = (product: StripeProduct) => {
    setSelectedProduct(product);
    onNext();
  };

  return (
    <ScrollView style={{ width: "100%", height: "100%" }}>
      <Box className="min-h-screen w-full min-w-screen items-center bg-background">
        <Center className="mb-6 w-full border-border-light border-b bg-background-dark">
          <Box className="w-full max-w-[1000px] flex-row items-center justify-between">
            <Box>
              <Heading size="2xl" className="mb-2">
                Seu período de teste acabou 😕
              </Heading>
              <Text className="text-text-tertiary">
                Não se preocupe, o seu site continua funcionando sem nenhuma alteração enquanto isso.{"\n"}
                Para continuar utilizando a plataforma Imoblr, por favor selecione um plano abaixo.
              </Text>
            </Box>
            <Box className="ml-8">
              <ExpiredTrialIllustration className="h-64 w-64 text-gray-500" />
            </Box>
          </Box>
        </Center>

        <Box className="w-full items-center p-12">
          <Box className="mb-6 items-center justify-center">
            <Heading
              size="4xl"
              className="bg-clip-text text-center text-[rgba(0,0,0,0)] leading-10 [-webkit-background-clip:text] [-webkit-text-fill-color:transparent] [background-image:linear-gradient(45deg,#a163f1,#6363f1_34%,#3498ea_60%,#40dfa3)]"
            >
              Escolha o plano que melhor{"\n"}atende suas necessidades
            </Heading>
            <Text className="mt-4 mb-8 max-w-[600px] text-center text-lg text-text-tertiary">
              Ofereremos planos variados que se adaptam a diferentes necessidades, desde planos individuais até planos
              para grandes negócios e equipes.
            </Text>
            <Center
              className={cx("flex-row rounded-full p-2", {
                "border-primary-300 bg-primary-50 ring-primary-100": isYearly,
              })}
            >
              <Switch id="billing-toggle" checked={isYearly} onCheckedChange={setIsYearly} className="mr-4" />
              <Text className={cx("-mt-0.5", { "text-primary": isYearly })}>
                Pagamento anual{" "}
                <Text
                  className={cx(
                    "ml-2 rounded-full border border-primary-100 bg-background-50 px-2 py-1 text-primary text-sm",
                    {
                      "border-primary-200 bg-background ring-4 ring-primary-500/10": isYearly,
                    },
                  )}
                >
                  20% de desconto
                </Text>
              </Text>
            </Center>
          </Box>

          {pricingProducts && (
            <Box className="w-full max-w-[1000px] rounded-4xl border border-gray-500/5 bg-gray-100 shadow-[0_51px_78px_rgba(17,7,53,0.025),0_21.3066px_35.4944px_rgba(17,7,53,0.018),0_11.3915px_18.9418px_rgba(17,7,53,0.015),0_6.38599px_9.8801px_rgba(17,7,53,0.013),0_3.39155px_4.58665px_rgba(17,7,53,0.01),0_1.4113px_1.55262px_rgba(17,7,53,0.015),inset_0_1px_0_rgba(41,56,78,0.025)]">
              <Center className="-z-10 absolute h-full w-full">
                <Box className="absolute right-[4%] bottom-[4%] h-1/2 w-1/2 bg-secondary blur-[3rem]" />
                <Box className="absolute bottom-[4%] left-[4%] h-1/2 w-1/2 bg-tertiary/50 blur-[3rem]" />
                <Box className="absolute top-[4%] right-[4%] h-1/2 w-1/2 bg-primary/50 blur-[3rem]" />
                <Box className="absolute top-[4%] left-[4%] h-1/2 w-1/2 bg-quaternary/60 blur-[3rem]" />
              </Center>
              {pricingProducts.map((product, productIndex) => {
                const price = getStripePrice(product, isYearly);
                if (!price) return null;

                const PlanIcon = planIcons[productIndex];

                return (
                  <Box
                    key={product.id}
                    className={cx(
                      "h-auto flex-row border-border-light border-b p-12",
                      planStyles[productIndex].cardContainer,
                      {
                        "rounded-t-4xl": productIndex === 0,
                        // "rounded-b-4xl": productIndex === pricingProducts.length - 1,
                      },
                    )}
                  >
                    <Box className="flex-1 flex-row">
                      <Center
                        className={cx("mr-6 h-12 w-12 rounded-xl border", planStyles[productIndex].iconContainer)}
                      >
                        <PlanIcon className={cx("h-6 w-6 opacity-80", planStyles[productIndex].icon)} />
                      </Center>
                      <Box className="flex-1 pt-1">
                        <Box className="-mt-2 flex-row items-center">
                          <Heading size="xl" className={cx("font-base", planStyles[productIndex].title)}>
                            {product.name}
                          </Heading>
                          {productIndex === 1 && (
                            <Badge
                              size="xs"
                              className="-translate-y-[1px] ml-2 opacity-50 border-orange-500"
                            >
                              <Text className="text-background text-xs uppercase">Melhor custo-benefício</Text>
                            </Badge>
                          )}
                        </Box>
                        <Text className="max-w-[80%] flex-1 text-text-quaternary">{product.description}</Text>
                        <Box className="mt-4 w-full flex-row flex-wrap gap-2">
                          {product.features?.map((feature: StripeProductFeature, index: number) => {
                            // Handle both string and object feature formats
                            const { name, icon, disabled } = parseFeatureText(feature?.name);
                            const IconComponent = featureIcons[icon || "checkmark"];

                            return (
                              <Badge
                                variant="outline"
                                rounded="full"
                                size="sm"
                                beforeIcon={IconComponent}
                                key={`${product.id}-feature-${index}`}
                                className={cx("w-fit", { "opacity-60": disabled })}
                              >
                                <Text>{name}</Text>
                              </Badge>
                            );
                          })}
                        </Box>
                      </Box>
                    </Box>
                    <Box className="ml-16 min-w-60">
                      <Text className="mb-1.5 text-text-quaternary text-xs uppercase">
                        {productIndex === 0 ? "Plano individual" : "Ideal para equipes"}
                      </Text>
                      <Box className="w-full flex-row items-start justify-start">
                        <Text className="mr-1 translate-y-1.5 text-lg text-text-secondary">R$</Text>
                        <Text className={cx("w-fit font-bold text-4xl", planStyles[productIndex].price)}>
                          {price.tiers
                            ? getTotalTieredPrice(teamMembers.length, price.tiers)
                            : price.unit_amount && (price.unit_amount / 100).toFixed(2)}
                        </Text>
                        <Box className="ml-1 translate-y-1.5">
                          {productIndex > 0 && (
                            <Text className="-mb-[4px] translate-x-1 text-sm text-text-tertiary">por usuário</Text>
                          )}
                          <Text className="text-sm text-text-tertiary">{isYearly ? " /ano" : " /mês"}</Text>
                        </Box>
                      </Box>
                      <Button
                        rounded="full"
                        variant="outline"
                        className={cx("mt-4 w-full")}
                        onPress={() => handleSelectPlan(product)}
                      >
                        <Text>
                          Selecionar <Text className={planStyles[productIndex].buttonPlanName}>{product.name}</Text>
                        </Text>
                        <ArrowRightIcon className="ml-4 h-5 w-5 text-text-quaternary" />
                      </Button>
                    </Box>
                  </Box>
                );
              })}
              <Center className="rounded-b-4xl bg-background p-16 shadow-[inset_0_2px_8px_0_rgba(var(--color-gray-950),0.05)]">
                <Heading size="lg" className="text-text-secondary">
                  Ficou com alguma dúvida?
                </Heading>
                <Text className="text-center text-text-quaternary">
                  Compare as características de cada plano e {"\n"} calcule os custos com base no número de usuários.
                </Text>
                <Box className="mt-8 flex-row gap-4">
                  <Button size="lg" rounded="full" variant="outline">
                    <Text>Comparar planos</Text>
                  </Button>
                  <Button size="lg" rounded="full" className="bg-gray-950">
                    <Text>Falar com especialista</Text>
                  </Button>
                </Box>
              </Center>
            </Box>
          )}
        </Box>
        <Center className="mt-16 w-full border-border-light border-t bg-background-dark">
          <FAQSection />
        </Center>
      </Box>
    </ScrollView>
  );
};

export default PricingTableStep;
