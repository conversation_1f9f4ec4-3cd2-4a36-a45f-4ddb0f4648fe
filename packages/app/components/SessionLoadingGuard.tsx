import { useSupabaseSession } from "@/context/supabase-session";
import { brand } from "@/theme/colors";
import { ActivityIndicator } from "react-native";
import { Center } from "./ui";

interface SessionLoadingGuardProps {
  children: React.ReactNode;
}

export function SessionLoadingGuard({ children }: SessionLoadingGuardProps) {
  const { loading: isSessionLoading } = useSupabaseSession();

  // Show loading indicator while session is being loaded
  if (isSessionLoading) {
    return (
      <Center className="absolute z-[999] h-full w-full flex-1 bg-background transition-all duration-300 ease-in-out">
        <ActivityIndicator size="large" color={`rgb(${brand[700]})`} />
      </Center>
    );
  }

  // Session is loaded, render children
  return <>{children}</>;
}
