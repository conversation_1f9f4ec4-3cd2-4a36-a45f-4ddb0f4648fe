// ==============================================================
// Those values are a config
// not to be imported or used directly
const propertyTypes = {
  residential: {
    apartment: "Apartamento",
    house: "Casa",
    condominium_house: "Casa de Condomínio",
    penthouse: "Cobertura",
    flat: "Flat",
    studio: "Kitnet / Conjugado",
    lot: "Lote / Terreno",
    townhouse: "Sobrado",
    residential_building: "Edifício Residencial",
    rural_property: "Fazenda / Sítios / Chácaras",
  } as const,
  commercial: {
    medical_office: "Consultório",
    warehouse: "Galpão / Depósito / Armazém",
    commercial_property: "Imóvel Comercial",
    commercial_lot: "Lote / Terreno",
    store: "Ponto Comercial / Loja / Box",
    office: "Sala/Conjunto",
    commercial_building: "Prédio/casa comercial",
  } as const,
};
// Those values are a config
// not to be imported or used directly
// ==============================================================

export const PROPERTY_TYPE_VALUE_LABEL_MAP = {
  ...propertyTypes.residential,
  ...propertyTypes.commercial,
} as const;

export const RESIDENTIAL_PROPERTY_TYPE_VALUES = Object.keys(propertyTypes.residential) as Array<
  keyof typeof propertyTypes.residential
>;
export const COMMERCIAL_PROPERTY_TYPE_VALUES = Object.keys(propertyTypes.commercial) as Array<
  keyof typeof propertyTypes.commercial
>;

export const RESIDENTIAL_PROPERTY_TYPE_LABELS = Object.values(propertyTypes.residential) as Array<
  (typeof propertyTypes.residential)[keyof typeof propertyTypes.residential]
>;
export const COMMERCIAL_PROPERTY_TYPE_LABELS = Object.values(propertyTypes.residential) as Array<
  (typeof propertyTypes.residential)[keyof typeof propertyTypes.residential]
>;

export const RESIDENTIAL_PROPERTY_TYPES = Object.entries(propertyTypes.residential).map(([key, value]) => ({
  label: value as (typeof propertyTypes.residential)[keyof typeof propertyTypes.residential],
  value: key as keyof typeof propertyTypes.residential,
}));

export const COMMERCIAL_PROPERTY_TYPES = Object.entries(propertyTypes.commercial).map(([key, value]) => ({
  label: value as (typeof propertyTypes.commercial)[keyof typeof propertyTypes.commercial],
  value: key as keyof typeof propertyTypes.commercial,
}));

export const PROPERTY_TYPES = [...RESIDENTIAL_PROPERTY_TYPES, ...COMMERCIAL_PROPERTY_TYPES];
export const PROPERTY_TYPE_VALUES = [...RESIDENTIAL_PROPERTY_TYPE_VALUES, ...COMMERCIAL_PROPERTY_TYPE_VALUES] as const;
export const PROPERTY_TYPE_LABELS = [...RESIDENTIAL_PROPERTY_TYPE_LABELS, ...COMMERCIAL_PROPERTY_TYPE_LABELS] as const;
