// ==============================================================
// Those values are a config
// not to be imported or used directly

const amenities = {
  property: {
    barbecue_grill: "Churrasqueira",
    gourmet_space: "Espaço gourmet",
    garden: "Jardim",
    pool: "Piscina",
    backyard: "Quintal",
    water_heating: "Aquecimento de água",
    heating: "Aquecimento de ambiente",
    air_conditioning: "Ar-condicionado",
    internet: "Conexão à internet",
    garage: "Garagem",
    fireplace: "Lareira",
    laundry: "Lavanderia",
    sauna: "Sauna",
    spa: "Spa",
    security_system: "Sistema de segurança",
  } as const,
  building: {
    shared_barbecue_grill: "Churrasqueira",
    shared_gourmet_space: "Espaço gourmet",
    bicycle_storage: "Bicicletário",
    intercom: "Interfone",
    gym: "Academia",
    green_area: "Espaço verde / Parque",
    playground: "Playground",
    shared_pool: "Piscina",
    tennis_court: "Quadra de tênis",
    sports_area: "Quadra poliesportiva",
    party_room: "Salão de festas",
    game_room: "Salão de jogos",
    storage: "Depósito",
    shared_laundry: "Lavanderia",
    elevator: "Elevador",
    shared_garage: "Estacionamento",
    shared_water_heating: "Água aquecida central",
    power_generator: "Gerador elétrico",
    reception: "Recepção / Portaria",
    shared_sauna: "Sauna",
    shared_spa: "Spa",
    shared_security_system: "Sistema de segurança",
    gated_community: "Condomínio fechado",
    private_security: "Segurança particular",
  } as const,
};
// Those values are a config
// not to be imported or used directly
// ==============================================================

export const PROPERTY_AMENITY_VALUES = Object.keys(amenities.property) as Array<keyof typeof amenities.property>;
export const BUILDING_AMENITY_VALUES = Object.keys(amenities.building) as Array<keyof typeof amenities.building>;

export const PROPERTY_AMENITY_LABELS = Object.values(amenities.property) as Array<
  (typeof amenities.property)[keyof typeof amenities.property]
>;
export const BUILDING_AMENITY_LABELS = Object.values(amenities.property) as Array<
  (typeof amenities.property)[keyof typeof amenities.property]
>;

export const PROPERTY_AMENITIES = Object.entries(amenities.property).map(([key, value]) => ({
  label: value as (typeof amenities.property)[keyof typeof amenities.property],
  value: key as keyof typeof amenities.property,
}));

export const BUILDING_AMENITIES = Object.entries(amenities.building).map(([key, value]) => ({
  label: value as (typeof amenities.building)[keyof typeof amenities.building],
  value: key as keyof typeof amenities.building,
}));

export const AMENITIES = [...PROPERTY_AMENITIES, ...BUILDING_AMENITIES];
export const AMENITY_VALUES = [...PROPERTY_AMENITY_VALUES, ...BUILDING_AMENITY_VALUES] as const;
export const AMENITY_LABELS = [...PROPERTY_AMENITY_LABELS, ...BUILDING_AMENITY_LABELS] as const;
