import { Box, Checkbox, Heading, Text } from "@/components/ui";
import { BUILDING_AMENITIES, PROPERTY_AMENITIES } from "@/constants/amenities";
import type { Property } from "@/types";

interface PropertyAmenitiesSectionProps {
  property: Property;
}

export const PropertyAmenitiesSection = ({ property }: PropertyAmenitiesSectionProps) => {
  const { property_amenities, building_amenities } = property;

  // Filter out amenities that are false
  const activePropertyAmenities = property_amenities
    ? PROPERTY_AMENITIES.filter(
        (amenity) => property_amenities[amenity.value as keyof typeof property_amenities] === true,
      )
    : [];

  const activeBuildingAmenities = building_amenities
    ? BUILDING_AMENITIES.filter(
        (amenity) => building_amenities[amenity.value as keyof typeof building_amenities] === true,
      )
    : [];

  // If there are no active amenities, don't render the section
  if (activePropertyAmenities.length === 0 && activeBuildingAmenities.length === 0) {
    return null;
  }

  return (
    <Box className="my-6 rounded-lg border border-border bg-background p-4">
      <Heading size="md" className="mb-4">
        Comodidades
      </Heading>

      {activePropertyAmenities.length > 0 && (
        <Box className="mb-4">
          <Text className="mb-2 font-medium text-text-secondary">Comodidades do imóvel</Text>
          <Box className="flex-row flex-wrap">
            {activePropertyAmenities.map((amenity, index) => (
              <Box key={`property-amenity-${amenity.value}-${index}`} className="mb-2 w-1/2 flex-row items-center pr-2">
                <Box className="flex-row items-center gap-2">
                  <Checkbox checked onCheckedChange={() => null} />
                  <Text className="text-text-secondary">{amenity.label}</Text>
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
      )}

      {activeBuildingAmenities.length > 0 && (
        <Box>
          <Text className="mb-2 font-medium text-text-secondary">Comodidades do condomínio/prédio</Text>
          <Box className="flex-row flex-wrap">
            {activeBuildingAmenities.map((amenity, index) => (
              <Box key={`building-amenity-${amenity.value}-${index}`} className="mb-2 w-1/2 flex-row items-center pr-2">
                <Box className="flex-row items-center gap-2">
                  <Checkbox checked onCheckedChange={() => null} />
                  <Text className="text-text-secondary">{amenity.label}</Text>
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default PropertyAmenitiesSection;
