import BedDoubleIcon from "@/assets/icons/bed-double.svg";
import BedSingle from "@/assets/icons/bed-single.svg";
// Icons
import BuildingIcon from "@/assets/icons/building.svg";
import BuiltAreaIcon from "@/assets/icons/built-area.svg";
import CameraIcon from "@/assets/icons/camera-01.svg";
import CarIcon from "@/assets/icons/car-05.svg";
import ArrowLeftIcon from "@/assets/icons/circle-arrow-left.svg";
import LocationIcon from "@/assets/icons/location-04.svg";
import ToiletIcon from "@/assets/icons/toilet.svg";
import TotalAreaIcon from "@/assets/icons/total-area.svg";
import SaveIcon from "@/assets/icons/tick-02.svg";
import EditIcon from "@/assets/icons/edit-02.svg";
import ImageIcon from "@/assets/icons/image.svg";
import PageLayout, { PageRow } from "@/components/PageLayout";
import { Box, Button, Heading, Text, Input, Textarea } from "@/components/ui";
import { PROPERTY_TYPE_VALUE_LABEL_MAP } from "@/constants/propertyTypes";
import { usePropertyDetails } from "@/hooks";
import { usePropertyMedia } from "@/hooks/usePropertyMedia";
import { useUpdateProperty } from "@/hooks/mutations";
import type { Property } from "@/types";
import { formatCurrency } from "@/utils";
import { Image } from "expo-image";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useState } from "react";
import { ActivityIndicator, ScrollView } from "react-native";
import PropertyAmenitiesSection from "./PropertyAmenitiesSection";

const PropertyMediaGallery = ({ propertyId }: { propertyId: string }) => {
  const { data: mediaItems, isLoading, error } = usePropertyMedia(propertyId);
  const [activeImage, setActiveImage] = useState<number>(0);

  if (isLoading) {
    return (
      <Box className="h-[320px] w-full items-center justify-center bg-background-dark">
        <ActivityIndicator size="large" />
      </Box>
    );
  }

  if (error || !mediaItems || mediaItems.length === 0) {
    return (
      <Box className="h-[320px] w-full items-center justify-center bg-background-dark">
        <Box className="rounded-full bg-background-dark p-4">
          <CameraIcon className="h-16 w-16 text-text-tertiary" />
        </Box>
        <Text className="mt-4 text-text-tertiary">No media available</Text>
      </Box>
    );
  }

  // Get the indices for the images we want to display
  const mainImageIndex = 0; // First image

  // Calculate indices for the grid images (the last 4 images)
  const gridImageIndices: (number | null)[] = [];
  if (mediaItems.length > 1) {
    // If we have more than 5 images, use the last 4
    if (mediaItems.length > 5) {
      for (let i = mediaItems.length - 4; i < mediaItems.length; i++) {
        gridImageIndices.push(i);
      }
    }
    // If we have 2-5 images, use all except the first one
    else {
      for (let i = 1; i < mediaItems.length; i++) {
        gridImageIndices.push(i);
      }
      // If we have fewer than 5 images, add null placeholders to fill the grid
      while (gridImageIndices.length < 4) {
        gridImageIndices.push(null);
      }
    }
  }

  console.log('Main image index:', mainImageIndex);
  console.log('Grid image indices:', gridImageIndices);
  console.log('Total images:', mediaItems.length);

  return (
    <Box className="h-[320px] w-full">
      {/* Main container */}
      <Box className="flex flex-row h-full w-full">
        {/* Main image (left side) */}
        <Box className="w-[59%] h-full pr-1">
          <Box className="rounded-md overflow-hidden h-full w-full">
            <Image
              source={{ uri: mediaItems[activeImage].url }}
              style={{ width: "100%", height: "100%" }}
              contentFit="cover"
              transition={200}
              cachePolicy="memory-disk"
            />
          </Box>
        </Box>

        {/* Right side grid */}
        <Box className="w-[40%] h-full">
          {/* Top row */}
          <Box className="flex flex-row h-[49.5%] mb-[1%]">
            {/* Top left */}
            <Box className="w-[49.5%] h-full mr-[1%]">
              {gridImageIndices.length > 0 && gridImageIndices[0] !== null ? (
                <Box
                  className="rounded-md overflow-hidden h-full w-full"
                  onTouchEnd={() => setActiveImage(gridImageIndices[0] as number)}
                >
                  <Image
                    source={{ uri: mediaItems[gridImageIndices[0] as number].url }}
                    style={{ width: "100%", height: "100%" }}
                    contentFit="cover"
                    transition={200}
                    cachePolicy="memory-disk"
                  />
                </Box>
              ) : (
                <Box className="bg-background-dark rounded-md h-full w-full items-center justify-center">
                  <ImageIcon className="h-8 w-8 text-text-tertiary" />
                </Box>
              )}
            </Box>

            {/* Top right */}
            <Box className="w-[49.5%] h-full">
              {gridImageIndices.length > 1 && gridImageIndices[1] !== null ? (
                <Box
                  className="rounded-md overflow-hidden h-full w-full"
                  onTouchEnd={() => setActiveImage(gridImageIndices[1] as number)}
                >
                  <Image
                    source={{ uri: mediaItems[gridImageIndices[1] as number].url }}
                    style={{ width: "100%", height: "100%" }}
                    contentFit="cover"
                    transition={200}
                    cachePolicy="memory-disk"
                  />
                </Box>
              ) : (
                <Box className="bg-background-dark rounded-md h-full w-full items-center justify-center">
                  <ImageIcon className="h-8 w-8 text-text-tertiary" />
                </Box>
              )}
            </Box>
          </Box>

          {/* Bottom row */}
          <Box className="flex flex-row h-[49.5%]">
            {/* Bottom left */}
            <Box className="w-[49.5%] h-full mr-[1%]">
              {gridImageIndices.length > 2 && gridImageIndices[2] !== null ? (
                <Box
                  className="rounded-md overflow-hidden h-full w-full"
                  onTouchEnd={() => setActiveImage(gridImageIndices[2] as number)}
                >
                  <Image
                    source={{ uri: mediaItems[gridImageIndices[2] as number].url }}
                    style={{ width: "100%", height: "100%" }}
                    contentFit="cover"
                    transition={200}
                    cachePolicy="memory-disk"
                  />
                </Box>
              ) : (
                <Box className="bg-background-dark rounded-md h-full w-full items-center justify-center">
                  <ImageIcon className="h-8 w-8 text-text-tertiary" />
                </Box>
              )}
            </Box>

            {/* Bottom right */}
            <Box className="w-[49.5%] h-full">
              {gridImageIndices.length > 3 && gridImageIndices[3] !== null ? (
                <Box
                  className="rounded-md overflow-hidden h-full w-full"
                  onTouchEnd={() => setActiveImage(gridImageIndices[3] as number)}
                >
                  <Image
                    source={{ uri: mediaItems[gridImageIndices[3] as number].url }}
                    style={{ width: "100%", height: "100%" }}
                    contentFit="cover"
                    transition={200}
                    cachePolicy="memory-disk"
                  />
                </Box>
              ) : (
                <Box className="bg-background-dark rounded-md h-full w-full items-center justify-center">
                  <ImageIcon className="h-8 w-8 text-text-tertiary" />
                </Box>
              )}
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Photo count indicator */}
      <Box className="absolute right-4 bottom-4 bg-black/70 px-3 py-1 rounded-md">
        <Text className="text-white font-medium">
          {mediaItems.length} {mediaItems.length === 1 ? 'foto' : 'fotos'}
        </Text>
      </Box>
    </Box>
  );
};

const PropertyFeaturesSection = ({ property }: { property: any }) => {
  const features = [
    {
      icon: BedDoubleIcon,
      value: property.suites_count || 0,
      label: "Suítes",
    },
    {
      icon: BedSingle,
      value: property.rooms_count || 0,
      label: "Quartos",
    },
    {
      icon: ToiletIcon,
      value: property.bathrooms_count || 0,
      label: "Banheiros",
    },
    {
      icon: CarIcon,
      value: property.parking_spots_count || 0,
      label: "Vagas",
    },
    {
      icon: BuiltAreaIcon,
      value: `${property.built_area || 0} m²`,
      label: "Construída",
    },
    {
      icon: TotalAreaIcon,
      value: `${property.total_area || 0} m²`,
      label: "Total",
    },
  ];

  return (
    <Box className="mt-6 rounded-lg border border-border bg-background p-4">
      <Heading size="md" className="mb-4">
        Características
      </Heading>
      <Box className="flex-row flex-wrap gap-4">
        {features.map((feature, index) => (
          <Box key={`feature-${feature.label}-${index}`} className="mb-2 w-[calc(33.33%-16px)] flex-row items-center">
            <feature.icon className="mr-2 h-5 w-5 text-text-secondary" />
            <Box className="flex-row items-center gap-1">
              <Text className="font-medium">{feature.value}</Text>
              <Text className="text-sm text-text-tertiary">{feature.label}</Text>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

const PropertyPricesSection = ({
  prices,
  isEditMode = false,
  editedPrices = {},
  onPriceChange = () => {}
}: {
  prices: any;
  isEditMode?: boolean;
  editedPrices?: Record<string, number>;
  onPriceChange?: (field: string, value: number) => void;
}) => {
  if (!prices) return null;

  const priceItems = [
    { label: "Venda", value: prices.sale_price, field: "sale_price" },
    { label: "Locação mensal", value: prices.rent_price, field: "rent_price" },
    { label: "Diária", value: prices.bnb_price, field: "bnb_price" },
  ].filter((item) => isEditMode || item.value > 0);

  const taxItems = [
    { label: "Condomínio", value: prices.condominium_monthly_tax, field: "condominium_monthly_tax" },
    { label: "IPTU", value: prices.iptu_monthly_tax, field: "iptu_monthly_tax" },
    { label: "Seguro", value: prices.insurance_monthly_tax, field: "insurance_monthly_tax" },
    { label: "Outras taxas", value: prices.other_monthly_tax, field: "other_monthly_tax" },
  ].filter((item) => isEditMode || item.value > 0);

  if (priceItems.length === 0 && taxItems.length === 0) return null;

  return (
    <Box className="mt-6 rounded-lg border border-border bg-background p-4">
      <Heading size="md" className="mb-4">
        Valores
      </Heading>

      {priceItems.length > 0 && (
        <Box className="mb-4">
          <Text className="mb-2 font-medium text-text-secondary">Preços</Text>
          <Box className="flex-row flex-wrap gap-4">
            {priceItems.map((item, index) => (
              <Box key={`price-item-${item.label}-${index}`} className="mb-2 w-[calc(33.33%-16px)]">
                <Text className="text-sm text-text-tertiary">{item.label}</Text>
                {isEditMode ? (
                  <Input
                    value={String(editedPrices[item.field] !== undefined ? editedPrices[item.field] : (item.value || 0))}
                    onChangeText={(text) => {
                      // Remove non-numeric characters
                      const numericValue = text.replace(/[^0-9]/g, '');
                      onPriceChange(item.field, numericValue ? parseInt(numericValue, 10) : 0);
                    }}
                    keyboardType="numeric"
                    className="font-semibold text-lg"
                  />
                ) : (
                  <Text className="font-semibold text-lg">{formatCurrency(item.value)}</Text>
                )}
              </Box>
            ))}
          </Box>
        </Box>
      )}

      {taxItems.length > 0 && (
        <Box>
          <Text className="mb-2 font-medium text-text-secondary">Taxas mensais</Text>
          <Box className="flex-row flex-wrap gap-4">
            {taxItems.map((item, index) => (
              <Box key={`price-item-${item.label}-${index}`} className="mb-2 w-[calc(33.33%-16px)]">
                <Text className="text-sm text-text-tertiary">{item.label}</Text>
                {isEditMode ? (
                  <Input
                    value={String(editedPrices[item.field] !== undefined ? editedPrices[item.field] : (item.value || 0))}
                    onChangeText={(text) => {
                      // Remove non-numeric characters
                      const numericValue = text.replace(/[^0-9]/g, '');
                      onPriceChange(item.field, numericValue ? parseInt(numericValue, 10) : 0);
                    }}
                    keyboardType="numeric"
                    className="font-semibold text-lg"
                  />
                ) : (
                  <Text className="font-semibold text-lg">{formatCurrency(item.value)}</Text>
                )}
              </Box>
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default function PropertyDetails() {
  const { propertyId } = useLocalSearchParams<{ propertyId: string }>();
  const router = useRouter();
  const { data: property, isLoading, error } = usePropertyDetails(propertyId);
  const updateProperty = useUpdateProperty();
  const [isEditMode, setIsEditMode] = useState(false);
  const [editedProperty, setEditedProperty] = useState<Partial<Property>>({});
  const [editedPrices, setEditedPrices] = useState<Record<string, number>>({});

  const handleGoBack = () => {
    router.back();
  };

  const handlePriceChange = (field: string, value: number) => {
    setEditedPrices(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const toggleEditMode = () => {
    if (isEditMode) {
      // Save changes
      if (property) {
        const payload: any = {
          property: {
            id: property.id,
            title: editedProperty.title || property.title,
            description: editedProperty.description || property.description,
          }
        };

        // Only include prices if there are changes
        if (Object.keys(editedPrices).length > 0 && property.prices?.id) {
          payload.prices = {
            id: property.prices.id,
            ...editedPrices
          };
        }

        updateProperty.mutate(payload, {
          onSuccess: () => {
            setIsEditMode(false);
            setEditedPrices({});
          },
          onError: (error) => {
            console.error("Error updating property:", error);
          }
        });
      }
    } else {
      // Enter edit mode
      setEditedProperty({
        title: property?.title,
        description: property?.description,
      });
      setEditedPrices({});
      setIsEditMode(true);
    }
  };

  if (isLoading) {
    return (
      <PageLayout
        pageTitle="Detalhes do imóvel"
        pageIcon={BuildingIcon}
        actions={[
          <Button key="back-button" variant="outline" size="sm" onPress={handleGoBack} className="mr-2">
            <ArrowLeftIcon className="mr-2 h-4 w-4 text-text" />
            <Text>Voltar</Text>
          </Button>,
        ]}
      >
        <PageRow>
          <Box className="mt-8 w-full items-center justify-center">
            <ActivityIndicator size="large" />
            <Text className="mt-4 text-text-tertiary">Carregando detalhes do imóvel...</Text>
          </Box>
        </PageRow>
      </PageLayout>
    );
  }

  if (error || !property) {
    return (
      <PageLayout
        pageTitle="Detalhes do imóvel"
        pageIcon={BuildingIcon}
        actions={[
          <Button key="back-button" variant="outline" size="sm" onPress={handleGoBack} className="mr-2">
            <ArrowLeftIcon className="mr-2 h-4 w-4 text-text" />
            <Text>Voltar</Text>
          </Button>,
        ]}
      >
        <PageRow>
          <Box className="mt-8 w-full items-center justify-center">
            <Text className="text-error">Erro ao carregar detalhes do imóvel.</Text>
            <Text className="mt-2 text-sm text-text-tertiary">
              {error instanceof Error ? error.message : "Ocorreu um erro desconhecido."}
            </Text>
            <Button className="mt-4" onPress={() => router.replace("/imoveis")}>
              Voltar para lista de imóveis
            </Button>
          </Box>
        </PageRow>
      </PageLayout>
    );
  }

  const propertyType =
    PROPERTY_TYPE_VALUE_LABEL_MAP[property.type as keyof typeof PROPERTY_TYPE_VALUE_LABEL_MAP] || "Imóvel";

  const addressText = [
    property.address?.street?.name,
    property.address?.street_number,
    property.address?.neighborhood?.name,
    property.address?.city?.name,
    property.address?.state,
  ]
    .filter(Boolean)
    .join(", ");

  return (
    <PageLayout
      pageTitle={isEditMode ? "Editar imóvel" : (property.title || "Detalhes do imóvel")}
      pageIcon={BuildingIcon}
      actions={[
        <Button key="back-button" variant="outline" size="sm" onPress={handleGoBack} className="mr-2">
          <ArrowLeftIcon className="mr-2 h-4 w-4 text-text" />
          <Text>Voltar</Text>
        </Button>,
        <Button
          key="edit-save-button"
          variant={isEditMode ? "default" : "outline"}
          size="sm"
          onPress={toggleEditMode}
          isLoading={updateProperty.isPending}
        >
          {isEditMode ? (
            <>
              <SaveIcon className="mr-2 h-4 w-4 text-text-inverse" />
              <Text className="text-text-inverse">Salvar</Text>
            </>
          ) : (
            <>
              <EditIcon className="mr-2 h-4 w-4 text-text" />
              <Text>Editar</Text>
            </>
          )}
        </Button>,
      ]}
    >
      <PageRow>
        <Box className="mt-4 w-full">
          <Box className="mb-4 flex-row">
            <Text className="text-lg text-text-tertiary">{propertyType}</Text>
          </Box>

          <PropertyMediaGallery propertyId={property.id} />

          <ScrollView className="w-full" showsVerticalScrollIndicator={false}>
            <Box className="mt-4">
              {isEditMode ? (
                <Input
                  value={editedProperty.title || property.title}
                  onChangeText={(text) => setEditedProperty({ ...editedProperty, title: text })}
                  className="mb-2 text-2xl font-bold"
                />
              ) : (
                <Heading size="2xl">{property.title}</Heading>
              )}
              <Box className="mt-2 flex-row items-center">
                <LocationIcon className="mr-2 h-4 w-4 text-text-tertiary" />
                <Text className="text-text-secondary">{addressText}</Text>
              </Box>
            </Box>

            <Box className="mt-6 rounded-lg border border-border bg-background p-4">
              <Heading size="md" className="mb-4">
                Descrição
              </Heading>
              {isEditMode ? (
                <Textarea
                  value={editedProperty.description || property.description || ""}
                  onChangeText={(text) => setEditedProperty({ ...editedProperty, description: text })}
                  className="min-h-[100px] p-2"
                />
              ) : (
                <Text className="text-text-secondary">{property.description}</Text>
              )}
            </Box>

            <PropertyFeaturesSection property={property} />

            <PropertyPricesSection
              prices={property.prices}
              isEditMode={isEditMode}
              editedPrices={editedPrices}
              onPriceChange={handlePriceChange}
            />

            <PropertyAmenitiesSection property={property} />
          </ScrollView>
        </Box>
      </PageRow>
    </PageLayout>
  );
}
