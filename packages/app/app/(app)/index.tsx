import HomeIcon from "@/assets/icons/dashboard-speed-01.svg";
import DataAnalysisIllustration from "@/assets/illustrations/storyset/Design stats-rafiki.svg";
import PresentationIllustration from '@platform/assets/illustrations/presentation.svg'
import { EmptyPagePlaceholder } from "@/components/EmptyPagePlaceholder";
import PageLayout from "@/components/PageLayout";

export default function Home() {
  return (
    <PageLayout
      pageTitle="Visão geral"
      description="Gerencie os seus imóveis, monitore estatísticas e movimentações."
      pageIcon={HomeIcon}
      searchLabel="Procurar por imóveis ou cliente..."
      featuredSearchBar
    >
      <EmptyPagePlaceholder
        illustration={PresentationIllustration}
        title="Business Intelligence"
        description="Dados insuficientes para exibir o painel de inteligência de negócios. Este painel estará disponível quando houverem dados suficientes para exibir."
      />
    </PageLayout>
  );
}
