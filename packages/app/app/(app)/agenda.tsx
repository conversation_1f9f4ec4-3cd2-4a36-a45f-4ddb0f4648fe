import { <PERSON><PERSON>, <PERSON> } from "expo-router";

import { Button } from "@/components/Button";
import { Container } from "@/components/Container";
import { ScreenContent } from "@/components/ScreenContent";
import {
  Box,
  Select,
  SelectBackdrop,
  SelectContent,
  SelectDragIndicator,
  SelectDragIndicatorWrapper,
  SelectIcon,
  SelectInput,
  SelectPortal,
  SelectTrigger,
} from "@/components/ui";

export default function Home() {
  return (
    <Box className="flex-1 overflow-hidden bg-background">
      <Box className="p-16">
        <Select>
          <SelectTrigger>
            <SelectInput placeholder={"sfdsd" || "Selecione uma opção"} />
            <SelectIcon size="xl" className="text-text-tertiary" />
          </SelectTrigger>
          <SelectPortal>
            <SelectBackdrop />
            <SelectContent>
              <SelectDragIndicatorWrapper>
                <SelectDragIndicator />
              </SelectDragIndicatorWrapper>
            </SelectContent>
          </SelectPortal>
        </Select>
      </Box>
      <Stack.Screen options={{ title: "Agenda" }} />
    </Box>
  );
}
