import AppSidebar from "@/components/AppSidebar";
import PausedSubscriptionModal from "@/components/PausedSubscriptionModal";
import { Box, Center } from "@/components/ui";
import { useDrawers } from "@/context/drawer";
import { useModals } from "@/context/modal";
import { useSupabaseSession } from "@/context/supabase-session";
import { useCurrentTeamId, useCurrentUserProfile, useTeamAccountsList } from "@/hooks";
import { useSwitchTeamAccount } from "@/hooks";
import { useBillingStatus } from "@/hooks/useBillingStatus";
import { brand, gray } from "@/theme/colors";
import { PortalHost } from "@gorhom/portal";
import { LinearGradient } from "expo-linear-gradient";
import { Redirect, Slot } from "expo-router";
import { useEffect, useState } from "react";
import { ActivityIndicator } from "react-native";

export default function Layout() {
  const drawers = useDrawers();
  const modals = useModals();
  const { session } = useSupabaseSession();
  const [currentTeamId] = useCurrentTeamId();
  const { data: currentUserProfile, isLoading: isUserProfileLoading } = useCurrentUserProfile();
  const { data: teamAccounts = [], isLoading: isUserOrgsLoading, error: userOrgsError } = useTeamAccountsList();
  const { data: billingStatus, isLoading: isBillingStatusLoading } = useBillingStatus(currentTeamId);
  const switchAccount = useSwitchTeamAccount();

  const isReady = !isUserProfileLoading && !isUserOrgsLoading && !isBillingStatusLoading;

  useEffect(() => {
    if (!isReady || !session) return;

    if ((!!session && !teamAccounts) || teamAccounts.length === 0) {
      drawers.openCreateTeamDrawer();
    }
    if (!!session && teamAccounts.length > 0 && !currentTeamId) {
      modals.openTeamPickerModal();
    }
  }, [isReady, session, currentTeamId, teamAccounts, drawers.openCreateTeamDrawer, modals.openTeamPickerModal]);

  // Redirect to login if no session
  if (!session) {
    return <Redirect href="/entrar" />;
  }

  // Show ActivityIndicator until everything is loaded
  if (!isReady) {
    return (
      <Center className={"absolute z-[999] h-full w-full flex-1 bg-background transition-all duration-300 ease-in-out"}>
        <ActivityIndicator size="large" color={`rgb(${brand[700]})`} />
      </Center>
    );
  }

  // Check if subscription is paused
  const isSubscriptionPaused = billingStatus?.status === "paused";

  return (
    <>
      <Box className="h-full w-full flex-row bg-background">
        <AppSidebar />
        <Box className="flex-1 overflow-y-auto z-10">
          <Slot />
        </Box>
        <LinearGradient
          colors={[
            `rgba(${brand["800"]},0.04)`,
            `rgba(${brand["800"]},0.02)`,
            "rgba(0,0,0,0)",
          ]}
          className="absolute top-0 right-0 h-[30vh] w-full"
        />
      </Box>
      <PortalHost name="testerer" />
      {isSubscriptionPaused && <PausedSubscriptionModal isOpen={true} />}
    </>
  );
}
