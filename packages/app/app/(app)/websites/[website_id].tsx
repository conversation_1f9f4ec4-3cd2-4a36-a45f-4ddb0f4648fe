import PlusIcon from "@/assets/icons/add.svg";
import TrashIcon from "@/assets/icons/delete-02.svg";
import WebsiteIcon from "@/assets/icons/globe-02.svg";
import ExternalLinkIcon from "@/assets/icons/link-square-01.svg";
import LiveIcon from "@/assets/icons/live-02.svg";
import PauseIcon from "@/assets/icons/pause.svg";
import RefreshIcon from "@/assets/icons/refresh.svg";
import SettingsIcon from "@/assets/icons/settings-01.svg";
import SaveIcon from "@/assets/icons/tick-02.svg";
import { DomainVerificationBadge } from "@/components/DomainVerificationBadge";
import Modal, { ModalFooter, ModalHeader } from "@/components/Modal";
import PageLayout, { PageRow } from "@/components/PageLayout";
import { Badge, Box, Button, Center, ColorPicker, Heading, Input, Switch, Text, Textarea } from "@/components/ui";
import { useAxios, useCustomDomains, useSupabase } from "@/hooks";
import { useUploadWebsiteMedia } from "@/hooks/mutations/useUploadWebsiteMedia";
import { useWebsites } from "@/hooks/useWebsites";
import type { Website } from "@/types/websites";
import { cx, getCustomDomainUrl, getSubdomainUrl } from "@/utils";
import * as ImagePicker from "expo-image-picker";
// Domain validation is handled with regex
import { type ExternalPathString, Link, useLocalSearchParams, useRouter } from "expo-router";
import { useCallback, useEffect, useState } from "react";
import { Image } from "react-native";
import { ActivityIndicator, Alert, ScrollView } from "react-native";

export default function WebsiteDetails() {
  const { website_id } = useLocalSearchParams();
  const router = useRouter();
  const supabase = useSupabase();
  const { getWebsite, updateWebsite, updateWebsiteTheme, createTheme, deleteWebsite } = useWebsites();
  const { mutateAsync: uploadWebsiteMedia } = useUploadWebsiteMedia();
  const { data: customDomainsData } = useCustomDomains({
    websiteId: website_id as string,
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDomainModal, setShowDomainModal] = useState(false);
  const [website, setWebsite] = useState<Website | null>(null);
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [uploadingHero, setUploadingHero] = useState(false);
  const [uploadingInstitutional, setUploadingInstitutional] = useState(false);

  const [formData, setFormData] = useState<{
    title: string;
    subdomain: string;
    description: string;
    published: boolean;
    theme?: {
      primary_color: string;
      secondary_color: string;
      font_family?: string;
    };
    logoImageToUpload?: {
      uri: string;
      fileName: string;
      type: "image";
    } | null;
    heroImageToUpload?: {
      uri: string;
      fileName: string;
      type: "image";
    } | null;
    institutionalImageToUpload?: {
      uri: string;
      fileName: string;
      type: "image";
    } | null;
  }>({
    title: "",
    subdomain: "",
    description: "",
    published: false,
    theme: {
      primary_color: "#3B82F6",
      secondary_color: "#10B981",
      font_family: "Inter",
    },
    logoImageToUpload: null,
    heroImageToUpload: null,
    institutionalImageToUpload: null,
  });

  const loadWebsite = useCallback(async () => {
    if (!website_id) return;

    setLoading(true);
    try {
      const data = await getWebsite(website_id as string);
      setWebsite(data);
      setFormData({
        title: data.title || "",
        subdomain: data.subdomain || "",
        description: data.description || "",
        published: data.published || false,
        theme: data.theme
          ? {
              primary_color: data.theme.primary_color,
              secondary_color: data.theme.secondary_color,
              font_family: data.theme.font_family,
            }
          : {
              primary_color: "#3B82F6",
              secondary_color: "#10B981",
              font_family: "Inter",
            },
      });

      // Custom domains are loaded separately via the useCustomDomains hook
    } catch (error) {
      console.error("Error loading website:", error);
      Alert.alert("Error", "Failed to load website details");
    } finally {
      setLoading(false);
    }
  }, [website_id, getWebsite]);

  useEffect(() => {
    loadWebsite();
    return undefined;
  }, [loadWebsite]);

  const handleUpdateWebsite = async () => {
    if (!website) return;

    setSaving(true);
    try {
      // Update the website data
      await updateWebsite(website.id, {
        title: formData.title,
        subdomain: formData.subdomain,
        description: formData.description,
        published: formData.published,
      });

      // Update or create the theme
      if (formData.theme) {
        if (website.theme) {
          // Update existing theme
          await updateWebsiteTheme(website.theme.id, {
            primary_color: formData.theme.primary_color,
            secondary_color: formData.theme.secondary_color,
            font_family: formData.theme.font_family,
          });
        } else {
          // Create a new theme and associate it with the website
          const newTheme = await createTheme({
            primary_color: formData.theme.primary_color,
            secondary_color: formData.theme.secondary_color,
            font_family: formData.theme.font_family || "Inter",
          });

          // Update the website with the new theme_id
          await updateWebsite(website.id, {
            theme_id: newTheme.id,
          });
        }
      }

      // Upload logo image if selected
      if (formData.logoImageToUpload) {
        try {
          setUploadingLogo(true);

          // If there's an existing logo, delete it first
          if (website.logo_image_url) {
            // Extract the key from the URL
            const urlParts = website.logo_image_url.split("/");
            const keyParts = urlParts.slice(urlParts.indexOf("websites"));
            const key = keyParts.join("/");

            // Delete the old image
            try {
              await fetch(`${process.env.EXPO_PUBLIC_API_URL}/media/delete-file`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
                },
                body: JSON.stringify({ key }),
              });
              console.log(`Old logo image deleted: ${key}`);
            } catch (deleteError) {
              console.error("Error deleting old logo image:", deleteError);
              // Continue with upload even if delete fails
            }
          }

          // Upload the new logo
          const uploadResult = await uploadWebsiteMedia({
            websiteId: website.id,
            mediaFile: formData.logoImageToUpload,
            mediaType: "logo",
          });

          if (!uploadResult.success) {
            throw new Error(uploadResult.error || "Failed to upload logo image");
          }
        } catch (logoError) {
          console.error("Error uploading logo image:", logoError);
          Alert.alert(
            "Aviso",
            `O website foi atualizado, mas houve um erro ao fazer upload do logo: ${logoError instanceof Error ? logoError.message : String(logoError)}`,
          );
        } finally {
          setUploadingLogo(false);
        }
      }

      // Upload hero image if selected
      if (formData.heroImageToUpload) {
        try {
          setUploadingHero(true);

          // If there's an existing hero image, delete it first
          if (website.hero_image_url) {
            // Extract the key from the URL
            const urlParts = website.hero_image_url.split("/");
            const keyParts = urlParts.slice(urlParts.indexOf("websites"));
            const key = keyParts.join("/");

            // Delete the old image
            try {
              await fetch(`${process.env.EXPO_PUBLIC_API_URL}/media/delete-file`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
                },
                body: JSON.stringify({ key }),
              });
              console.log(`Old hero image deleted: ${key}`);
            } catch (deleteError) {
              console.error("Error deleting old hero image:", deleteError);
              // Continue with upload even if delete fails
            }
          }

          // Upload the new hero image
          const uploadResult = await uploadWebsiteMedia({
            websiteId: website.id,
            mediaFile: formData.heroImageToUpload,
            mediaType: "hero",
          });

          if (!uploadResult.success) {
            throw new Error(uploadResult.error || "Failed to upload hero image");
          }
        } catch (heroError) {
          console.error("Error uploading hero image:", heroError);
          Alert.alert(
            "Aviso",
            `O website foi atualizado, mas houve um erro ao fazer upload da imagem de capa: ${heroError instanceof Error ? heroError.message : String(heroError)}`,
          );
        } finally {
          setUploadingHero(false);
        }
      }

      // Upload institutional image if selected
      if (formData.institutionalImageToUpload) {
        try {
          setUploadingInstitutional(true);

          // If there's an existing institutional image, delete it first
          if (website.institutional_image_url) {
            // Extract the key from the URL
            const urlParts = website.institutional_image_url.split("/");
            const keyParts = urlParts.slice(urlParts.indexOf("websites"));
            const key = keyParts.join("/");

            // Delete the old image
            try {
              await fetch(`${process.env.EXPO_PUBLIC_API_URL}/media/delete-file`, {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
                },
                body: JSON.stringify({ key }),
              });
              console.log(`Old institutional image deleted: ${key}`);
            } catch (deleteError) {
              console.error("Error deleting old institutional image:", deleteError);
              // Continue with upload even if delete fails
            }
          }

          // Upload the new institutional image
          const uploadResult = await uploadWebsiteMedia({
            websiteId: website.id,
            mediaFile: formData.institutionalImageToUpload,
            mediaType: "institutional",
          });

          if (!uploadResult.success) {
            throw new Error(uploadResult.error || "Failed to upload institutional image");
          }
        } catch (institutionalError) {
          console.error("Error uploading institutional image:", institutionalError);
          Alert.alert(
            "Aviso",
            `O website foi atualizado, mas houve um erro ao fazer upload da imagem institucional: ${institutionalError instanceof Error ? institutionalError.message : String(institutionalError)}`,
          );
        } finally {
          setUploadingInstitutional(false);
        }
      }

      // Reload the website to get the updated data with theme and images
      await loadWebsite();

      // Reset image upload state
      setFormData((prev) => ({
        ...prev,
        logoImageToUpload: null,
        heroImageToUpload: null,
        institutionalImageToUpload: null,
      }));

      Alert.alert("Sucesso", "Website atualizado com sucesso");
    } catch (error) {
      console.error("Error updating website:", error);
      Alert.alert("Erro", "Falha ao atualizar o website");
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteWebsite = () => {
    if (!website) return;
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!website) return;

    setDeleting(true);
    try {
      // Delete the theme first if it exists (to avoid foreign key constraints)
      if (website.theme) {
        const { error: themeError } = await supabase.from("website_themes").delete().eq("id", website.theme.id);

        if (themeError) throw themeError;
      }

      // Then delete the website
      await deleteWebsite(website.id);
      setShowDeleteModal(false);
      Alert.alert("Sucesso", "Website deletado com sucesso");
      router.replace("/websites");
    } catch (error) {
      console.error("Error deleting website:", error);
      Alert.alert("Erro", "Falha ao deletar o website");
    } finally {
      setDeleting(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean): void => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handlePickImage = async (type: "logo" | "hero" | "institutional") => {
    try {
      if (!website) return;

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: "images",
        allowsEditing: true,
        quality: 0.8,
        aspect: type === "logo" ? [1, 1] : type === "hero" ? [16, 9] : [4, 3], // Square for logo, 16:9 for hero, 4:3 for institutional
      });

      if (result.canceled) {
        console.log("Image selection canceled");
        return;
      }

      const selectedAsset = result.assets[0];

      // Check file size (max 5MB)
      if (selectedAsset.fileSize && selectedAsset.fileSize > 5 * 1024 * 1024) {
        Alert.alert("Erro", "A imagem selecionada deve ter menos de 5MB");
        return;
      }

      // Store the selected image in formData to be uploaded when saving
      setFormData((prev) => ({
        ...prev,
        [type === "logo" ? "logoImageToUpload" : type === "hero" ? "heroImageToUpload" : "institutionalImageToUpload"]:
          {
            uri: selectedAsset.uri,
            fileName: selectedAsset.fileName || `${type}_image_${Date.now()}.jpg`,
            type: "image",
          },
      }));

      Alert.alert(
        "Imagem selecionada",
        `A ${type === "logo" ? "logo" : type === "hero" ? "imagem de capa" : "imagem institucional"} será atualizada quando você salvar as alterações.`,
      );
    } catch (error) {
      console.error(`Error selecting ${type} image:`, error);
      Alert.alert("Erro", `Falha ao selecionar a imagem: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  const handleThemeChange = (field: string, value: string): void => {
    setFormData((prev) => {
      const currentTheme = prev.theme || {
        primary_color: "#3B82F6",
        secondary_color: "#10B981",
        font_family: "Inter",
      };

      return {
        ...prev,
        theme: {
          ...currentTheme,
          [field]: value,
        },
      };
    });
  };

  if (loading) {
    return (
      <Box className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" />
      </Box>
    );
  }

  return (
    <PageLayout
      pageTitle={website?.title || "Gerenciar website"}
      description="Gerencie e personalize seu site"
      pageIcon={WebsiteIcon}
      actions={[
        <Button
          size="sm"
          key="delete-website"
          variant="destructive-link"
          onPress={handleDeleteWebsite}
          className="mr-2"
        >
          <TrashIcon className="mr-2 w-[16px] stroke-2 text-text-inverse" />
          <Text>Deletar</Text>
        </Button>,
        <Button size="sm" key="save-website" onPress={handleUpdateWebsite} isLoading={saving}>
          <SaveIcon className="mr-2 w-[16px] text-text-inverse" />
          <Text>Salvar</Text>
        </Button>,
      ]}
    >
      {/* Delete Confirmation Modal */}
      <Modal isOpen={showDeleteModal} onClose={() => setShowDeleteModal(false)}>
        <Box className="p-6">
          <Heading size="lg" className="mb-4">
            Confirmar Exclusão
          </Heading>
          <Text className="mb-6">Tem certeza que deseja excluir o website "{website?.title}"?</Text>
          <Text className="text-sm text-text-secondary">
            O site será excluído imediatamente e de forma definitiva. Está ação NÃO pode ser desfeita.
          </Text>

          <Box className="mt-6 flex-row justify-end">
            <Button variant="outline" onPress={() => setShowDeleteModal(false)} className="mr-2">
              <Text>Cancelar</Text>
            </Button>
            <Button variant="destructive-link" onPress={confirmDelete} isLoading={deleting}>
              <Text>Confirmar e deletar</Text>
            </Button>
          </Box>
        </Box>
      </Modal>

      {/* Add Domain Modal */}
      <Modal isOpen={showDomainModal} onClose={() => setShowDomainModal(false)}>
        <Box className="p-6">
          <DomainForm website={website} onClose={() => setShowDomainModal(false)} />
        </Box>
      </Modal>
      <ScrollView>
        <PageRow className="flex-row gap-12 pb-8">
          <Box className="flex-1 gap-4">
            <Box className="gap-3">
              <Box>
                <Text className="mb-1 font-medium">Título</Text>
                <Input
                  placeholder="Título do website"
                  value={formData.title}
                  onChangeText={(value: string) => handleInputChange("title", value)}
                />
              </Box>

              <Box>
                <Text className="mb-1 font-medium">Descrição</Text>
                <Textarea
                  numberOfLines={4}
                  placeholder="Descrição do website"
                  value={formData.description}
                  onChangeText={(value: string) => handleInputChange("description", value)}
                />
              </Box>

              <Box className="mt-2">
                <Box className="flex-row items-center">
                  <Switch
                    checked={formData.published}
                    onCheckedChange={(checked) => handleInputChange("published", checked)}
                    size="md"
                  />
                  <Text
                    className={cx(
                      "ml-2 font-medium text-sm",
                      formData.published ? "text-primary" : "text-text-secondary",
                    )}
                  >
                    {formData.published ? "Publicado" : "Rascunho"}
                  </Text>
                </Box>
                <Text className="mt-2 text-gray-500 text-xs">
                  {formData.published
                    ? "Seu website está online e acessível aos visitantes"
                    : "Seu website está modo rascunho e não pode ser acessado"}
                </Text>
              </Box>
            </Box>

            <Box className="gap-3">
              <Text className="font-semibold text-lg">Imagens do Website</Text>

              {/* Logo Image Upload */}
              <Box className="gap-2">
                <Text className="font-medium">Logo</Text>
                <Box className="flex-row items-center gap-4">
                  <Box className="h-24 w-24 rounded-lg border border-border overflow-hidden">
                    {formData.logoImageToUpload ? (
                      <Image
                        source={{ uri: formData.logoImageToUpload.uri }}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="cover"
                      />
                    ) : website && website.logo_image_url ? (
                      <Image
                        source={{ uri: website.logo_image_url }}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="cover"
                      />
                    ) : (
                      <Center className="h-full w-full bg-background-darker">
                        <Text className="text-text-tertiary text-xs text-center">Sem logo</Text>
                      </Center>
                    )}
                  </Box>
                  <Button onPress={() => handlePickImage("logo")} isLoading={uploadingLogo} size="sm" variant="outline">
                    <Text>
                      {formData.logoImageToUpload
                        ? "Alterar seleção"
                        : website && website.logo_image_url
                          ? "Alterar logo"
                          : "Adicionar logo"}
                    </Text>
                  </Button>
                </Box>
                <Text className="text-xs text-text-tertiary">Recomendado: Imagem quadrada de pelo menos 200x200px</Text>
              </Box>

              {/* Hero Image Upload */}
              <Box className="gap-2 mt-4">
                <Text className="font-medium">Imagem de Capa</Text>
                <Box className="flex-row items-center gap-4">
                  <Box className="h-24 w-40 rounded-lg border border-border overflow-hidden">
                    {formData.heroImageToUpload ? (
                      <Image
                        source={{ uri: formData.heroImageToUpload.uri }}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="cover"
                      />
                    ) : website && website.hero_image_url ? (
                      <Image
                        source={{ uri: website.hero_image_url }}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="cover"
                      />
                    ) : (
                      <Center className="h-full w-full bg-background-darker">
                        <Text className="text-text-tertiary text-xs text-center">Sem imagem de capa</Text>
                      </Center>
                    )}
                  </Box>
                  <Button onPress={() => handlePickImage("hero")} isLoading={uploadingHero} size="sm" variant="outline">
                    <Text>
                      {formData.heroImageToUpload
                        ? "Alterar seleção"
                        : website && website.hero_image_url
                          ? "Alterar imagem"
                          : "Adicionar imagem"}
                    </Text>
                  </Button>
                </Box>
                <Text className="text-xs text-text-tertiary">
                  Recomendado: Imagem no formato 16:9 de pelo menos 1200x675px
                </Text>
              </Box>

              {/* Institutional Image Upload */}
              <Box className="gap-2 mt-4">
                <Text className="font-medium">Imagem Institucional</Text>
                <Box className="flex-row items-center gap-4">
                  <Box className="h-24 w-32 rounded-lg border border-border overflow-hidden">
                    {formData.institutionalImageToUpload ? (
                      <Image
                        source={{ uri: formData.institutionalImageToUpload.uri }}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="cover"
                      />
                    ) : website && website.institutional_image_url ? (
                      <Image
                        source={{ uri: website.institutional_image_url }}
                        style={{ width: "100%", height: "100%" }}
                        resizeMode="cover"
                      />
                    ) : (
                      <Center className="h-full w-full bg-background-darker">
                        <Text className="text-text-tertiary text-xs text-center">Sem imagem institucional</Text>
                      </Center>
                    )}
                  </Box>
                  <Button
                    onPress={() => handlePickImage("institutional")}
                    isLoading={uploadingInstitutional}
                    size="sm"
                    variant="outline"
                  >
                    <Text>
                      {formData.institutionalImageToUpload
                        ? "Alterar seleção"
                        : website && website.institutional_image_url
                          ? "Alterar imagem"
                          : "Adicionar imagem"}
                    </Text>
                  </Button>
                </Box>
                <Text className="text-xs text-text-tertiary">
                  Recomendado: Imagem no formato 4:3 de pelo menos 800x600px para a página "Sobre nós"
                </Text>
              </Box>
            </Box>

            <Box className="mt-8 gap-4">
              <Text className="font-semibold text-lg">Configurações do Tema</Text>

              <Box className="gap-3">
                <Box>
                  <ColorPicker
                    label="Cor Primária"
                    placeholder="#3B82F6"
                    value={formData.theme?.primary_color || "#3B82F6"}
                    onChange={(color: string) => handleThemeChange("primary_color", color)}
                  />
                </Box>

                <Box>
                  <ColorPicker
                    label="Cor Secundária"
                    placeholder="#10B981"
                    value={formData.theme?.secondary_color || "#10B981"}
                    onChange={(color: string) => handleThemeChange("secondary_color", color)}
                  />
                </Box>

                <Box>
                  <Text className="mb-1 font-medium">Fonte</Text>
                  <Input
                    placeholder="Fonte"
                    value={formData.theme?.font_family || ""}
                    onChangeText={(value: string) => handleThemeChange("font_family", value)}
                  />
                  <Text className="mt-1 text-gray-500 text-xs">Exemplo: Inter, Roboto, Open Sans</Text>
                </Box>
              </Box>
            </Box>
          </Box>

          <Box className="w-1/3 ">
            <Box className="flex-col rounded-lg border border-border-lighter bg-background-darker p-2">
              <Heading className="mb-2 px-2 py-2 text-text-tertiary">Status do site</Heading>

              <Box className="gap-4 rounded-lg border border-border bg-background p-6 shadow">
                <Box>
                  <Heading>Subdomínio</Heading>
                  <Link href={getSubdomainUrl(formData.subdomain) as ExternalPathString} asChild target="_blank">
                    <Button className="h-6 w-fit p-0 py-0" variant="link">
                      <Text>{formData.subdomain}.imoblr.com.br</Text>
                      <ExternalLinkIcon className="ml-1 h-4 w-4" />
                    </Button>
                  </Link>
                </Box>
                <Box>
                  <Box className="flex-row items-center">
                    <Text className="font-medium">Domínios</Text>
                    <Button
                      variant="outline"
                      size="2xs"
                      onPress={() => setShowDomainModal(true)}
                      className="ml-2 w-fit"
                    >
                      <PlusIcon className="h-3 w-3" />
                    </Button>
                  </Box>
                  {customDomainsData && customDomainsData.length > 0 ? (
                    <Box className="space-y-2">
                      {customDomainsData.map((domain) => (
                        <Box key={domain.id}>
                          <Link
                            href={getCustomDomainUrl(domain.domain_name) as ExternalPathString}
                            asChild
                            target="_blank"
                          >
                            <Button className="h-6 w-fit p-0 py-0" variant="link">
                              <Text>{domain.domain_name}</Text>
                              <ExternalLinkIcon className="ml-1 h-4 w-4" />
                            </Button>
                          </Link>
                          <Box className="flex-row items-center">
                            <Box className="flex-row items-start gap-1">
                              <Button size="xs" className="h-5 px-1" variant="destructive-outline">
                                <TrashIcon className="h-3 w-3 text-error-800" />
                              </Button>
                              <Button size="xs" className="h-5 px-1" variant="outline">
                                <SettingsIcon className="h-3 w-3 text-text" />
                              </Button>
                            </Box>
                            <DomainVerificationBadge domainId={domain.id} initialStatus={domain.verification_status} />
                          </Box>
                        </Box>
                      ))}
                    </Box>
                  ) : (
                    <Text className="text-text-secondary">Nenhum domínio cadastrado</Text>
                  )}
                </Box>
                <Box>
                  <Heading className="mb-1">Status</Heading>
                  <Badge
                    className="w-fit"
                    variant={formData.published ? "primary-outline" : "outline"}
                    size="sm"
                    rounded="full"
                    beforeIcon={formData.published ? LiveIcon : PauseIcon}
                  >
                    <Text>{formData.published ? "Publicado" : "Rascunho"}</Text>
                  </Badge>
                </Box>
              </Box>
            </Box>
          </Box>
        </PageRow>
      </ScrollView>
    </PageLayout>
  );
}

interface CloudflareVerificationData {
  id: string;
  hostname: string;
  ssl?: {
    status: string;
    method: string;
    type: string;
    validation_records?: Array<{
      txt_name?: string;
      txt_value?: string;
      http_url?: string;
      http_body?: string;
    }>;
  };
  status: string;
  verification_errors?: string[];
  ownership_verification?: {
    name: string;
    type: string;
    value: string;
  };
  ownership_verification_http?: {
    http_url: string;
    http_body: string;
  };
}

interface DomainResponseData {
  success: boolean;
  hostname: string;
  cloudflare_data: CloudflareVerificationData;
  error?: string;
  details?: unknown;
}

interface DomainFormProps {
  website: Website | null;
  onClose: () => void;
}

function DomainForm({ website, onClose }: DomainFormProps) {
  const [domainName, setDomainName] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isValid, setIsValid] = useState(false);
  const [step, setStep] = useState<"add" | "verify">("add");
  const [domainData, setDomainData] = useState<DomainResponseData | null>(null);
  const { axios } = useAxios();

  // Validate domain on change
  const handleDomainChange = (text: string) => {
    setDomainName(text);
    // Domain validation regex
    // This will accept domains like example.com, sub.example.com, and imoblrteste.com.br
    const domainRegex =
      /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
    const isValid = domainRegex.test(text);

    // Log validation result
    console.log("Domain validation:", { text, isValid });
    setIsValid(isValid);
    if (error && isValid) setError(null);
  };

  // Submit domain to API
  const handleSubmit = async () => {
    if (!isValid || !website) {
      console.log("Form validation failed:", { isValid, website });
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Make sure we have all required parameters
      if (!website.id) {
        throw new Error("ID do website não encontrado");
      }
      if (!website.team_account_id) {
        throw new Error("ID da conta não encontrado");
      }

      console.log("Submitting domain:", {
        hostname: domainName,
        websiteId: website.id,
        teamAccountId: website.team_account_id,
      });

      const response = await axios.post("/domains", {
        hostname: domainName,
        websiteId: website.id,
        teamAccountId: website.team_account_id,
      });

      console.log("Domain API response:", response.data);

      if (response.data.success) {
        // Store the domain data and move to verification step
        setDomainData(response.data);
        setStep("verify");
        Alert.alert("Sucesso", "Domínio adicionado com sucesso. Siga as instruções para verificação.");
      } else {
        setError(response.data.error || "Erro ao adicionar domínio");
        Alert.alert("Erro", response.data.error || "Erro ao adicionar domínio");
      }
    } catch (err) {
      console.error("Error adding domain:", err);
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Erro ao adicionar domínio. Verifique se o domínio é válido e tente novamente.";

      setError(errorMessage);
      Alert.alert("Erro", errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle verification check
  const handleVerifyDomain = () => {
    // In a real implementation, this would check the verification status
    // For now, we'll just close the modal
    onClose();
  };

  // Handle "configure later" action
  const handleConfigureLater = () => {
    onClose();
  };

  // Generate verification instructions based on Cloudflare data
  const getVerificationInstructions = () => {
    if (!domainData?.cloudflare_data) return null;

    const result = domainData.cloudflare_data;

    // Check for TXT verification
    if (result.ownership_verification && result.ownership_verification.type === "txt") {
      return {
        type: "txt",
        name: result.ownership_verification.name,
        value: result.ownership_verification.value,
        instructions: `Adicione um registro TXT com nome ${result.ownership_verification.name} e valor ${result.ownership_verification.value} nas configurações de DNS.`,
      };
    }

    // Check for HTTP verification
    if (result.ownership_verification_http) {
      return {
        type: "http",
        url: result.ownership_verification_http.http_url,
        content: result.ownership_verification_http.http_body,
        instructions: `Crie um arquivo em ${result.ownership_verification_http.http_url} com o conteúdo: ${result.ownership_verification_http.http_body}`,
      };
    }

    // Check SSL validation records if available
    if (result.ssl?.validation_records && result.ssl.validation_records.length > 0) {
      const record = result.ssl.validation_records[0];

      if (record.txt_name && record.txt_value) {
        return {
          type: "txt",
          name: record.txt_name,
          value: record.txt_value,
          instructions: `Adicione um registro TXT com nome ${record.txt_name} e valor ${record.txt_value} nas configurações de DNS.`,
        };
      }

      if (record.http_url && record.http_body) {
        return {
          type: "http",
          url: record.http_url,
          content: record.http_body,
          instructions: `Crie um arquivo em ${record.http_url} com o conteúdo: ${record.http_body}`,
        };
      }
    }

    return null;
  };

  const verificationInfo = getVerificationInstructions();

  // Render the appropriate step
  if (step === "verify") {
    return (
      <Box className="space-y-4">
        <ModalHeader title="Configure seu domínio" onClose={onClose} icon={WebsiteIcon} />

        <Box className="gap-4">
          {verificationInfo ? (
            <>
              <Box className="rounded-md border border-border p-4">
                <Box className="mb-4 flex-row items-center gap-2">
                  <Center className="h-6 w-6 rounded-full bg-primary-100">
                    <Text className="text-primary-600">1</Text>
                  </Center>
                  <Text className="font-medium">Verifique a propriedade do domínio</Text>
                </Box>

                {verificationInfo.type === "txt" ? (
                  <Box className="space-y-3">
                    <Text className="text-sm">
                      Adicione um registro <Text className="font-medium">tipo TXT</Text> no DNS do seu domínio com as
                      seguintes informações:
                    </Text>

                    <Box className="space-y-2">
                      <Box className="flex-row justify-between">
                        <Text className="font-medium text-sm">Nome:</Text>
                        <Text className="text-sm">{verificationInfo.name}</Text>
                      </Box>

                      <Box className="flex-row justify-between">
                        <Text className="font-medium text-sm">Valor:</Text>
                        <Text className="text-sm">{verificationInfo.value}</Text>
                      </Box>
                    </Box>
                  </Box>
                ) : verificationInfo.type === "http" ? (
                  <Box className="space-y-3">
                    <Text className="text-sm">Crie um arquivo no seguinte caminho:</Text>

                    <Box className="space-y-2">
                      <Box className="flex-row justify-between">
                        <Text className="font-medium text-sm">URL:</Text>
                        <Text className="text-sm">{verificationInfo.url}</Text>
                      </Box>

                      <Box className="flex-row justify-between">
                        <Text className="font-medium text-sm">Conteúdo:</Text>
                        <Text className="text-sm">{verificationInfo.content}</Text>
                      </Box>
                    </Box>
                  </Box>
                ) : (
                  <Text className="text-sm">{verificationInfo.instructions}</Text>
                )}
              </Box>
              <Box className="rounded-md border border-border p-4">
                <Box className="mb-4 flex-row items-center gap-2">
                  <Center className="h-6 w-6 rounded-full bg-primary-100">
                    <Text className="text-primary-600">2</Text>
                  </Center>
                  <Text className="font-medium">Aponte o domínio para a Imoblr</Text>
                </Box>

                <Box className="space-y-3">
                  <Text className="text-sm">
                    Adicione os seguintes registros <Text className="font-medium">tipo CNAME</Text> no DNS do seu
                    domínio com as seguintes informações:
                  </Text>

                  <Box className="space-y-2">
                    <Box className="flex-row justify-between">
                      <Text className="font-medium text-sm">Nome:</Text>
                      <Text className="text-sm">@ (ou deixar em branco)</Text>
                      <Text className="font-medium text-sm">Valor:</Text>
                      <Text className="text-sm">sites.imoblr.com.br</Text>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </>
          ) : (
            <Box className="rounded-md bg-destructive-50 p-3">
              <Text className="text-destructive-600 text-sm">
                Não foi possível obter instruções de verificação. Entre em contato com o suporte.
              </Text>
            </Box>
          )}

          {/* <Box className="rounded-md border border-primary-100 bg-primary-50 p-4 shadow-xs">
            <Text className="text-sm text-text-secondary">
              <Text className="font-medium text-sm">A verificação pode levar até 24 horas para ser concluída.</Text>Você
              pode verificar o status da verificação a qualquer momento na página de detalhes do website.
            </Text>
          </Box> */}
        </Box>

        <ModalFooter
          primaryAction={{
            iconBefore: RefreshIcon,
            label: "Checar status",
            onPress: handleVerifyDomain,
            variant: "outline",
          }}
          secondaryAction={{
            label: "Vou configurar depois",
            onPress: handleConfigureLater,
            variant: "destructive-link",
          }}
        />
      </Box>
    );
  }

  // Default step: Add domain
  return (
    <Box className="space-y-4">
      <ModalHeader title="Adicionar domínio" onClose={onClose} icon={WebsiteIcon} />
      <Box>
        <Text className="mb-1 font-medium">Domínio</Text>
        <Input
          placeholder="Exemplo: minha-imobiliaria.com.br"
          value={domainName}
          onChangeText={handleDomainChange}
          autoCapitalize="none"
          autoCorrect={false}
          autoFocus
          editable={!isSubmitting}
        />
        <Text className={`mt-2 text-xs ${isValid ? "text-success" : "text-text-secondary"}`}>
          {isValid
            ? "Domínio válido! Clique em 'Adicionar domínio' para continuar."
            : "Digite um domínio válido que você possui (ex: minha-imobiliaria.com.br)"}
        </Text>
      </Box>

      {error && (
        <Box className="rounded-md bg-destructive-50 p-3">
          <Text className="text-destructive-600 text-sm">{error}</Text>
        </Box>
      )}

      <Box className="mt-4 rounded-md border border-primary-100 bg-primary-50 p-4 shadow-xs">
        <Text className="text-sm text-text-secondary">
          Após adicionar seu domínio, você precisará configurar os registros DNS do seu provedor de domínio. Instruções
          detalhadas serão fornecidas após a adição do domínio.
        </Text>
      </Box>

      <ModalFooter
        primaryAction={{
          label: isSubmitting ? "Adicionando..." : "Adicionar domínio",
          onPress: handleSubmit,
          disabled: isSubmitting || !isValid,
        }}
        secondaryAction={{
          label: "Cancelar",
          onPress: onClose,
          disabled: isSubmitting,
        }}
      />
    </Box>
  );
}
