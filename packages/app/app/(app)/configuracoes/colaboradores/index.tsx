import React, { useState } from "react";
import { ActivityIndicator } from "react-native";
import {
  Box,
  Text,
} from "@/components/ui";
import { PageRow } from "@/components/PageLayout";
import { SettingGroup } from "@/components/SettingGroup";
import { useCurrentTeamId } from "@/hooks/useCurrentTeamId";
import { useCurrentUserAccountRole } from "@/hooks/useCurrentUserAccountRole";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { InviteTeamMember } from "./InviteTeamMember";
import { PendingInvitesList } from "./PendingInvitesList";
import { ActiveMembersList } from "./ActiveMembersList";

export default function TeamSettingsScreen() {
  const [currentTeamId] = useCurrentTeamId();
  const teamId = currentTeamId;

  const { session } = useSupabaseAuth();

  const { data: currentUserRoleData, isLoading: isLoadingRole, error: errorRole } = useCurrentUserAccountRole(teamId);

  const isOwner = currentUserRoleData?.team_role === "owner";
  const currentUserId = session?.user?.id;

  const isLoading = isLoadingRole;
  const error = errorRole;

  const handleInviteCreated = (token: string) => {
    // Token handling is now managed within PendingInvitesList component
    console.log("Invite created with token:", token);
  };

  if (isLoading) {
    return (
      <PageRow className="items-center justify-center py-8">
        <ActivityIndicator size="large" />
        <Text className="mt-2 text-foreground">Carregando dados da equipe...</Text>
      </PageRow>
    );
  }

  if (error) {
    return (
      <PageRow className="py-8">
        <Text className="text-destructive">Erro ao carregar dados da equipe: {error.message}</Text>
      </PageRow>
    );
  }

  // Removed separate InviteModal component function

  return (
    <PageRow className="flex-col">
      {isOwner && (
        <SettingGroup
          title="Adicionar novo colaborador"
          description="Crie e gerencie convites para novos colaboradores em sua organização"
        >
          <Box className="">
            {teamId && (
              <>
                <InviteTeamMember teamId={teamId} onInviteCreated={handleInviteCreated} />
                <PendingInvitesList teamId={teamId} />
              </>
            )}
          </Box>
        </SettingGroup>
      )}

      <SettingGroup
        title="Colaboradores ativos"
        description="Visualize, remova e altere permissões de colaboradores que já fazem parte de sua organização"
      >
        {teamId && <ActiveMembersList teamId={teamId} />}
      </SettingGroup>


    </PageRow>
  );
}
