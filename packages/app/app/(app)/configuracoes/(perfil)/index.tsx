import React from "react";
import { <PERSON>, Button, Heading, Input, Text, Textarea, FormField, FormInput, Form } from "@/components/ui";
import { useLocalSearchParams } from "expo-router";
import { PageRow } from "@/components/PageLayout";
import { useCurrentTeamId } from "@/hooks/useCurrentTeamId";
import { useTeamProfile } from "@/hooks/useTeamProfile";
import { useUpdateTeamProfile } from "@/hooks/mutations/useUpdateTeamProfile";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { ActivityIndicator } from "react-native";

// Form validation schema
const profileSchema = z.object({
  name: z.string().optional(),
  address: z.string().optional(),
  phone_number: z.string().optional(),
  whatsapp_number: z.string().optional(),
  instagram_url: z.string().optional(),
  facebook_url: z.string().optional(),
  youtube_url: z.string().optional(),
  about_us: z.string().optional(),
});

type ProfileFormValues = z.infer<typeof profileSchema>;

export default function TeamProfileScreen() {
  const [teamId] = useCurrentTeamId();
  const { data: profile, isLoading, error } = useTeamProfile(teamId);
  const updateTeamProfile = useUpdateTeamProfile(teamId);

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: profile?.name || "",
      address: profile?.address || "",
      phone_number: profile?.phone_number || "",
      whatsapp_number: profile?.whatsapp_number || "",
      instagram_url: profile?.instagram_url || "",
      facebook_url: profile?.facebook_url || "",
      youtube_url: profile?.youtube_url || "",
      about_us: profile?.about_us || "",
    },
  });

  // Update form values when profile data is loaded
  React.useEffect(() => {
    if (profile) {
      form.reset({
        name: profile.name || "",
        address: profile.address || "",
        phone_number: profile.phone_number || "",
        whatsapp_number: profile.whatsapp_number || "",
        instagram_url: profile.instagram_url || "",
        facebook_url: profile.facebook_url || "",
        youtube_url: profile.youtube_url || "",
        about_us: profile.about_us || "",
      });
    }
  }, [profile, form]);

  const onSubmit = form.handleSubmit(async (data) => {
    try {
      await updateTeamProfile.mutateAsync(data);
    } catch (error) {
      console.error("Failed to update team profile:", error);
    }
  });

  if (isLoading) {
    return (
      <Box className="flex-1 bg-background p-4">
        <Text className="mb-4 font-bold text-2xl text-foreground">Carregando...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Box className="flex-1 bg-background p-4">
        <Text className="text-destructive">Erro ao carregar perfil: {error.message}</Text>
      </Box>
    );
  }

  return (
    <PageRow className="py-8">
      <Heading size="lg">
        Perfil público
      </Heading>
      <Text className="mb-8 text-text-secondary text-sm w-1/3 mt-2">
        Estas são as configurações do perfil público do seu negócio. Todas as informações aqui
        configuradas são visiveis para todos os usuários em seu site, integrações e canais.
      </Text>

      <Box className="w-full max-w-2xl">
        <Form {...form}>
          <Box className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormInput
                  label="Nome da Empresa"
                  placeholder="Nome da sua empresa"
                  {...field}
                />
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormInput
                  label="Endereço"
                  placeholder="Endereço completo"
                  {...field}
                />
              )}
            />

            <FormField
              control={form.control}
              name="phone_number"
              render={({ field }) => (
                <FormInput
                  label="Telefone"
                  placeholder="(00) 0000-0000"
                  {...field}
                />
              )}
            />

            <FormField
              control={form.control}
              name="whatsapp_number"
              render={({ field }) => (
                <FormInput
                  label="WhatsApp"
                  placeholder="(00) 00000-0000"
                  {...field}
                />
              )}
            />

            <FormField
              control={form.control}
              name="instagram_url"
              render={({ field }) => (
                <FormInput
                  label="Instagram"
                  placeholder="https://instagram.com/seuusuario"
                  {...field}
                />
              )}
            />

            <FormField
              control={form.control}
              name="facebook_url"
              render={({ field }) => (
                <FormInput
                  label="Facebook"
                  placeholder="https://facebook.com/suapagina"
                  {...field}
                />
              )}
            />

            <FormField
              control={form.control}
              name="youtube_url"
              render={({ field }) => (
                <FormInput
                  label="YouTube"
                  placeholder="https://youtube.com/seucanal"
                  {...field}
                />
              )}
            />

            <FormField
              control={form.control}
              name="about_us"
              render={({ field }) => (
                <Box className="space-y-1">
                  <Text className="font-medium">Sobre Nós</Text>
                  <Textarea
                    numberOfLines={6}
                    placeholder="Descreva sua empresa, história, valores e diferenciais..."
                    {...field}
                  />
                  <Text className="text-xs text-text-tertiary">
                    Este texto será exibido na página "Sobre Nós" do seu site institucional.
                  </Text>
                </Box>
              )}
            />

            <Button
              onPress={onSubmit}
              disabled={updateTeamProfile.isPending}
              className="mt-4"
            >
              {updateTeamProfile.isPending ? (
                <>
                  <ActivityIndicator size="small" color="white" />
                  <Text className="ml-2">Salvando...</Text>
                </>
              ) : (
                <Text>Salvar Perfil</Text>
              )}
            </Button>
          </Box>
        </Form>
      </Box>
    </PageRow>
  );
}
