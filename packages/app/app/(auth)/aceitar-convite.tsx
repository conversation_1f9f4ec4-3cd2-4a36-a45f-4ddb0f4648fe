import * as React from "react";
import { ActivityIndicator, Platform } from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { Button } from "@/components/ui/button";
import { Text } from "@/components/ui/text";
import { Box, Center, Heading } from "@/components/ui";
import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";
import { useSupabase } from "@/hooks/useSupabase";
import { useAcceptInvitation } from "@/hooks/mutations/useAcceptInvitation";
import Reanimated, { FadeIn } from "react-native-reanimated";
import UsersIcon from "@/assets/icons/user-multiple-02.svg";
import { Link } from "expo-router";

export default function AcceptInviteScreen() {
  const { token } = useLocalSearchParams<{ token: string }>();
  const router = useRouter();
  const { session, loading: authLoading } = useSupabaseAuth();
  const supabaseClient = useSupabase();
  // Don't use the lookup hook for unauthenticated users
  const [invitationData, setInvitationData] = React.useState<{
    active: boolean;
    account_name: string;
    team_id?: string;
    account_role?: string;
  } | null>(null);
  const [isLoadingInvitation, setIsLoadingInvitation] = React.useState(true);
  const [invitationError, setInvitationError] = React.useState<Error | null>(null);
  const acceptInvitationMutation = useAcceptInvitation();

  // State to track if we're redirecting to login
  const [isRedirecting, setIsRedirecting] = React.useState(false);

  // Initial setup and token check
  React.useEffect(() => {
    // Handle missing token case
    if (!token) {
      setIsLoadingInvitation(false);
      setInvitationError(new Error("Token não fornecido"));
      return;
    }

    if (!supabaseClient) return;
    
    // Important flag to prevent state updates after unmount
    let isMounted = true;
    
    console.log(`Invitation check for token: ${token}`);
    console.log(`User auth status: ${session ? 'logged in' : 'not logged in'}`);
    
    // Set loading state
    setIsLoadingInvitation(true);
    
    // Check invitation
    const checkInvitation = async () => {
      try {        
        if (session) {
          // For authenticated users
          console.log("Checking invitation with RPC");
          const { data, error } = await supabaseClient.rpc("lookup_invitation", {
            lookup_invitation_token: token
          });
          
          if (error) {
            console.error("RPC error:", error);
            throw new Error(error.message);
          }
          
          console.log("Invitation data:", data);
          
          if (isMounted) {
            if (data) {
              setInvitationData({
                active: true,
                account_name: data.account_name || "Time",
                team_id: data.team_id,
                account_role: data.account_role
              });
            } else {
              setInvitationData({ active: false, account_name: "" });
            }
          }
        } else {
          // For unauthenticated users
          console.log("Showing generic invite message for unauthenticated user");
          if (isMounted) {
            setInvitationData({
              active: true,
              account_name: "Uma equipe"
            });
          }
        }
      } catch (err) {
        console.error("Error checking invitation:", err);
        if (isMounted) {
          setInvitationError(err instanceof Error ? err : new Error(String(err)));
        }
      } finally {
        if (isMounted) {
          setIsLoadingInvitation(false);
        }
      }
    };
    
    checkInvitation();
    
    return () => {
      isMounted = false;
    };
  }, [token, session, supabaseClient]);

  // Handle accepting invitation
  const handleAcceptInvitation = async () => {
    const tokenToUse = token || (Platform.OS === "web" ? localStorage.getItem("pendingInviteToken") : null);
    
    if (!tokenToUse) {
      console.log("No token available for invitation acceptance");
      alert("Não foi possível encontrar o token do convite.");
      return;
    }

    console.log("Attempting to accept invitation with token:", tokenToUse);
    console.log("Authenticated as:", session?.user?.email);
    
    try {
      // Show modal or loading state if needed
      const result = await acceptInvitationMutation.mutateAsync({ token: tokenToUse });
      console.log("Invitation accepted successfully:", result);
      
      // Clear stored token if we used it
      if (Platform.OS === "web") {
        localStorage.removeItem("pendingInviteToken");
      }
      
      // Show success message
      alert("Convite aceito com sucesso!");
      
      // Redirect to dashboard after successful acceptance
      console.log("Redirecting to dashboard...");
      setTimeout(() => {
        router.replace("/");
      }, 1000);
    } catch (error) {
      console.error("Error accepting invitation:", error);
      alert("Erro ao aceitar convite. Este convite pode estar expirado (limite de 24 horas) ou ser inválido.");
    }
  };

  // Handle sign in and then accept flow
  const handleSignInAndAccept = () => {
    setIsRedirecting(true);
    // We'll redirect to login page and store the invitation token
    // in localStorage/AsyncStorage to retrieve after login
    if (Platform.OS === "web") {
      // For web, use localStorage
      localStorage.setItem("pendingInviteToken", token || "");
    } else {
      // For native, we'll pass the token as a parameter
      // This is a simplification - in a real app you might want to use
      // a more robust approach like AsyncStorage
    }
    
    // Redirect to login page
    router.push({
      pathname: "/entrar",
      params: { returnTo: `/aceitar-convite?token=${token}` }
    });
  };

  // For debugging
  console.log("Render state:", {
    isLoadingInvitation,
    authLoading,
    hasError: !!invitationError,
    hasData: !!invitationData,
    active: invitationData?.active,
  });

  // Show loading state while checking invitation and auth
  if (isLoadingInvitation || authLoading) {
    console.log("Showing loading state");
    return (
      <Center className="h-full w-full">
        <ActivityIndicator size="large" color="#0000ff" />
        <Text className="mt-4 text-text-secondary">Verificando convite...</Text>
      </Center>
    );
  }

  // Show error if invitation is invalid or expired
  if (invitationError) {
    console.log("Showing error state:", invitationError.message);
    return (
      <Center className="h-full w-full">
        <Box className="w-full max-w-md rounded-lg border border-border bg-background p-8">
          <Heading size="lg" className="mb-4 text-foreground">Convite inválido</Heading>
          <Text className="mb-6 text-text-secondary">
            {invitationError.message}
          </Text>
          <Link href="/" asChild>
            <Button className="w-full">
              <Text>Voltar para página inicial</Text>
            </Button>
          </Link>
        </Box>
      </Center>
    );
  }
  
  // Ensure invitationData exists
  if (!invitationData) {
    console.log("No invitation data");
    return (
      <Center className="h-full w-full">
        <Box className="w-full max-w-md rounded-lg border border-border bg-background p-8">
          <Heading size="lg" className="mb-4 text-foreground">Convite não encontrado</Heading>
          <Text className="mb-6 text-text-secondary">
            Não foi possível verificar os detalhes do convite.
          </Text>
          <Link href="/" asChild>
            <Button className="w-full">
              <Text>Voltar para página inicial</Text>
            </Button>
          </Link>
        </Box>
      </Center>
    );
  }
  
  // Check if invitation is active
  if (!invitationData.active) {
    console.log("Invitation not active");
    return (
      <Center className="h-full w-full">
        <Box className="w-full max-w-md rounded-lg border border-border bg-background p-8">
          <Heading size="lg" className="mb-4 text-foreground">Convite expirado</Heading>
          <Text className="mb-6 text-text-secondary">
            Este convite está expirado ou não é válido.
          </Text>
          <Link href="/" asChild>
            <Button className="w-full">
              <Text>Voltar para página inicial</Text>
            </Button>
          </Link>
        </Box>
      </Center>
    );
  }

  // Success invitation state
  console.log("Showing invitation acceptance screen", { 
    accountName: invitationData.account_name,
    isLoggedIn: !!session
  });
  
  return (
    <Center className="h-full w-full">
      <Box className="w-full max-w-md rounded-lg border border-border bg-background p-8">
        <Center className="mb-6">
          <Box className="mb-4 rounded-full bg-primary-50 p-4">
            <UsersIcon className="h-8 w-8 text-primary" />
          </Box>
          <Heading size="lg" className="mb-2 text-center text-foreground">
            Você foi convidado(a)
          </Heading>
          <Text className="text-center text-text-secondary">
            {invitationData.account_name} convidou você para se juntar à equipe
          </Text>
        </Center>

        {session ? (
          <Button 
            onPress={handleAcceptInvitation} 
            className="w-full" 
            disabled={acceptInvitationMutation.isPending}
          >
            {acceptInvitationMutation.isPending ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text>Aceitar convite</Text>
            )}
          </Button>
        ) : (
          <Button 
            onPress={handleSignInAndAccept} 
            className="w-full"
            disabled={isRedirecting}
          >
            {isRedirecting ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text>Entrar e aceitar convite</Text>
            )}
          </Button>
        )}

        <Text className="mt-4 text-center text-sm text-text-tertiary">
          Ao aceitar, você terá acesso ao painel administrativo desta equipe.
        </Text>
      </Box>
    </Center>
  );
}
