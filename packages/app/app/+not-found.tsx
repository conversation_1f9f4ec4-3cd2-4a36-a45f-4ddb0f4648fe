import { Link, Stack } from "expo-router";
import { Text } from "react-native";

import { Center } from "@/components/ui";

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: "Oops!" }} />
      <Center className="h-full w-full">
        <Text className={styles.title}>This screen doesn't exist.</Text>
        <Link href="/" className={styles.link}>
          <Text className={styles.linkText}>Go to home screen!</Text>
        </Link>
      </Center>
    </>
  );
}

const styles = {
  title: "text-xl font-bold",
  link: "mt-4 pt-4",
  linkText: "text-base text-[#2e78b7]",
};
