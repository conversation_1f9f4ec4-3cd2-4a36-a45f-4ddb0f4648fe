# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# supabase for testing
supabase/.branches
supabase/.temp
supabase/**/*.env

# cloudflare related
workers/*/node_modules/
workers/*/wrangler/
/workers/*/.wrangler
/workers/*/.dev.vars

.env
# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

.expo/
expo-env.d.ts
# @end expo-cli

/dist