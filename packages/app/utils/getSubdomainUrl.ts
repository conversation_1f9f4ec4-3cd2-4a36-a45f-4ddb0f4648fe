/**
 * Generates a URL for a subdomain based on the current environment
 *
 * In production (app.imoblr.com.br), it returns https://[subdomain].imoblr.com.br
 * In preview (*.app.imoblr.com.br), it returns https://[subdomain].[preview-env].imoblr.com.br
 * In development (localhost, 127.0.0.1, etc.), it returns http://localhost:4321/[subdomain]
 *
 * @param subdomain - The subdomain to generate a URL for
 * @returns The complete URL for the subdomain
 */
export const getSubdomainUrl = (subdomain: string): string => {
  // Get the current hostname if we're in a browser environment
  const hostname = typeof window !== 'undefined' ? window.location.hostname : '';
  const port = typeof window !== 'undefined' ? window.location.port : '';

  // Check if we're in production, preview, or development environment
  const isProduction = hostname === 'app.imoblr.com.br';
  const isPreview = hostname.endsWith('.app.imoblr.com.br') && hostname !== 'app.imoblr.com.br';
  const isDevelopment = hostname === 'localhost' || hostname === '127.0.0.1' || hostname.startsWith('192.168.');

  // Generate the URL based on the environment
  if (isProduction) {
    return `https://${subdomain}.imoblr.com.br`;
  } if (isPreview) {
    // Extract the preview environment (the part before .app.imoblr.com.br)
    const previewEnv = hostname.replace('.app.imoblr.com.br', '');
    return `https://${subdomain}.${previewEnv}.imoblr.com.br`;
  } if (isDevelopment) {
    // For development environment, use the astro app port (4321)
    // This will route through our local proxy to the Astro app
    return `http://${hostname}:4321/${subdomain}`;
  }
  // Default to production URL if environment can't be determined
  return `https://${subdomain}.imoblr.com.br`;
};
