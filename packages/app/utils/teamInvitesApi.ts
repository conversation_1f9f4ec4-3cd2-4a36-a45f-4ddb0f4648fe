import type { AxiosInstance } from 'axios';

// Types for team invites API
export interface TeamInvite {
  invitation_id: string;
  email: string;
  team_role: 'owner' | 'member';
  team_name: string;
  created_at: string;
  updated_at: string;
  invited_by_user_id: string;
  expires_at: string;
}

export interface CreateTeamInviteRequest {
  team_id: string;
  email: string;
  team_role?: 'owner' | 'member';
}

export interface CreateTeamInviteResponse {
  success: boolean;
  invitation: {
    id: string;
    team_id: string;
    email: string;
    team_role: string;
    team_name: string;
    token: string;
    created_at: string;
  };
}

export interface ListTeamInvitesResponse {
  success: boolean;
  invitations: TeamInvite[];
  pagination: {
    limit: number;
    offset: number;
    total: number;
    has_more: boolean;
  };
}

export interface AcceptTeamInviteRequest {
  token: string;
}

export interface AcceptTeamInviteResponse {
  success: boolean;
  team_id: string;
  team_role: string;
  team_name: string;
  slug: string;
  message: string;
}

export interface DeleteTeamInviteResponse {
  success: boolean;
  message: string;
  deleted_invitation: {
    id: string;
    email: string;
    team_name: string;
  };
}

export interface LookupTeamInviteResponse {
  active: boolean;
  invitation?: {
    team_name: string;
    team_role: string;
    email: string;
    created_at: string;
    team_slug: string;
    invited_by: {
      name: string;
      email: string;
    } | null;
  };
  error?: string;
}

// API client functions
export class TeamInvitesApi {
  constructor(private axios: AxiosInstance) {}

  /**
   * Create a new team invitation
   */
  async createTeamInvite(data: CreateTeamInviteRequest): Promise<CreateTeamInviteResponse> {
    const response = await this.axios.post<CreateTeamInviteResponse>('/team-invites', data);
    return response.data;
  }

  /**
   * List team invitations for a specific team
   */
  async listTeamInvites(teamId: string, options?: { limit?: number; offset?: number }): Promise<ListTeamInvitesResponse> {
    const params = new URLSearchParams();
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());
    
    const url = `/team-invites/${teamId}${params.toString() ? `?${params.toString()}` : ''}`;
    const response = await this.axios.get<ListTeamInvitesResponse>(url);
    return response.data;
  }

  /**
   * Accept a team invitation
   */
  async acceptTeamInvite(data: AcceptTeamInviteRequest): Promise<AcceptTeamInviteResponse> {
    const response = await this.axios.post<AcceptTeamInviteResponse>('/team-invites/accept', data);
    return response.data;
  }

  /**
   * Delete a team invitation
   */
  async deleteTeamInvite(inviteId: string): Promise<DeleteTeamInviteResponse> {
    const response = await this.axios.delete<DeleteTeamInviteResponse>(`/team-invites/${inviteId}`);
    return response.data;
  }

  /**
   * Lookup team invitation details by token (public endpoint)
   */
  async lookupTeamInvite(token: string): Promise<LookupTeamInviteResponse> {
    const response = await this.axios.get<LookupTeamInviteResponse>(`/team-invites/lookup/${token}`);
    return response.data;
  }
}

// Factory function to create API client instance
export function createTeamInvitesApi(axios: AxiosInstance): TeamInvitesApi {
  return new TeamInvitesApi(axios);
}
