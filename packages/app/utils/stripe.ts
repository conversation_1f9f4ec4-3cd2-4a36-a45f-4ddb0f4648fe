import type { StripeProduct, StripeProductPrice } from "@/types";
import type { StripeProductPriceTier } from "@/types";

/**
 * Retrieves the appropriate Stripe price for a product based on billing interval preference.
 *
 * @param {StripeProduct} product - The Stripe product containing price information
 * @param {boolean} yearly - Whether to retrieve yearly or monthly pricing
 * @returns {StripeProductPrice | undefined} The matching price object or undefined if not found
 */
export const getStripePrice = (product: StripeProduct, yearly: boolean): StripeProductPrice | undefined => {
  if (!product || !product.prices || yearly === undefined) return undefined;
  return product.prices?.find((price) => price.recurring?.interval === (yearly ? "year" : "month"));
};

/**
 * Sorts price tiers by their upper limit in ascending order.
 * Tiers with null upper limits (unlimited) are placed at the end.
 *
 * @param {StripeProductPriceTier[]} tiers - Array of price tiers to sort
 * @returns {StripeProductPriceTier[]} Sorted array of price tiers
 * @private
 */
const sortTiers = (tiers: StripeProductPriceTier[]) => {
  return [...tiers].sort((a, b) => {
    if (a.up_to === null) return 1;
    if (b.up_to === null) return -1;
    return a.up_to - b.up_to;
  });
};

/**
 * Calculates the total price for a tiered pricing structure based on quantity.
 * Handles multiple tiers with different unit prices and upper limits.
 *
 * @param {number} quantity - The quantity to calculate pricing for
 * @param {StripeProductPriceTier[]} tiers - Array of price tiers
 * @returns {number} The total price in currency units (dollars, euros, etc.)
 */
export const getTotalTieredPrice = (quantity: number, tiers: StripeProductPriceTier[]) => {
  if (!tiers || tiers.length === 0) return 0;

  const sortedTiers = sortTiers(tiers);
  let remaining = quantity;
  let total = 0;
  let previousUpTo = 0;

  for (const tier of sortedTiers) {
    if (remaining <= 0) break;

    const tierMax = tier.up_to ?? Number.POSITIVE_INFINITY;
    const tierRange = tierMax - previousUpTo;
    const tierQuantity = Math.min(remaining, tierRange);

    if (tier.unit_amount !== null) {
      total += tierQuantity * tier.unit_amount;
      remaining -= tierQuantity;
    }

    previousUpTo = tierMax;
  }

  return total / 100; // Convert cents to currency
};

/**
 * Determines the unit price for a specific quantity in a tiered pricing structure.
 * Returns the unit price of the tier that the quantity falls into.
 *
 * @param {number} quantity - The quantity to determine unit price for
 * @param {StripeProductPriceTier[]} tiers - Array of price tiers
 * @returns {number} The unit price in currency units (dollars, euros, etc.)
 */
export const getUnitTieredPrice = (quantity: number, tiers: StripeProductPriceTier[]) => {
  if (!tiers || tiers.length === 0) return 0;

  const sortedTiers = sortTiers(tiers);
  let remaining = quantity;
  let previousUpTo = 0;

  for (const tier of sortedTiers) {
    const tierMax = tier.up_to ?? Number.POSITIVE_INFINITY;
    const tierRange = tierMax - previousUpTo;

    if (remaining <= tierRange) {
      return tier.unit_amount !== null ? tier.unit_amount / 100 : 0;
    }

    remaining -= tierRange;
    previousUpTo = tierMax;
  }

  // Fallback to last tier's price if quantity exceeds all defined tiers
  const lastTier = sortedTiers[sortedTiers.length - 1];
  return lastTier.unit_amount !== null ? lastTier.unit_amount / 100 : 0;
};

/**
 * Represents a parsed feature with optional icon and description.
 *
 * @typedef {Object} ParsedFeature
 * @property {string} [icon] - Icon identifier for the feature
 * @property {string} name - Name of the feature
 * @property {string} [description] - Optional description of the feature
 */
export type ParsedFeature = {
  icon?: string;
  name: string;
  description?: string;
  disabled?: boolean;
};

/**
 * Parses a feature text string into structured feature data.
 * Supports format: "[icon] Feature Name (Optional description)"
 *
 * @param {string} text - The feature text to parse
 * @returns {ParsedFeature} Structured feature data
 * @example
 * // Returns { icon: "check", name: "Unlimited Users", description: "No user limits" }
 * parseFeatureText("[check] Unlimited Users (No user limits)");
 *
 * // Returns { name: "Basic Support" }
 * parseFeatureText("Basic Support");
 */
export const parseFeatureText = (text: string): ParsedFeature => {
  const pattern = /^\[([^\]]+)\]\s*([^(]+)(?:\((.*)\))?$/;
  const match = text.match(pattern);

  return match
    ? {
        icon: match[1].trim(),
        name: match[2].trim(),
        description: match[3]?.trim(),
        disabled: match[1].trim() === "n",
      }
    : { name: text.trim() };
};
