interface FormatCurrencyOptions extends Omit<Intl.NumberFormatOptions, 'locale'> {
  /**
   * The locale to use for formatting (default: 'pt-BR')
   */
  locale?: string;
  
  /**
   * Whether to hide the currency symbol (default: false)
   */
  hideCurrency?: boolean;
  
  /**
   * Whether to hide decimal places (default: false)
   */
  hideDecimals?: boolean;
}

/**
 * Format a number as currency with customizable options
 * @param value The number to format
 * @param options Formatting options
 * @returns Formatted currency string
 */
export const formatCurrency = (
  value?: number | null,
  options: FormatCurrencyOptions = {}
): string => {
  // Default value handling
  if (value === undefined || value === null) return "0";
  
  // Extract custom options that need special handling
  const {
    locale = "pt-BR",
    hideCurrency = false,
    hideDecimals = false,
    ...restOptions
  } = options;
  
  // Base Intl.NumberFormat options
  const numberFormatOptions: Intl.NumberFormatOptions = {
    // Set style based on whether we want to hide currency
    style: hideCurrency ? "decimal" : "currency",
    // If showing currency, default to BRL unless specified
    currency: hideCurrency ? undefined : (options.currency || "BRL"),
    // Handle decimal places
    minimumFractionDigits: hideDecimals ? 0 : (options.minimumFractionDigits ?? 2),
    maximumFractionDigits: hideDecimals ? 0 : (options.maximumFractionDigits ?? 2),
    // Include other provided options
    ...restOptions
  };
  
  return new Intl.NumberFormat(locale, numberFormatOptions).format(value / 100);
};
