/**
 * Generates initials from a name string
 * @param name The name to generate initials from
 * @returns Two letter initials for the name
 * @example
 * getInitials("<PERSON>") // returns "<PERSON>"
 * getInitials("<PERSON>") // returns "KJ"
 * getInitials("<PERSON><PERSON>") // returns "KA"
 * getInitials("J") // returns "J"
 */
export const getInitials = (name?: string): string => {
  if (!name) return "";

  const words = name.trim().split(" ");

  // For single letter
  if (name.length === 1) {
    return name.toUpperCase();
  }

  // For single word, return first two letters
  if (words.length === 1) {
    return name.slice(0, 2).toUpperCase();
  }

  // For multiple words, take first letter of first two words
  return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
};
