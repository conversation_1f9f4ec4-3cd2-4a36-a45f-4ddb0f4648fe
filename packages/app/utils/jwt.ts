export enum TokenStatus {
  Valid = "valid",
  Invalid = "invalid",
  Expiring = "expiring",
}

export function CheckTokenStatus(token: string): TokenStatus {
  const parts = token.split(".");
  if (parts.length !== 3) return TokenStatus.Invalid;
  try {
    const data = DecodeJWT(token);
    if (!data?.exp) return TokenStatus.Invalid;

    const now = Date.now() / 1000;
    if (data.exp <= now) return TokenStatus.Invalid;

    const duration = data.exp - data.iat;
    if (duration <= 0) return TokenStatus.Invalid;

    const elapsed = now - data.iat;
    if (elapsed <= 0) return TokenStatus.Invalid;

    if (elapsed >= duration * 0.5) {
      return TokenStatus.Expiring;
    }

    return TokenStatus.Valid;
  } catch (e) {
    console.error(e);
    return TokenStatus.Invalid;
  }
}

export function DecodeJWT(token: string) {
  const payload = token.split(".")[1];
  return JSON.parse(atob(payload));
}
