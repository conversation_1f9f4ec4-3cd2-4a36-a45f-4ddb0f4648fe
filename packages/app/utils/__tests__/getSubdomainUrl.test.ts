import { getSubdomainUrl } from '../getSubdomainUrl';

// Mock window.location for testing
const mockWindowLocation = (hostname: string, port = '') => {
  // Save the original window.location
  const originalLocation = window.location;

  // Mock window.location
  delete window.location;
  window.location = { ...originalLocation, hostname, port } as any;

  // Return a cleanup function
  return () => {
    window.location = originalLocation;
  };
};

describe('getSubdomainUrl', () => {
  it('should return production URL when hostname is app.imoblr.com.br', () => {
    const cleanup = mockWindowLocation('app.imoblr.com.br');

    expect(getSubdomainUrl('test-subdomain')).toBe('https://test-subdomain.imoblr.com.br');

    cleanup();
  });

  it('should return preview URL when hostname is preview.app.imoblr.com.br', () => {
    const cleanup = mockWindowLocation('preview.app.imoblr.com.br');

    expect(getSubdomainUrl('test-subdomain')).toBe('https://test-subdomain.preview.imoblr.com.br');

    cleanup();
  });

  it('should return preview URL when hostname is staging.app.imoblr.com.br', () => {
    const cleanup = mockWindowLocation('staging.app.imoblr.com.br');

    expect(getSubdomainUrl('test-subdomain')).toBe('https://test-subdomain.staging.imoblr.com.br');

    cleanup();
  });

  it('should return development URL when hostname is localhost', () => {
    const cleanup = mockWindowLocation('localhost', '3000');

    expect(getSubdomainUrl('test-subdomain')).toBe('http://localhost:3000/test-subdomain');

    cleanup();
  });

  it('should return development URL when hostname is 127.0.0.1', () => {
    const cleanup = mockWindowLocation('127.0.0.1', '8081');

    expect(getSubdomainUrl('test-subdomain')).toBe('http://127.0.0.1:8081/test-subdomain');

    cleanup();
  });

  it('should return development URL when hostname is a local IP address', () => {
    const cleanup = mockWindowLocation('*************', '19000');

    expect(getSubdomainUrl('test-subdomain')).toBe('http://*************:19000/test-subdomain');

    cleanup();
  });

  it('should use default port 8081 when no port is provided in development', () => {
    const cleanup = mockWindowLocation('localhost', '');

    expect(getSubdomainUrl('test-subdomain')).toBe('http://localhost:8081/test-subdomain');

    cleanup();
  });

  it('should return production URL when hostname is not recognized', () => {
    const cleanup = mockWindowLocation('unknown-host.com');

    expect(getSubdomainUrl('test-subdomain')).toBe('https://test-subdomain.imoblr.com.br');

    cleanup();
  });

  it('should handle empty subdomain', () => {
    const cleanup = mockWindowLocation('app.imoblr.com.br');

    expect(getSubdomainUrl('')).toBe('https://.imoblr.com.br');

    cleanup();
  });
});
