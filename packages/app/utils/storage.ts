import { Platform } from "react-native";
import * as SecureStore from "expo-secure-store";

export class GenericStorageClass {
	get = async (): Promise<string | null> => null;
	set: (value: string) => Promise<void> = async (): Promise<void> => {};
	delete: () => Promise<void> = async (): Promise<void> => {};

	constructor(key: string) {
		if (Platform.OS === "web") {
			this.get = async (): Promise<string | null> =>
				window.localStorage.getItem(key);
			this.set = async (value: string) =>
				window.localStorage.setItem(key, value);
			this.delete = async () => window.localStorage.removeItem(key);
		}

		if (Platform.OS === "android" || Platform.OS === "ios") {
			this.get = () => SecureStore.getItemAsync(key);
			this.set = (value: string) => SecureStore.setItemAsync(key, value);
			this.delete = () => SecureStore.deleteItemAsync(key);
		}
	}
}

const storage = {
	getItem: async (key: string) => {
		if (Platform.OS === "web") {
			return window.localStorage.getItem(key);
		}

		if (Platform.OS === "android" || Platform.OS === "ios") {
			return SecureStore.getItemAsync(key);
		}

		throw new Error("Unsupported platform");
	},
	setItem: async (key: string, value: string) => {
		if (Platform.OS === "web") {
			return window.localStorage.setItem(key, value);
		}

		if (Platform.OS === "android" || Platform.OS === "ios") {
			return SecureStore.setItemAsync(key, value);
		}

		throw new Error("Unsupported platform");
	},
};
