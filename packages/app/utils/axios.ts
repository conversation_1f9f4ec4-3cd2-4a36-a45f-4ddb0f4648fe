import axios from "axios";

// Create axios instance with base configuration
const axiosInstance = axios.create({
  baseURL: process.env.EXPO_PUBLIC_API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add a request interceptor that will try to get the token from localStorage in web environments
// This provides a fallback for components that don't use the useAxios hook
axiosInstance.interceptors.request.use(
  (config) => {
    // Skip adding auth token if it's already set by useAxios hook
    if (config.headers.Authorization) {
      return config;
    }

    // Only run in browser environment
    if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
      try {
        // Try to get the session from localStorage
        const sessionStr = localStorage.getItem('supabase.auth.token');
        if (sessionStr) {
          try {
            const session = JSON.parse(sessionStr);
            const accessToken = session?.currentSession?.access_token;

            if (accessToken) {
              config.headers.Authorization = `Bearer ${accessToken}`;
              console.log('[axios] Added auth token from localStorage for request:', config.url);
            } else {
              console.warn('[axios] No auth token found in localStorage for request:', config.url);
            }
          } catch (parseError) {
            console.error('[axios] Error parsing session JSON:', parseError);
          }
        } else {
          console.warn('[axios] No session found in localStorage for request:', config.url);
        }
      } catch (error) {
        // Log error if localStorage access fails
        console.error('[axios] Error accessing auth token:', error);
      }
    } else {
      console.log('[axios] Not in browser environment or localStorage not available');
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor for debugging
axiosInstance.interceptors.response.use(
  (response) => {
    console.log(`[axios] Response from ${response.config.url}:`, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    });
    return response;
  },
  (error) => {
    console.error("[axios] Error response:", {
      url: error.config?.url,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
    });
    return Promise.reject(error);
  }
);

export default axiosInstance;