/**
 * Generates a URL for a custom domain based on the current environment
 *
 * In production, it returns https://[domain]
 * In preview, it returns https://[domain]
 * In development (localhost, 127.0.0.1, etc.), it returns http://localhost:4321/[domain]
 *
 * @param domain - The custom domain to generate a URL for
 * @returns The complete URL for the custom domain
 */
export const getCustomDomainUrl = (domain: string): string => {
  // Get the current hostname if we're in a browser environment
  const hostname = typeof window !== 'undefined' ? window.location.hostname : '';

  // Check if we're in production, preview, or development environment
  const isProduction = hostname === 'app.imoblr.com.br';
  const isPreview = hostname.endsWith('.app.imoblr.com.br') && hostname !== 'app.imoblr.com.br';
  const isDevelopment = hostname === 'localhost' || hostname === '127.0.0.1' || hostname.startsWith('192.168.');

  // Generate the URL based on the environment
  if (isProduction || isPreview) {
    return `https://${domain}`;
  } if (isDevelopment) {
    // For development environment, use the astro app port (4321)
    // This will route through our local proxy to the Astro app
    // Use the domain= prefix to indicate this is a custom domain
    return `http://${hostname}:4321/${domain}`;
  }
  // Default to production URL if environment can't be determined
  return `https://${domain}`;
};
