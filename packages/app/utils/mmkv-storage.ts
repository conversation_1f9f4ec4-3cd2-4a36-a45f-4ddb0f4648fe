import { MMKV } from 'react-native-mmkv';
import { StorageAdapter } from '@supabase/supabase-js';

let storage: MMKV | null = null;

// Initialize storage only on client side
if (typeof window !== 'undefined') {
  storage = new MMKV();
}

export const MMKVStorage: StorageAdapter = {
  getItem: (key: string) => {
    if (!storage) return Promise.resolve(null);
    const value = storage.getString(key);
    return Promise.resolve(value ?? null);
  },
  setItem: (key: string, value: string) => {
    if (!storage) return Promise.resolve();
    storage.set(key, value);
    return Promise.resolve();
  },
  removeItem: (key: string) => {
    if (!storage) return Promise.resolve();
    storage.delete(key);
    return Promise.resolve();
  },
};
