import Constants from "expo-constants";

// Get the hostname based on the current environment
export const getWebsiteHostname = (): string => {
  const environment = Constants.expoConfig?.extra?.environment || "development";

  switch (environment) {
    case "production":
      return "imoblr.com.br";
    case "staging":
      return "staging.imoblr.com.br";
    default:
      return "localhost";
  }
};
