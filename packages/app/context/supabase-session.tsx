import type React from "react";
import { createContext, useContext, useEffect, useState } from "react";
import type { Session } from "@supabase/supabase-js";
import { supabaseClient } from "@/utils/supabase";
import { Platform } from "react-native";

type SupabaseSessionContextType = {
  session: Session | null;
  loading: boolean;
};

const SupabaseSessionContext = createContext<SupabaseSessionContextType>({
  session: null,
  loading: true,
});

export function SupabaseSessionProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Skip session initialization on server side
    if (Platform.OS === "web" && typeof window === "undefined") {
      setLoading(false);
      return;
    }

    // Get initial session
    supabaseClient.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabaseClient.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  return <SupabaseSessionContext.Provider value={{ session, loading }}>{children}</SupabaseSessionContext.Provider>;
}

export function useSupabaseSession() {
  const context = useContext(SupabaseSessionContext);
  if (context === undefined) {
    throw new Error("useSupabaseSession must be used within a SupabaseSessionProvider");
  }
  return context;
}
