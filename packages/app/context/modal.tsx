import TeamPicker from "@/components/TeamPicker";
import type React from "react";
import { createContext, useContext, useState, useMemo, memo, useCallback } from "react";

type ModalContextType = {
  openTeamPickerModal: () => void;
  closeTeamPickerModal: () => void;
};

const initialContextValue: ModalContextType = {
  openTeamPickerModal: () => {},
  closeTeamPickerModal: () => {},
};

const ModalContext = createContext<ModalContextType>(initialContextValue);

const MemoizedChildren = memo(({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
});

const ModalProvider = ({ children }: { children: React.ReactNode }) => {
  const [isTeamPickerModalOpen, setIsTeamPickerModalOpen] = useState(false);

  const openTeamPickerModal = useCallback(() => {
    setIsTeamPickerModalOpen(true);
  }, []);

  const closeTeamPickerModal = useCallback(() => {
    setIsTeamPickerModalOpen(false);
  }, []);

  const contextValue = useMemo(
    () => ({
      openTeamPickerModal,
      closeTeamPickerModal,
    }),
    [openTeamPickerModal, closeTeamPickerModal],
  );

  return (
    <ModalContext.Provider value={contextValue}>
      {isTeamPickerModalOpen && (
        <TeamPicker isOpen={isTeamPickerModalOpen} onClose={() => setIsTeamPickerModalOpen(false)} />
      )}
      <MemoizedChildren>{children}</MemoizedChildren>
    </ModalContext.Provider>
  );
};

const useModals = () => {
  return useContext(ModalContext);
};

export { ModalProvider, useModals };
