import type React from "react";
import { createContext, useContext, useState, useMemo, memo, useCallback } from "react";
import CreateNewProperty from "@/components/CreateNewProperty";
import CreateNewTeam from "@/components/CreateNewTeam";

type DrawersContextType = {
  openCreateTeamDrawer: () => void;
  openCreatePropertyDrawer: () => void;
  closeCreateTeamDrawer: () => void;
  closeCreatePropertyDrawer: () => void;
};

const initialContextValue: DrawersContextType = {
  openCreateTeamDrawer: () => {},
  openCreatePropertyDrawer: () => {},
  closeCreateTeamDrawer: () => {},
  closeCreatePropertyDrawer: () => {},
};

const DrawersContext = createContext<DrawersContextType>(initialContextValue);

const MemoizedChildren = memo(({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
});

const DrawersProvider = ({ children }: { children: React.ReactNode }) => {
  const [isCreateTeamDrawerOpen, setIsCreateTeamDrawerOpen] = useState(false);
  const [isCreatePropertyDrawerOpen, setIsCreatePropertyDrawerOpen] = useState(false);

  const openCreateTeamDrawer = useCallback(() => {
    setIsCreateTeamDrawerOpen(true);
  }, []);

  const closeCreateTeamDrawer = useCallback(() => {
    setIsCreateTeamDrawerOpen(false);
  }, []);

  const openCreatePropertyDrawer = useCallback(() => {
    setIsCreatePropertyDrawerOpen(true);
  }, []);

  const closeCreatePropertyDrawer = useCallback(() => {
    setTimeout(() => {
      setIsCreatePropertyDrawerOpen(false);
    }, 50);
  }, []);

  const contextValue = useMemo(
    () => ({
      openCreateTeamDrawer,
      openCreatePropertyDrawer,
      closeCreateTeamDrawer,
      closeCreatePropertyDrawer,
    }),
    [openCreateTeamDrawer, openCreatePropertyDrawer, closeCreateTeamDrawer, closeCreatePropertyDrawer],
  );

  return (
    <DrawersContext.Provider value={contextValue}>
      {isCreateTeamDrawerOpen && (
        <CreateNewTeam mode="drawer" isOpen={isCreateTeamDrawerOpen} onClose={closeCreateTeamDrawer} />
      )}
      {isCreatePropertyDrawerOpen && (
        <CreateNewProperty mode="drawer" isOpen={isCreatePropertyDrawerOpen} onClose={closeCreatePropertyDrawer} />
      )}
      <MemoizedChildren>{children}</MemoizedChildren>
    </DrawersContext.Provider>
  );
};

const useDrawers = () => {
  return useContext(DrawersContext);
};

export { DrawersProvider, useDrawers };
