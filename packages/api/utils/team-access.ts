import type { Env } from "@/api/types";
import type { SupabaseClient } from "@supabase/supabase-js";
import { getSupabaseClient } from "./supabase";

/**
 * Check if a user is a member of a team using the current_user_team_role function
 * @param teamId The team ID to check membership for
 * @param supabase An initialized Supabase client with the user's JWT
 * @returns True if the user is a member of the team, false otherwise
 */
export async function isUserTeamMember(
  teamId: string,
  supabase: SupabaseClient
): Promise<boolean> {
  try {
    // Use the current_user_team_role RPC function to check team membership
    // This function will return the user's role if they are a member, or throw an error if not
    const { data, error } = await supabase
      .rpc('current_user_team_role', { team_id: teamId });

    if (error) {
      // If the error message indicates the user is not a member, return false
      if (error.message.includes('User is not a member of this team')) {
        console.warn(`User is not a member of team ${teamId}`);
        return false;
      }

      console.error("Error checking team membership:", error);
      return false;
    }

    // If we got data back, the user is a member
    return !!data;
  } catch (error) {
    console.error("Exception checking team membership:", error);
    return false;
  }
}

/**
 * Verify that a user has access to a team
 * @param teamId The team ID to check access for
 * @param env The environment variables
 * @param jwt The user's JWT token
 * @returns An object with success status and optional error response
 */
export async function verifyTeamAccess(
  teamId: string,
  env: Env,
  jwt: string
): Promise<{ success: boolean; error?: Response }> {
  // Get a Supabase client with the user's JWT
  const { client: supabase, error: supabaseError } = getSupabaseClient(env, jwt);

  if (supabaseError || !supabase) {
    console.error("Failed to initialize Supabase client for team access check");
    return {
      success: false,
      error: supabaseError || new Response(
        JSON.stringify({ error: "Server error: Unable to verify team access" }),
        { status: 500, headers: { "Content-Type": "application/json" } }
      )
    };
  }

  // Check if the user is a member of the team
  const isMember = await isUserTeamMember(teamId, supabase);

  if (!isMember) {
    console.warn(`User attempted to access team ${teamId} without membership`);
    return {
      success: false,
      error: new Response(
        JSON.stringify({ error: "Access denied: You are not a member of this team" }),
        { status: 403, headers: { "Content-Type": "application/json" } }
      )
    };
  }

  return { success: true };
}
