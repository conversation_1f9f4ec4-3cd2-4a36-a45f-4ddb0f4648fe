import type { Env } from "../types";
import { createDbClient } from "./db";
import { teamUsers } from "../schema/billing";
import { eq, and } from "drizzle-orm";

/**
 * Check if a user has a specific role on a team
 * @param userId The user ID to check
 * @param teamId The team ID to check
 * @param requiredRole The required role ('owner' or 'member')
 * @param env Environment variables
 * @returns Promise<boolean> - true if user has the required role or higher
 */
export async function hasTeamRole(
  userId: string,
  teamId: string,
  requiredRole: 'owner' | 'member',
  env: Env
): Promise<boolean> {
  try {
    // Use Drizzle ORM with direct database connection (same as billing endpoint)
    console.log(`[teamRoleCheck] Checking role for user ${userId} on team ${teamId}, required role: ${requiredRole}`);
    const db = createDbClient(env);

    // Query the team_users table using Drizzle ORM
    console.log(`[teamRoleCheck] Executing Drizzle query for team_users table`);
    const userRoleResult = await db
      .select({
        teamRole: teamUsers.teamRole,
      })
      .from(teamUsers)
      .where(and(
        eq(teamUsers.userId, userId),
        eq(teamUsers.teamId, teamId)
      ))
      .limit(1);

    console.log(`[teamRoleCheck] Query result:`, userRoleResult);

    if (userRoleResult.length === 0) {
      console.log(`[teamRoleCheck] User ${userId} is not a member of team ${teamId}`);
      return false;
    }

    // Check if user has the required role
    const userRole = userRoleResult[0].teamRole;
    console.log(`[teamRoleCheck] User ${userId} has role ${userRole} on team ${teamId}`);
    
    if (requiredRole === 'member') {
      // Any role (owner or member) satisfies member requirement
      return userRole === 'owner' || userRole === 'member';
    } else if (requiredRole === 'owner') {
      // Only owner role satisfies owner requirement
      return userRole === 'owner';
    }

    return false;
  } catch (error) {
    console.error('Error checking team role:', error);
    return false;
  }
}

/**
 * Check if a user is an owner of a team
 * @param userId The user ID to check
 * @param teamId The team ID to check
 * @param env Environment variables
 * @returns Promise<boolean> - true if user is an owner of the team
 */
export async function isTeamOwner(
  userId: string,
  teamId: string,
  env: Env
): Promise<boolean> {
  return hasTeamRole(userId, teamId, 'owner', env);
}

/**
 * Check if a user is a member (owner or member) of a team
 * @param userId The user ID to check
 * @param teamId The team ID to check
 * @param env Environment variables
 * @returns Promise<boolean> - true if user is a member of the team
 */
export async function isTeamMember(
  userId: string,
  teamId: string,
  env: Env
): Promise<boolean> {
  return hasTeamRole(userId, teamId, 'member', env);
}

/**
 * Get a user's role on a team
 * @param userId The user ID to check
 * @param teamId The team ID to check
 * @param env Environment variables
 * @returns Promise<'owner' | 'member' | null> - the user's role or null if not a member
 */
export async function getUserTeamRole(
  userId: string,
  teamId: string,
  env: Env
): Promise<'owner' | 'member' | null> {
  try {
    // Use Drizzle ORM with direct database connection (same as billing endpoint)
    const db = createDbClient(env);

    // Query the team_users table using Drizzle ORM
    const userRoleResult = await db
      .select({
        teamRole: teamUsers.teamRole,
      })
      .from(teamUsers)
      .where(and(
        eq(teamUsers.userId, userId),
        eq(teamUsers.teamId, teamId)
      ))
      .limit(1);

    if (userRoleResult.length === 0) {
      return null;
    }

    return userRoleResult[0].teamRole as 'owner' | 'member';
  } catch (error) {
    console.error('Error getting user team role:', error);
    return null;
  }
}
