import * as jose from "jose";
import type { Env } from "../types"; // Use the relative path

// Verify JWT token using jose library
export async function verifyJwt(token: string, secret: string): Promise<jose.JWTPayload | null> {
  if (!token) return null;

  if (!secret) {
    console.error("JWT verification failed: Missing JWT secret");
    return null;
  }

  try {
    const secretBuffer = new TextEncoder().encode(secret);
    if (secretBuffer.length === 0) {
      console.error("JWT verification failed: Empty JWT secret");
      return null;
    }

    const { payload } = await jose.jwtVerify(token, secretBuffer);
    return payload;
  } catch (error) {
    console.error("JWT verification failed:", error);
    return null;
  }
}

// Extract token from Authorization header
// Use the global Request type
export function extractToken(request: Request): string | null {
  const authHeader = request.headers.get("Authorization");
  if (!authHeader) return null;

  return authHeader.startsWith("Bearer ") ? authHeader.slice(7) : authHeader;
}

// Verify authentication for an incoming request
// Use the global Request type
export async function verifyAuthentication(
  request: Request,
  env: Env,
): Promise<{ isAuthenticated: boolean; userId?: string; error?: string }> {
  const token = extractToken(request);

  if (!token) {
    return {
      isAuthenticated: false,
      error: "Authorization header missing or invalid",
    };
  }

  const payload = await verifyJwt(token, env.SUPABASE_JWT_SECRET);

  if (!payload || !payload.sub) {
    // Check for payload and 'sub' claim
    return {
      isAuthenticated: false,
      error: "Invalid or expired token, or missing subject claim",
    };
  }

  return {
    isAuthenticated: true,
    userId: payload.sub, // 'sub' usually contains the user ID
  };
}

// Create an unauthorized response
// Note: CORS headers might be handled globally by the router framework (e.g., Hono)
// If using this function directly, ensure CORS headers are appropriate.
export function createUnauthorizedResponse(message = "Unauthorized"): Response {
  return new Response(JSON.stringify({ error: message }), {
    status: 401,
    headers: { "Content-Type": "application/json" },
  });
}
