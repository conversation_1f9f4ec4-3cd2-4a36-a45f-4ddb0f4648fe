import type { Env } from '@/api/types';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

// Create a postgres client with the connection string
export function createPgClient(env: Env) {
  if (!env.DATABASE_URL) {
    throw new Error('DATABASE_URL environment variable is required');
  }

  console.log('Creating database connection with URL:', env.DATABASE_URL.replace(/:[^:@]*@/, ':***@')); // Log URL with password masked

  // Check if the URL already includes search_path
  const hasSearchPath = env.DATABASE_URL.includes('search_path=');

  // Disable prefetch as it is not supported for "Transaction" pool mode
  // Set the schema search path to include basejump and auth schemas
  return postgres(env.DATABASE_URL, {
    prepare: false,
    // This ensures the search_path includes the schemas we need (if not already in URL)
    ...(hasSearchPath ? {} : { options: "-c search_path=public,basejump,auth" }),
    // Log queries for debugging
    debug: true,
    // Explicitly set the schema
    schema: 'basejump,auth,public'
  });
}

// Create a drizzle client with the postgres client
export function createDbClient(env: Env) {
  const client = createPgClient(env);
  return drizzle(client, {
    // Log queries for debugging
    logger: {
      logQuery: (query, params) => {
        console.log('Executing query:', query);
        console.log('With params:', params);
      }
    }
  });
}
