import type { Env } from "@/api/types"; // Using path alias
import { createClient, type SupabaseClient } from "@supabase/supabase-js";

// Initialize Supabase client with user JWT
export function initSupabase(env: Env, jwt: string): SupabaseClient {
  const options: { auth: { autoRefreshToken: boolean; persistSession: boolean }; global: { headers: { Authorization: string } } } = {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
    global: {
      headers: {
        Authorization: `Bearer ${jwt}`,
      },
    },
  };

  // Use the anon key with JWT for user-specific operations
  // This ensures RLS policies are enforced
  return createClient(env.SUPABASE_URL, env.SUPABASE_ANON_KEY, options);
}

// Initialize Supabase client with service role (admin access)
// ONLY use this for operations that require admin privileges
export function initSupabaseAdmin(env: Env): SupabaseClient {
  const options = {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  };

  return createClient(env.SUPABASE_URL, env.SUPABASE_SERVICE_ROLE_KEY, options);
}

// Get a Supabase client instance with error handling
export function getSupabaseClient(env: Env, jwt?: string): { client: SupabaseClient | null; error: Response | null } {
  try {
    // Require JWT for user-specific operations
    if (!jwt) {
      console.error("JWT token required but not provided to Supabase client");
      return {
        client: null,
        error: new Response(JSON.stringify({ error: "Authentication error: JWT token required" }), {
          status: 401,
          headers: { "Content-Type": "application/json" },
        }),
      };
    }

    return { client: initSupabase(env, jwt), error: null };
  } catch (error: unknown) {
    console.error("Supabase initialization error:", error);
    return {
      client: null,
      error: new Response(JSON.stringify({ error: "Server configuration error: Unable to connect to database" }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }),
    };
  }
}

// Get a Supabase admin client instance with error handling
// ONLY use this for operations that require admin privileges
export function getSupabaseAdminClient(env: Env): { client: SupabaseClient | null; error: Response | null } {
  try {
    return { client: initSupabaseAdmin(env), error: null };
  } catch (error: unknown) {
    console.error("Supabase admin initialization error:", error);
    return {
      client: null,
      error: new Response(JSON.stringify({ error: "Server configuration error: Unable to connect to database" }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }),
    };
  }
}
