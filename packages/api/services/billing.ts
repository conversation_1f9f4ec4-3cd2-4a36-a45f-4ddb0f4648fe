import { and, desc, eq, sql } from 'drizzle-orm';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import {
  authSchema,
  authUsers,
  basejumpSchema,
  billingCustomers,
  billingSubscriptions,
  config,
  teamAccounts,
  teamUsers
} from '../schema/billing';

export interface BillingStatusResult {
  status: string | null;
  trial_start: Date | null;
  trial_end: Date | null;
  current_period_start: Date | null;
  current_period_end: Date | null;
  cancel_at_period_end: boolean | null;
  cancel_at: Date | null;
  canceled_at: Date | null;
  plan_name: string | null;
  quantity: number | null;
  provider: string | null;
  subscription_id: string | null;
  customer_id: string | null;
  team_id: string;
  billing_email: string | null;
  account_role: string | null;
  is_primary_owner: boolean;
  billing_enabled: boolean;
}

export async function getTeamBillingStatus(
  db: PostgresJsDatabase,
  teamId: string,
  userId: string
): Promise<BillingStatusResult | null> {
  try {
    // Test query to verify database connection and schema access
    console.log('Running test query to verify database connection...');
    const testResult = await db.execute(sql`SELECT current_schema(), current_user, current_database()`);
    console.log('Database connection test result:', testResult);

    // Test query to verify schema access
    console.log('Testing schema access...');
    const schemaTest = await db.execute(sql`SELECT schema_name FROM information_schema.schemata WHERE schema_name IN ('basejump', 'auth', 'public')`);
    console.log('Available schemas:', schemaTest);

    // Test direct SQL query to access team_accounts table
    console.log('Testing direct SQL query to team_accounts table...');
    try {
      const directSqlTest = await db.execute(sql`SELECT * FROM basejump.team_accounts WHERE id = ${teamId} LIMIT 1`);
      console.log('Direct SQL query result:', directSqlTest);
    } catch (sqlError) {
      console.error('Error with direct SQL query:', sqlError);
    }
    // Get team account information
    const teamResult = await db
      .select({
        id: teamAccounts.id,
        primaryOwnerUserId: teamAccounts.primaryOwnerUserId,
      })
      .from(teamAccounts)
      .where(eq(teamAccounts.id, teamId))
      .limit(1);

    if (teamResult.length === 0) {
      return null;
    }

    const team = teamResult[0];
    const isPrimaryOwner = team.primaryOwnerUserId === userId;

    // Get user role in the team
    const userRoleResult = await db
      .select({
        teamRole: teamUsers.teamRole,
      })
      .from(teamUsers)
      .where(and(
        eq(teamUsers.teamId, teamId),
        eq(teamUsers.userId, userId)
      ))
      .limit(1);

    const accountRole = userRoleResult.length > 0 ? userRoleResult[0].teamRole : null;

    // Get subscription information
    const subscriptionResult = await db
      .select({
        id: billingSubscriptions.id,
        status: billingSubscriptions.status,
        trialStart: billingSubscriptions.trialStart,
        trialEnd: billingSubscriptions.trialEnd,
        currentPeriodStart: billingSubscriptions.currentPeriodStart,
        currentPeriodEnd: billingSubscriptions.currentPeriodEnd,
        cancelAtPeriodEnd: billingSubscriptions.cancelAtPeriodEnd,
        cancelAt: billingSubscriptions.cancelAt,
        canceledAt: billingSubscriptions.canceledAt,
        planName: billingSubscriptions.planName,
        quantity: billingSubscriptions.quantity,
        provider: billingSubscriptions.provider,
        billingCustomerId: billingSubscriptions.billingCustomerId,
      })
      .from(billingSubscriptions)
      .where(eq(billingSubscriptions.teamAccountId, teamId))
      .orderBy(desc(billingSubscriptions.created))
      .limit(1);

    const subscription = subscriptionResult.length > 0 ? subscriptionResult[0] : null;

    // Get customer information
    const customerResult = await db
      .select({
        id: billingCustomers.id,
        email: billingCustomers.email,
      })
      .from(billingCustomers)
      .where(eq(billingCustomers.accountId, teamId))
      .limit(1);

    const customer = customerResult.length > 0 ? customerResult[0] : null;

    // Get primary owner email as fallback
    const primaryOwnerResult = await db
      .select({
        email: authUsers.email,
      })
      .from(authUsers)
      .where(eq(authUsers.id, team.primaryOwnerUserId))
      .limit(1);

    const primaryOwnerEmail = primaryOwnerResult.length > 0 ? primaryOwnerResult[0].email : null;

    // Get config information
    const configResult = await db
      .select({
        billingProvider: config.billingProvider,
        enableTeamAccountBilling: config.enableTeamAccountBilling,
      })
      .from(config)
      .limit(1);

    const configInfo = configResult.length > 0 ? configResult[0] : {
      billingProvider: 'stripe',
      enableTeamAccountBilling: true
    };

    // Combine all the information
    return {
      status: subscription?.status || null,
      trial_start: subscription?.trialStart || null,
      trial_end: subscription?.trialEnd || null,
      current_period_start: subscription?.currentPeriodStart || null,
      current_period_end: subscription?.currentPeriodEnd || null,
      cancel_at_period_end: subscription?.cancelAtPeriodEnd || false,
      cancel_at: subscription?.cancelAt || null,
      canceled_at: subscription?.canceledAt || null,
      plan_name: subscription?.planName || null,
      quantity: subscription?.quantity || null,
      provider: configInfo.billingProvider,
      subscription_id: subscription?.id || null,
      customer_id: customer?.id || null,
      team_id: teamId,
      billing_email: customer?.email || primaryOwnerEmail,
      account_role: accountRole,
      is_primary_owner: isPrimaryOwner,
      billing_enabled: configInfo.enableTeamAccountBilling,
    };
  } catch (error) {
    console.error('Error getting team billing status:', error);
    throw error;
  }
}
