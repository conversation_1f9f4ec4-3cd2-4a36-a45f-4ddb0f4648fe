import { cors } from "hono/cors";
import { Hono } from "hono/tiny";
import { checkRequiredEnvVarsMiddleware } from "./env";
import addressesRouter from "./routes/addresses"; // Import the sub-router
import billingRouter from "./routes/billing";
import domainsRouter from "./routes/domains";
import mediaRouter from "./routes/media";
import propertiesRouter from "./routes/properties";
import teamInvitesRouter from "./routes/team-invites";
import teamsRouter from "./routes/teams";
import webhooksRouter from "./routes/webhooks";
import websitesRouter from "./routes/websites";
import type { Env } from "./types"; // Import Env type for the fetch handler

// Initialize the main Hono app
const app = new Hono<{ Bindings: Env }>();

// Middleware to check for required environment variables
app.use("*", checkRequiredEnvVarsMiddleware);

// Enable global CORS. This will handle OPTIONS preflight requests automatically.
app.use(
  "*",
  cors({
    origin: "*", // Allow all origins (adjust for production)
    allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowHeaders: ["Content-Type", "Authorization"], // Add any other custom headers used
    maxAge: 86400, // Cache preflight response for 1 day
  }),
);

// Mount the addresses sub-router at the /addresses path
app.route("/addresses", addressesRouter);

app.route("/media", mediaRouter);

app.route("/domains", domainsRouter);

app.route("/websites", websitesRouter);

app.route("/teams", teamsRouter);

app.route("/team-invites", teamInvitesRouter);

app.route("/billing", billingRouter);

app.route("/properties", propertiesRouter);

app.route("/webhooks", webhooksRouter);

// Global 404 handler for any routes not matched by sub-routers
app.notFound((c) => {
  return c.text("Route Not Found", 404);
});

// Export the Hono app (Cloudflare Workers will automatically use the fetch handler)
export default app;
