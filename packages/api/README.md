# Platform API

This is the API service migrated from the standalone Api project into the Platform monorepo.

## Development

To run the API locally:

```bash
# From the root of the Platform repo
nx serve api
```

## Building

To build the API:

```bash
# From the root of the Platform repo
nx build api
```

## Deployment

To deploy the API:

```bash
# From the root of the Platform repo
nx deploy api
```

## Integration Notes

This API was integrated into the Platform monorepo from the standalone Api project. It uses Cloudflare Workers for deployment and runs through wrangler.

### Original Workers

The project contains the following workers:
- addresses worker (./addresses)
- domains worker (./domains)

These workers can be run using the nx commands above, which will use wrangler under the hood.
