import type { Env } from "../../../types";
import { createDbClient } from "../../../utils/db";
import { createUnauthorizedResponse } from "../../../utils/auth";
import { isTeamOwner } from "../../../utils/teamRoleCheck";
import { teamInvites } from "../../../schema/billing";
import { eq } from "drizzle-orm";
import type { Context } from "hono";

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

// DELETE /team-invites/:inviteId
export const handleDeleteTeamInvite = async (c: Context<{ Bindings: Env; Variables: HandlerVariables }>) => {
  try {
    // Get auth from context (set by middleware)
    const auth = c.get("auth");
    if (!auth?.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse("Authentication required");
    }

    // Get invite ID from URL params
    const { inviteId } = c.req.param();

    if (!inviteId) {
      return c.json({ error: "Invite ID is required" }, 400);
    }

    // Use Drizzle ORM with direct database connection
    const db = createDbClient(c.env);

    // First, get the invitation to check if it exists and get the team_id
    const invitationResult = await db
      .select({
        id: teamInvites.id,
        teamId: teamInvites.teamId,
        email: teamInvites.email,
        teamName: teamInvites.teamName,
      })
      .from(teamInvites)
      .where(eq(teamInvites.id, inviteId))
      .limit(1);

    if (invitationResult.length === 0) {
      return c.json({ error: "Invitation not found" }, 404);
    }

    const invitation = invitationResult[0];

    // Check if user is owner of the team
    const isOwner = await isTeamOwner(auth.userId, invitation.teamId, c.env);

    if (!isOwner) {
      return c.json({ error: "Only team owners can delete invitations" }, 403);
    }

    // Delete the invitation
    const deleteResult = await db
      .delete(teamInvites)
      .where(eq(teamInvites.id, inviteId))
      .returning({
        id: teamInvites.id,
      });

    if (deleteResult.length === 0) {
      console.error("Error deleting team invitation: No rows affected");
      return c.json({
        error: "Failed to delete team invitation"
      }, 500);
    }

    return c.json({
      success: true,
      message: "Team invitation deleted successfully",
      deleted_invitation: {
        id: invitation.id,
        email: invitation.email,
        team_name: invitation.teamName
      }
    });

  } catch (error) {
    console.error("Error deleting team invitation:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to delete team invitation"
    }, 500);
  }
};
