import type { Env } from "../../../types";
import { createDbClient } from "../../../utils/db";
import { createUnauthorizedResponse } from "../../../utils/auth";
import { teamInvites, teamAccounts, teamUsers } from "../../../schema/billing";
import { eq, and, gte } from "drizzle-orm";
import type { Context } from "hono";

interface AcceptTeamInviteRequest {
  token: string;
}

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

// POST /team-invites/accept
export const handleAcceptTeamInvite = async (c: Context<{ Bindings: Env; Variables: HandlerVariables }>) => {
  try {
    // Get auth from context (set by middleware)
    const auth = c.get("auth");
    if (!auth?.isAuthenticated || !auth.userId) {
      return createUnauthorizedResponse("Authentication required");
    }

    const body = await c.req.json() as AcceptTeamInviteRequest;
    const { token } = body;

    // Validate required fields
    if (!token) {
      return c.json({ error: "Missing required field: token" }, 400);
    }

    // Use Drizzle ORM with direct database connection
    const db = createDbClient(c.env);

    // Calculate 24 hours ago
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    // Find the invitation by token and validate it's still active (within 24 hours)
    const invitationResult = await db
      .select({
        id: teamInvites.id,
        teamId: teamInvites.teamId,
        email: teamInvites.email,
        teamRole: teamInvites.teamRole,
        teamName: teamInvites.teamName,
        createdAt: teamInvites.createdAt,
        invitedByUserId: teamInvites.invitedByUserId,
      })
      .from(teamInvites)
      .where(and(
        eq(teamInvites.token, token),
        gte(teamInvites.createdAt, twentyFourHoursAgo)
      ))
      .limit(1);

    if (invitationResult.length === 0) {
      return c.json({ error: "Invitation not found or expired" }, 404);
    }

    const invitation = invitationResult[0];

    // Check if user is already a member of the team
    const existingMemberResult = await db
      .select({
        teamRole: teamUsers.teamRole,
      })
      .from(teamUsers)
      .where(and(
        eq(teamUsers.teamId, invitation.teamId),
        eq(teamUsers.userId, auth.userId)
      ))
      .limit(1);

    if (existingMemberResult.length > 0) {
      return c.json({ error: "You are already a member of this team" }, 400);
    }

    // Get team information for response
    const teamResult = await db
      .select({
        slug: teamAccounts.slug,
      })
      .from(teamAccounts)
      .where(eq(teamAccounts.id, invitation.teamId))
      .limit(1);

    if (teamResult.length === 0) {
      return c.json({ error: "Team not found" }, 404);
    }

    const team = teamResult[0];

    // Start a transaction-like operation by adding user to team
    const now = new Date();

    try {
      const newTeamUserResult = await db
        .insert(teamUsers)
        .values({
          teamId: invitation.teamId,
          userId: auth.userId,
          teamRole: invitation.teamRole,
          createdAt: now,
          updatedAt: now
        })
        .returning({
          id: teamUsers.id,
          teamId: teamUsers.teamId,
          userId: teamUsers.userId,
          teamRole: teamUsers.teamRole
        });

      if (newTeamUserResult.length === 0) {
        return c.json({
          error: "Failed to add user to team"
        }, 500);
      }
    } catch (error: any) {
      console.error("Error adding user to team:", error);

      // Check if it's a unique constraint violation (user already exists)
      if (error.code === '23505') {
        return c.json({ error: "You are already a member of this team" }, 400);
      }

      return c.json({
        error: "Failed to add user to team",
        details: error.message
      }, 500);
    }

    // Delete the invitation after successful acceptance
    try {
      await db
        .delete(teamInvites)
        .where(eq(teamInvites.token, token));
    } catch (error) {
      console.error("Error deleting invitation after acceptance:", error);
      // Don't fail the request since the user was successfully added to the team
      // Just log the error for monitoring
    }

    return c.json({
      success: true,
      team_id: invitation.teamId,
      team_role: invitation.teamRole,
      team_name: invitation.teamName,
      slug: team.slug,
      message: "Successfully joined the team"
    });

  } catch (error) {
    console.error("Error accepting team invitation:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to accept team invitation"
    }, 500);
  }
};
