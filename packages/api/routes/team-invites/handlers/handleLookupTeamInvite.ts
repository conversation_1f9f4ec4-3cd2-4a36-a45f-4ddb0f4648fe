import type { Env } from "../../../types";
import { createDbClient } from "../../../utils/db";
import { teamInvites, teamAccounts } from "../../../schema/billing";
import { eq, and, gte } from "drizzle-orm";
import type { Context } from "hono";

// GET /team-invites/lookup/:token
export const handleLookupTeamInvite = async (c: Context<{ Bindings: Env }>) => {
  try {
    // Get token from URL params
    const { token } = c.req.param();

    if (!token) {
      return c.json({ error: "Token is required" }, 400);
    }

    // Use Drizzle ORM with direct database connection
    const db = createDbClient(c.env);

    // Calculate 24 hours ago
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    // Find the invitation by token and validate it's still active (within 24 hours)
    const invitationResult = await db
      .select({
        id: teamInvites.id,
        teamId: teamInvites.teamId,
        email: teamInvites.email,
        teamRole: teamInvites.teamRole,
        teamName: teamInvites.teamName,
        createdAt: teamInvites.createdAt,
        invitedByUserId: teamInvites.invitedByUserId,
      })
      .from(teamInvites)
      .where(and(
        eq(teamInvites.token, token),
        gte(teamInvites.createdAt, twentyFourHoursAgo)
      ))
      .limit(1);

    if (invitationResult.length === 0) {
      return c.json({
        active: false,
        error: "Invitation not found or expired"
      }, 404);
    }

    const invitation = invitationResult[0];

    // Get additional team information
    const teamResult = await db
      .select({
        slug: teamAccounts.slug,
        publicMetadata: teamAccounts.publicMetadata,
      })
      .from(teamAccounts)
      .where(eq(teamAccounts.id, invitation.teamId))
      .limit(1);

    if (teamResult.length === 0) {
      return c.json({
        active: false,
        error: "Team not found"
      }, 404);
    }

    const team = teamResult[0];

    // For now, we'll skip the inviter information since we don't have user_accounts table in our schema
    // TODO: Add inviter information lookup when user_accounts table is available in schema
    const inviterInfo = null;

    return c.json({
      active: true,
      invitation: {
        team_name: invitation.teamName,
        team_role: invitation.teamRole,
        email: invitation.email,
        created_at: invitation.createdAt,
        team_slug: team.slug,
        invited_by: inviterInfo ? {
          name: `${inviterInfo.first_name || ''} ${inviterInfo.last_name || ''}`.trim() || 'Unknown',
          email: inviterInfo.email
        } : null
      }
    });

  } catch (error) {
    console.error("Error looking up team invitation:", error);
    return c.json({
      active: false,
      error: error instanceof Error ? error.message : "Failed to lookup team invitation"
    }, 500);
  }
};
