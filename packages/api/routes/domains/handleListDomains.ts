import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse, verifyAuthentication } from "@/api/utils/auth";
import { createClient } from "@supabase/supabase-js";
import type { Context } from "hono";

export async function handleListDomains(c: Context<{ Bindings: Env }>) {
  try {
    // Verify authentication
    const auth = await verifyAuthentication(c.req.raw, c.env);
    if (!auth.isAuthenticated) {
      return createUnauthorizedResponse(auth.error || "Authentication required");
    }

    // Get the website ID from the URL params
    const { websiteId } = c.req.param();

    if (!websiteId) {
      return c.json({ error: "Website ID is required" }, 400);
    }

    // Get the JWT token from the request
    const authHeader = c.req.raw.headers.get("Authorization");
    const jwt = authHeader ? authHeader.replace("Bearer ", "") : null;

    // Create a user-context client for database operations
    const userClient = jwt ? createClient(
      c.env.SUPABASE_URL,
      c.env.SUPABASE_ANON_KEY,
      {
        global: {
          headers: {
            Authorization: `Bearer ${jwt}`,
          },
        },
      }
    ) : null;

    // If we couldn't create a user client, use the service client
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt || undefined);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // Use the user client if available, otherwise use the service client
    const client = userClient || supabaseClient;

    // Query the custom_domains table for domains associated with this website
    const { data: domains, error: domainsError } = await client
      .from('custom_domains')
      .select('*')
      .eq('website_id', websiteId);

    if (domainsError) {
      console.error("Error fetching domains:", domainsError);
      return c.json({
        error: "Failed to fetch domains",
        details: domainsError.message
      }, 500);
    }

    return c.json({
      success: true,
      domains: domains || []
    });
  } catch (error) {
    console.error("Error listing domains:", error);
    return c.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error occurred"
    }, 500);
  }
}
