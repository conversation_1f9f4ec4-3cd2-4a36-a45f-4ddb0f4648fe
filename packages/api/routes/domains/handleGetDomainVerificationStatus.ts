import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse, verifyAuthentication } from "@/api/utils/auth";
import { createClient } from "@supabase/supabase-js";
import type { Context } from "hono";

interface CloudflareResponse {
  success: boolean;
  errors: Array<{ code: number; message: string }>;
  messages: string[];
  result: {
    id: string;
    hostname: string;
    ssl: {
      status: string;
      method: string;
      type: string;
      validation_records?: Array<{
        txt_name?: string;
        txt_value?: string;
        http_url?: string;
        http_body?: string;
      }>;
    };
    status: string;
    verification_errors: string[];
    ownership_verification?: {
      name: string;
      type: string;
      value: string;
    };
    ownership_verification_http?: {
      http_url: string;
      http_body: string;
    };
  };
}

export async function handleGetDomainVerificationStatus(c: Context<{ Bindings: Env }>) {
  try {
    // Verify authentication
    const auth = await verifyAuthentication(c.req.raw, c.env);
    if (!auth.isAuthenticated) {
      return createUnauthorizedResponse(auth.error || "Authentication required");
    }

    // Get the domain ID from the URL params
    const { domainId } = c.req.param();

    if (!domainId) {
      return c.json({ error: "Domain ID is required" }, 400);
    }

    // Get JWT from request headers for user-context operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.replace("Bearer ", "");

    // Create a user-context client for database operations if JWT is available
    const userClient = jwt ? createClient(
      c.env.SUPABASE_URL,
      c.env.SUPABASE_ANON_KEY,
      {
        global: {
          headers: {
            Authorization: `Bearer ${jwt}`,
          },
        },
      }
    ) : null;

    // If we couldn't create a user client, use the service client
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt || undefined);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // Use the user client if available, otherwise use the service client
    const client = userClient || supabaseClient;

    // Get the domain details from the database
    const { data: domain, error: domainError } = await client
      .from('custom_domains')
      .select('*')
      .eq('id', domainId)
      .single();

    if (domainError) {
      console.error("Error fetching domain:", domainError);
      return c.json({
        error: "Failed to fetch domain",
        details: domainError.message
      }, 500);
    }

    if (!domain) {
      return c.json({ error: "Domain not found" }, 404);
    }

    // Get the domain verification status from Cloudflare
    const verificationStatus = await getCloudflareVerificationStatus(c.env, domain.cloudflare_hostname_id);

    // Update the domain verification status in the database
    let newVerificationStatus: 'pending' | 'verified' | 'failed' = domain.verification_status;

    if (verificationStatus.success) {
      // Map Cloudflare status to our status
      if (verificationStatus.result.status === 'active') {
        newVerificationStatus = 'verified';
      } else if (verificationStatus.result.verification_errors && verificationStatus.result.verification_errors.length > 0) {
        newVerificationStatus = 'failed';
      } else {
        newVerificationStatus = 'pending';
      }

      // Only update if the status has changed
      if (newVerificationStatus !== domain.verification_status) {
        const { error: updateError } = await client
          .from('custom_domains')
          .update({ verification_status: newVerificationStatus, updated_at: new Date().toISOString() })
          .eq('id', domainId);

        if (updateError) {
          console.error("Error updating domain verification status:", updateError);
          return c.json({
            error: "Failed to update domain verification status",
            details: updateError.message
          }, 500);
        }
      }
    }

    // Return the verification status and raw verification data
    return c.json({
      success: true,
      domain: {
        ...domain,
        verification_status: newVerificationStatus
      },
      cloudflare_data: verificationStatus.success ? verificationStatus.result : null,
      verification_errors: verificationStatus?.result?.verification_errors || []
    });
  } catch (error) {
    console.error("Error getting domain verification status:", error);
    return c.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error occurred"
    }, 500);
  }
}

async function getCloudflareVerificationStatus(env: Env, hostnameId: string): Promise<CloudflareResponse> {
  const { CLOUDFLARE_ZONE_ID, CLOUDFLARE_CUSTOM_HOSTNAMES_API_TOKEN } = env;

  try {
    const response = await fetch(
      `https://api.cloudflare.com/client/v4/zones/${CLOUDFLARE_ZONE_ID}/custom_hostnames/${hostnameId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${CLOUDFLARE_CUSTOM_HOSTNAMES_API_TOKEN}`,
        },
      }
    );

    if (!response.ok) {
      console.error("Cloudflare API error:", {
        status: response.status,
        statusText: response.statusText
      });

      return {
        success: false,
        errors: [{ code: response.status, message: response.statusText }],
        messages: [],
        result: {
          id: hostnameId,
          hostname: "",
          ssl: {
            status: "",
            method: "",
            type: ""
          },
          status: "",
          verification_errors: ["Failed to fetch verification status from Cloudflare"]
        }
      };
    }

    const data = await response.json() as CloudflareResponse;
    return data;
  } catch (error) {
    console.error("Error fetching Cloudflare verification status:", error);
    return {
      success: false,
      errors: [{ code: 500, message: error instanceof Error ? error.message : "Unknown error" }],
      messages: [],
      result: {
        id: hostnameId,
        hostname: "",
        ssl: {
          status: "",
          method: "",
          type: ""
        },
        status: "",
        verification_errors: ["Failed to fetch verification status from Cloudflare"]
      }
    };
  }
}


