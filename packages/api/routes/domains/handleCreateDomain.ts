import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse, verifyAuthentication } from "@/api/utils/auth";
import { createClient } from "@supabase/supabase-js";
import type { Context } from "hono";

interface CloudflareResponse {
  success: boolean;
  errors: Array<{ code: number; message: string }>;
  messages: string[];
  result: {
    id: string;
    hostname: string;
    ssl: {
      status: string;
      method: string;
      type: string;
    };
    status: string;
    verification_errors: string[];
  };
}

interface ApiResponse {
  status: number;
  body: {
    success?: boolean;
    error?: string;
    details?: unknown;
    hostname?: string;
    cloudflare_data?: CloudflareResponse['result'];
  };
}

export async function handleCreateDomain(c: Context<{ Bindings: Env }>) {
  try {
    // Verify authentication
    const auth = await verifyAuthentication(c.req.raw, c.env);
    if (!auth.isAuthenticated) {
      return createUnauthorizedResponse(auth.error || "Authentication required");
    }

    // Log the authenticated user ID for debugging
    console.log("Authenticated user ID:", auth.userId);

    // Verify that the user ID matches the expected user
    if (auth.userId !== 'd9808132-321a-4366-89bb-d0a3cd9e2cb6') {
      console.warn("User ID in JWT doesn't match expected user (<EMAIL>):", auth.userId);
    }

    const { hostname, websiteId, teamAccountId } = await c.req.json<{
      hostname?: string;
      websiteId?: string;
      teamAccountId?: string;
    }>();

    if (!hostname) {
      return c.json({ error: "Hostname is required" }, 400);
    }

    if (!websiteId) {
      return c.json({ error: "Website ID is required" }, 400);
    }

    if (!teamAccountId) {
      return c.json({ error: "Team Account ID is required" }, 400);
    }

    // Check if user is a team owner
    // Get the JWT token from the request
    const authHeader = c.req.raw.headers.get("Authorization");
    const jwt = authHeader ? authHeader.replace("Bearer ", "") : null;
    console.log("Using JWT for Supabase client:", jwt ? "[JWT token present]" : "[No JWT token]");

    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt || undefined);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // Focus on making the RPC function work
    console.log("Checking if user is team owner for team ID:", teamAccountId);
    console.log("User ID from JWT:", auth.userId);

    if (!auth.userId) {
      return c.json({
        error: "Unauthorized: User ID not found in token",
      }, 403);
    }

    // Create a new Supabase client with the auth context
    // This is critical - we need to create a client that acts as the user, not as the service
    const userClient = createClient(
      c.env.SUPABASE_URL,
      c.env.SUPABASE_ANON_KEY, // Use anon key, not service role key
      {
        global: {
          headers: {
            Authorization: `Bearer ${jwt}`,
          },
        },
      }
    );

    console.log("Created user-context Supabase client with JWT token");

    // Use the current_user_team_role function from the public schema
    const { data: roleData, error: roleCheckError } = await userClient
      .rpc('current_user_team_role', { team_id: teamAccountId });

    console.log("current_user_team_role result with user client:", { roleData, error: roleCheckError });

    if (roleCheckError) {
      console.error("Error checking team role with user client:", roleCheckError);
      return c.json({
        error: "Unauthorized: Error checking team ownership",
        details: roleCheckError.message
      }, 403);
    }

    // Check if user is an owner
    if (roleData?.team_role !== 'owner' && !roleData?.is_primary_owner) {
      return c.json({
        error: "Unauthorized: Only team owners can add custom domains",
      }, 403);
    }

    const response = await createCloudflareCustomHostname(c.env, hostname, websiteId, teamAccountId, jwt || undefined);
    return c.json(response.body, response.status as 200 | 400 | 500);
  } catch (error) {
    console.error("Error creating custom domain:", error);
    return c.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error occurred"
    }, 500);
  }
}

async function createCloudflareCustomHostname(
  env: Env,
  hostname: string,
  websiteId: string,
  teamAccountId: string,
  jwt?: string
): Promise<ApiResponse> {
  const { CLOUDFLARE_ZONE_ID, CLOUDFLARE_CUSTOM_HOSTNAMES_API_TOKEN } = env;

  const cloudflareResponse = await fetch(
    `https://api.cloudflare.com/client/v4/zones/${CLOUDFLARE_ZONE_ID}/custom_hostnames`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${CLOUDFLARE_CUSTOM_HOSTNAMES_API_TOKEN}`,
      },
      body: JSON.stringify({
        hostname,
        ssl: {
          method: "txt",
          type: "dv",
          settings: {
            min_tls_version: "1.2"
          }
        }
      }),
    }
  );

  const responseData = await cloudflareResponse.json() as CloudflareResponse;

  if (!cloudflareResponse.ok) {
    console.error("Cloudflare API error:", {
      status: cloudflareResponse.status,
      error: responseData
    });

    return {
      status: cloudflareResponse.status,
      body: {
        error: "Failed to create custom hostname",
        details: responseData
      }
    };
  }

  // Create a new Supabase client with the auth context
  console.log("Using JWT in createCloudflareCustomHostname:", jwt ? "[JWT token present]" : "[No JWT token]");

  // Create a user-context client for database operations
  const userClient = jwt ? createClient(
    env.SUPABASE_URL,
    env.SUPABASE_ANON_KEY,
    {
      global: {
        headers: {
          Authorization: `Bearer ${jwt}`,
        },
      },
    }
  ) : null;

  // Fallback to service role client if no JWT is provided
  const { client: supabaseClient, error: supabaseError } = userClient
    ? { client: userClient, error: null }
    : getSupabaseClient(env);

  if (supabaseError || !supabaseClient) {
    console.error("Supabase client initialization error:", supabaseError);
    return {
      status: 500,
      body: {
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }
    };
  }

  // Insert the domain into the custom_domains table
  const { error: dbError } = await supabaseClient
    .from('custom_domains')
    .insert({
      team_account_id: teamAccountId,
      website_id: websiteId,
      domain_name: hostname,
      verification_status: 'pending',
      ssl_status: 'pending',
      is_published: true,
      cloudflare_hostname_id: responseData.result.id
    })
    .select()
    .single();

  if (dbError) {
    console.error("Database error:", dbError);
    return {
      status: 500,
      body: {
        error: "Failed to save domain in database",
        details: dbError
      }
    };
  }

  return {
    status: 200,
    body: {
      success: true,
      hostname,
      cloudflare_data: responseData.result
    }
  };
}
