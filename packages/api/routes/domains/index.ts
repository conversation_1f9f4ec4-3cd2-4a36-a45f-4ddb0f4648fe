import { Hono } from "hono";
import type { Env } from "../../types";
import { handleCreateDomain } from "./handleCreateDomain";
import { handleGetDomainVerificationStatus } from "./handleGetDomainVerificationStatus";
import { handleListDomains } from "./handleListDomains";
import { handleListTeamDomains } from "./handleListTeamDomains";

const app = new Hono<{ Bindings: Env }>();

app.post("/", handleCreateDomain);
app.get("/website/:websiteId", handleListDomains);
app.get("/team/:teamId", handleListTeamDomains);
app.get("/:domainId/verification-status", handleGetDomainVerificationStatus);

export default app;
