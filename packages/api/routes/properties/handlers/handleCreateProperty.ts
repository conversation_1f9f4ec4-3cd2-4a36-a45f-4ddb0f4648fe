import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse, verifyAuthentication } from "@/api/utils/auth";
import { verifyTeamAccess } from "@/api/utils/team-access";
import type { Context } from "hono";
import type { CreatePropertyPayload } from "../types";

// POST /properties
export async function handleCreateProperty(c: Context<{ Bindings: Env }>) {
  try {
    // Verify authentication
    const auth = await verifyAuthentication(c.req.raw, c.env);
    if (!auth.isAuthenticated) {
      return createUnauthorizedResponse(auth.error || "Authentication required");
    }

    // Get JWT token from request for user-specific operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : authHeader || "";

    // Parse request body
    const payload: CreatePropertyPayload = await c.req.json();
    const { property, prices, property_amenities, building_amenities } = payload;

    // Validate required fields
    if (!property.title) {
      return c.json({ error: "Property title is required" }, 400);
    }

    if (!property.team_account_id) {
      return c.json({ error: "Team account ID is required" }, 400);
    }

    if (!property.purpose) {
      return c.json({ error: "Property purpose is required" }, 400);
    }

    if (!property.type) {
      return c.json({ error: "Property type is required" }, 400);
    }

    // Verify team access
    const teamAccess = await verifyTeamAccess(property.team_account_id, c.env, jwt);
    if (!teamAccess.success) {
      return teamAccess.error || c.json({ error: "Access denied: You are not a member of this team" }, 403);
    }

    // Initialize Supabase client
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // Prepare property data without address fields
    const propertyData = {
      title: property.title,
      description: property.description,
      team_account_id: property.team_account_id,
      purpose: property.purpose,
      type: property.type,
      built_area: property.built_area,
      total_area: property.total_area,
      floors_count: property.floors_count,
      floor: property.floor,
      rooms_count: property.rooms_count,
      bathrooms_count: property.bathrooms_count,
      parking_spots_count: property.parking_spots_count,
      suites_count: property.suites_count,
      pets_allowed: property.pets_allowed,
      available_for_rent: property.available_for_rent,
      available_for_sale: property.available_for_sale,
      available_for_bnb: property.available_for_bnb,
      furnished: property.furnished,
      address_id: property.address_id,
      address_complement: property.address_complement,
    };

    // Create the property record
    const { data: newProperty, error: propertyError } = await supabaseClient
      .from("properties")
      .insert(propertyData)
      .select()
      .single();

    if (propertyError) {
      console.error("Error creating property:", propertyError);
      return c.json({ error: propertyError.message }, 400);
    }

    // Create property prices
    const { error: pricesError } = await supabaseClient.from("properties_prices").insert({
      property_id: newProperty.id,
      ...prices,
    });

    if (pricesError) {
      // If prices creation fails, we should delete the property
      await supabaseClient.from("properties").delete().eq("id", newProperty.id);
      console.error("Error creating property prices:", pricesError);
      return c.json({ error: pricesError.message }, 400);
    }

    // Update property amenities if provided
    if (property_amenities) {
      // First check if property amenities record already exists
      const { data: propertyAmenitiesData } = await supabaseClient
        .from("property_amenities")
        .select("id")
        .eq("property_id", newProperty.id)
        .single();

      if (propertyAmenitiesData) {
        // Update existing record
        const { error: propertyAmenitiesError } = await supabaseClient
          .from("property_amenities")
          .update(property_amenities)
          .eq("id", propertyAmenitiesData.id);

        if (propertyAmenitiesError) {
          console.error("Error updating property amenities:", propertyAmenitiesError);
        }
      } else {
        // Insert new record
        const { error: propertyAmenitiesError } = await supabaseClient
          .from("property_amenities")
          .insert({
            property_id: newProperty.id,
            ...property_amenities,
          });

        if (propertyAmenitiesError) {
          console.error("Error creating property amenities:", propertyAmenitiesError);
        }
      }
    }

    // Update building amenities if provided
    if (building_amenities) {
      // First check if building amenities record already exists
      const { data: buildingAmenitiesData } = await supabaseClient
        .from("building_amenities")
        .select("id")
        .eq("property_id", newProperty.id)
        .single();

      if (buildingAmenitiesData) {
        // Update existing record
        const { error: buildingAmenitiesError } = await supabaseClient
          .from("building_amenities")
          .update(building_amenities)
          .eq("id", buildingAmenitiesData.id);

        if (buildingAmenitiesError) {
          console.error("Error updating building amenities:", buildingAmenitiesError);
        }
      } else {
        // Insert new record
        const { error: buildingAmenitiesError } = await supabaseClient
          .from("building_amenities")
          .insert({
            property_id: newProperty.id,
            ...building_amenities,
          });

        if (buildingAmenitiesError) {
          console.error("Error creating building amenities:", buildingAmenitiesError);
        }
      }
    }

    // Return the created property
    return c.json({ property: newProperty }, 201);
  } catch (error) {
    console.error("Error in handleCreateProperty:", error);
    return c.json({ error: error instanceof Error ? error.message : "Unknown error" }, 500);
  }
}
