import { Hono } from "hono/tiny";
import type { Env } from "../../types";
import { createUnauthorizedResponse, verifyAuthentication } from "../../utils";
import { handleCreateProperty } from "./handlers";

// Create Hono router with binding type for environment variables
const propertiesRouter = new Hono<{
  Bindings: Env;
  Variables: { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };
}>();

// Middleware: Authentication Check for all routes in this router
propertiesRouter.use(async (c, next) => {
  const env = c.env;

  // Pass the raw request directly to verifyAuthentication
  const auth = await verifyAuthentication(c.req.raw, env);
  if (!auth.isAuthenticated) {
    // Return the unauthorized response directly
    return createUnauthorizedResponse(auth.error || "Authentication required");
  }

  // Store auth data in the context variables for subsequent handlers/middleware
  c.set("auth", auth);

  // Proceed to the next middleware or route handler
  return await next();
});

// Define routes
propertiesRouter.post("/", handleCreateProperty);

export default propertiesRouter;
