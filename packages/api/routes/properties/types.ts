// Types for the properties API

export interface CreatePropertyPayload {
  property: {
    title: string;
    description?: string;
    team_account_id: string;
    purpose: "residential" | "commercial" | "mixed";
    type: string;
    built_area?: number;
    total_area?: number;
    floors_count?: number;
    floor?: number;
    rooms_count?: number;
    bathrooms_count?: number;
    parking_spots_count?: number;
    suites_count?: number;
    pets_allowed: boolean;
    available_for_rent: boolean;
    available_for_sale: boolean;
    available_for_bnb: boolean;
    furnished: boolean;
    address_id: string;
    address_complement?: string;
    google_maps_place_id?: string;
    coordinates?: { lat: number; lng: number } | null;
  };
  prices: {
    sale_price?: number;
    rent_price?: number;
    bnb_price?: number;
    condominium_monthly_tax?: number;
    iptu_monthly_tax?: number;
    insurance_monthly_tax?: number;
    other_monthly_tax?: number;
  };
  property_amenities?: Record<string, boolean>;
  building_amenities?: Record<string, boolean>;
}

export interface PropertyResponse {
  property: {
    id: string;
    title: string;
    description?: string;
    team_account_id: string;
    purpose: string;
    type: string;
    built_area?: number;
    total_area?: number;
    floors_count?: number;
    floor?: number;
    rooms_count?: number;
    bathrooms_count?: number;
    parking_spots_count?: number;
    suites_count?: number;
    pets_allowed: boolean;
    available_for_rent: boolean;
    available_for_sale: boolean;
    available_for_bnb: boolean;
    furnished: boolean;
    address_id: string;
    address_complement?: string;
    created_at: string;
    updated_at: string;
  };
}
