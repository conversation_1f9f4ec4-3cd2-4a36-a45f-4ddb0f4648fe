import type { SupabaseClient } from "@supabase/supabase-js";
import type {
  Db<PERSON>ddress,
  <PERSON>bCity,
  DbNeighborhood,
  DbStreet,
  ExtractedAddressComponents,
  GooglePlaceDetails,
} from "../types";

export function extractPostalCode(googleData: GooglePlaceDetails): string | null {
  if (!googleData.addressComponents) return null;
  const postalCode = googleData.addressComponents.find((component) => component.types.includes("postal_code"));
  return postalCode ? postalCode.longText : null;
}

export function extractCountry(googleData: GooglePlaceDetails): string | null {
  if (!googleData.addressComponents) return null;
  const country = googleData.addressComponents.find((component) => component.types.includes("country"));
  return country ? country.shortText : null; // Use shortText for country code (e.g., BR)
}

export function extractState(googleData: GooglePlaceDetails): string | null {
  if (!googleData.addressComponents) return null;
  const state = googleData.addressComponents.find((component) =>
    component.types.includes("administrative_area_level_1"),
  );
  return state ? state.shortText : null; // Use shortText for state abbreviation (e.g., SP)
}

export function extractAddressComponents(googleData: GooglePlaceDetails, placeId: string): ExtractedAddressComponents {
  const components: ExtractedAddressComponents = {
    address_street: null,
    address_number: null,
    address_neighborhood: null,
    address_city: null,
    address_state: googleData.addressComponents ? extractState(googleData) : null,
    address_country: googleData.addressComponents ? extractCountry(googleData) : null,
    address_postcode: googleData.addressComponents ? extractPostalCode(googleData) : null,
    formatted_address: googleData.formattedAddress || null,
    google_maps_place_id: placeId,
    coordinates: undefined,
  };

  if (!googleData.addressComponents) return components; // Return defaults if no components

  const route = googleData.addressComponents.find((c) => c.types.includes("route"));
  if (route) components.address_street = route.longText;

  const streetNumber = googleData.addressComponents.find((c) => c.types.includes("street_number"));
  if (streetNumber) components.address_number = streetNumber.longText;

  const neighborhood = googleData.addressComponents.find(
    (c) => c.types.includes("sublocality_level_1") || c.types.includes("sublocality"),
  );
  if (neighborhood) components.address_neighborhood = neighborhood.longText;

  const city =
    googleData.addressComponents.find((c) => c.types.includes("locality")) ||
    googleData.addressComponents.find((c) => c.types.includes("administrative_area_level_2"));
  if (city) components.address_city = city.longText;

  if (googleData.location) {
    components.coordinates = {
      lat: googleData.location.latitude,
      lng: googleData.location.longitude,
    };
  }

  return components;
}

export async function processAddress(
  supabaseClient: SupabaseClient,
  addressComponents: ExtractedAddressComponents,
): Promise<DbAddress | null> {
  try {
    // Validate required fields for DB insertion
    if (!addressComponents.address_city || !addressComponents.address_state) {
      console.error("processAddress: Missing required city or state:", addressComponents);
      return null;
    }

    const neighborhoodName = addressComponents.address_neighborhood || "Unknown";
    const streetName = addressComponents.address_street || "Unknown";

    // 1. Get or create city
    // Specify the type expected from the database (DbCity) and select only 'id' initially
    const cityResult = await getOrCreate<DbCity>(
      supabaseClient,
      "cities",
      {
        // matchCriteria
        name: addressComponents.address_city, // Non-null asserted based on earlier check
        state: addressComponents.address_state, // Non-null asserted based on earlier check
      },
      null, // insertData (defaults to matchCriteria)
      "id", // selectColumns
    );
    if (cityResult.error || !cityResult.data) {
      console.error("Failed to get/create city:", cityResult.error);
      return null;
    }
    const cityId = cityResult.data.id; // Safely access id

    // 2. Get or create neighborhood
    const neighborhoodResult = await getOrCreate<DbNeighborhood>(
      supabaseClient,
      "neighborhoods",
      {
        // matchCriteria
        city_id: cityId, // Use the retrieved cityId
        name: neighborhoodName,
      },
      null, // insertData
      "id", // selectColumns
    );
    if (neighborhoodResult.error || !neighborhoodResult.data) {
      console.error("Failed to get/create neighborhood:", neighborhoodResult.error);
      return null;
    }
    const neighborhoodId = neighborhoodResult.data.id; // Safely access id

    // 3. Get or create street
    const streetResult = await getOrCreate<DbStreet>(
      supabaseClient,
      "streets",
      {
        // matchCriteria
        city_id: cityId, // Use the retrieved cityId
        neighborhood_id: neighborhoodId, // Use the retrieved neighborhoodId
        name: streetName,
      },
      null, // insertData
      "id", // selectColumns
    );
    if (streetResult.error || !streetResult.data) {
      console.error("Failed to get/create street:", streetResult.error);
      return null;
    }
    const streetId = streetResult.data.id; // Safely access id

    // 4. Create the final address record (use the retrieved IDs)
    // Note: The initial check in handleAddressDetails already covers this,
    // but we double-check here before insertion to avoid race conditions if possible.
    // However, a simple insert might be sufficient if the placeId constraint is reliable.

    const geometryPoint = addressComponents.coordinates
      ? `POINT(${addressComponents.coordinates.lng} ${addressComponents.coordinates.lat})`
      : null;

    const addressDataToInsert = {
      city_id: cityId, // Use the correct ID variable
      street_id: streetId, // Use the correct ID variable
      neighborhood_id: neighborhoodId, // Use the correct ID variable
      street_number: addressComponents.address_number || "", // Use empty string if null
      coordinates: geometryPoint,
      postcode: addressComponents.address_postcode || "", // Use empty string if null
      state: addressComponents.address_state, // Already validated non-null
      country: addressComponents.address_country || "", // Use empty string if null
      google_maps_place_id: addressComponents.google_maps_place_id,
      formatted_address: addressComponents.formatted_address,
      // has_coordinates: !!geometryPoint // This can be derived or set by DB trigger
    };

    const { data: newAddress, error: addressError } = await supabaseClient
      .from("addresses")
      .insert(addressDataToInsert)
      .select(`
                *,
                city:cities(*),
                neighborhood:neighborhoods(*),
                street:streets(*)
            `)
      .single<DbAddress>(); // Use generic type

    if (addressError) {
      // Handle potential unique constraint violation if placeId already exists
      // (though the initial check should prevent this most times)
      if (addressError.code === "23505") {
        // Postgres unique violation code
        console.warn(
          `Address with placeId ${addressComponents.google_maps_place_id} likely already exists. Attempting to fetch.`,
        );
        // Try fetching the existing one again
        const { data: existingAddress, error: fetchError } = await supabaseClient
          .from("addresses")
          .select("*, city:cities(*), neighborhood:neighborhoods(*), street:streets(*)")
          .eq("google_maps_place_id", addressComponents.google_maps_place_id)
          .single<DbAddress>();
        if (fetchError || !existingAddress) {
          console.error("Failed to fetch existing address after insert conflict:", fetchError);
          return null;
        }
        return existingAddress;
      }
      console.error("Failed to insert address:", addressError);
      return null; // Return null on other insertion errors
    }

    console.log("Successfully created new address:", newAddress?.id);
    return newAddress; // newAddress can be null if insert failed unexpectedly
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : "Unknown error";
    const stack = error instanceof Error ? error.stack : undefined;
    console.error("Error processing address:", message);
    console.error("Stack trace:", stack);
    return null;
  }
}

// Generic getOrCreate function for Supabase
export async function getOrCreate<T>(
  supabaseClient: SupabaseClient,
  tableName: string,
  matchCriteria: Partial<T>, // Fields to match for existence
  insertData: Partial<T> | null = null, // Data to insert if not found (defaults to matchCriteria)
  selectColumns = "*", // Columns to select
): Promise<{ data: T | null; error: unknown | null }> {
  const dataToInsert = insertData || matchCriteria;

  // 1. Try to find existing record
  let query = supabaseClient.from(tableName).select(selectColumns);
  for (const key in matchCriteria) {
    query = query.eq(key as string, matchCriteria[key]);
  }
  const { data: existing, error: fetchError } = await query.maybeSingle<T>();

  if (fetchError) {
    console.error(`Error fetching from ${tableName}:`, fetchError);
    return { data: null, error: fetchError };
  }

  if (existing) {
    return { data: existing, error: null };
  }

  // 2. Create if not found
  console.log(`Creating new entry in ${tableName}:`, dataToInsert);
  const { data: created, error: insertError } = await supabaseClient
    .from(tableName)
    .insert(dataToInsert)
    .select(selectColumns)
    .single<T>();

  if (insertError) {
    console.error(`Error inserting into ${tableName}:`, insertError);
    // Handle potential race condition where another request created it between fetch and insert
    // Check if insertError is an object with a 'code' property before accessing it
    if (insertError && typeof insertError === "object" && "code" in insertError && insertError.code === "23505") {
      // Postgres unique violation code
      // Unique constraint violation
      console.warn(`Race condition likely for ${tableName}, re-fetching...`);
      let retryQuery = supabaseClient.from(tableName).select(selectColumns);
      for (const key in matchCriteria) {
        retryQuery = retryQuery.eq(key as string, matchCriteria[key]);
      }
      const { data: retryData, error: retryError } = await retryQuery.single<T>();
      if (retryError) {
        console.error(`Error re-fetching from ${tableName} after race condition:`, retryError);
        return { data: null, error: retryError };
      }
      return { data: retryData, error: null }; // Return the record created by the other request
    }
    return { data: null, error: insertError };
  }

  return { data: created, error: null };
}
