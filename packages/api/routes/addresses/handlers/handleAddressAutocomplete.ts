import type { Env } from "@/api/types";
import type { Context } from "hono";

// Define the expected Variables structure for handlers that might need it (even if unused)
type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

interface AutocompleteRequestBody {
  query?: string;
}

// --- Address Autocomplete Handler ---
export async function handleAddressAutocomplete(
  c: Context<{ Bindings: Env; Variables: HandlerVariables }>,
): Promise<Response> {
  const env = c.env; // Extract env from context
  const req = c.req.raw; // Extract raw request from context
  try {
    // Ensure body exists and is valid JSON
    let body: AutocompleteRequestBody;
    try {
      body = await req.json();
    } catch (e) {
      return new Response(JSON.stringify({ error: "Invalid JSON body" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    const { query } = body;

    if (!query) {
      return new Response(JSON.stringify({ error: "Query parameter is required" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    const requestBody = {
      input: query,
      languageCode: "pt-BR",
      regionCode: "BR",
    };

    const response = await fetch("https://places.googleapis.com/v1/places:autocomplete", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Goog-Api-Key": env.GOOGLE_API_KEY as string, // Assert as string (middleware checks)
        "X-Goog-FieldMask": "*", // Consider specifying only needed fields
      },
      body: JSON.stringify(requestBody),
    });

    const data = await response.json();

    // Forward Google's status code if it's an error
    const status = response.ok ? 200 : response.status;

    return new Response(JSON.stringify(data), {
      status: status,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : "Unknown error";
    console.error("handleAddressAutocomplete error:", error);
    return new Response(JSON.stringify({ error: "Internal server error", details: message }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
