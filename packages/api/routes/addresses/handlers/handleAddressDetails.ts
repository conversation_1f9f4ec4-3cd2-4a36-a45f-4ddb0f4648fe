import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import type { Context } from "hono";
import type { DbAddress, DetailsRequestBody, GooglePlaceDetails } from "../types";
import { extractAddressComponents, processAddress } from "./utils";

// Define the expected Variables structure for handlers that might need it (even if unused)
type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

// --- Address Details Handler ---
export async function handleAddressDetails(
  c: Context<{ Bindings: Env; Variables: HandlerVariables }>,
): Promise<Response> {
  const env = c.env; // Extract env from context
  const req = c.req.raw; // Extract raw request from context

  // Get JWT token from request for user-specific operations
  const authHeader = c.req.header("Authorization");
  const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

  const { client: supabase, error: supabaseError } = getSupabaseClient(env, jwt);
  if (supabaseError) return supabaseError;
  if (!supabase) {
    // Should be handled by supabaseError, but belts and suspenders
    return new Response(JSON.stringify({ error: "Database connection failed" }), { status: 500 });
  }

  try {
    let body: DetailsRequestBody;
    try {
      body = await req.json();
    } catch (e) {
      return new Response(JSON.stringify({ error: "Invalid JSON body" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }
    const { placeId } = body;

    if (!placeId) {
      return new Response(JSON.stringify({ error: "placeId parameter is required" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // 1. Check cache in DB
    console.log("Checking database for cached address with placeId:", placeId);
    try {
      const { data: existingAddress, error: dbError } = await supabase
        .from("addresses")
        .select(`
                    *,
                    city:cities(*),
                    neighborhood:neighborhoods(*),
                    street:streets(*)
                `)
        .eq("google_maps_place_id", placeId)
        .maybeSingle<DbAddress>(); // Use generic type

      if (dbError) {
        console.error("Database query error:", dbError);
        // Don't fail, proceed to Google API
      } else if (existingAddress) {
        console.log("Found cached address data for placeId:", placeId);
        return new Response(JSON.stringify(existingAddress), {
          headers: { "Content-Type": "application/json" },
        });
      }
    } catch (cacheCheckError: unknown) {
      const message = cacheCheckError instanceof Error ? cacheCheckError.message : "Unknown error during cache check";
      console.error("Exception during cache check query:", message, cacheCheckError);
      // Proceed to Google API
    }

    // 2. Fetch from Google Places API if not in cache
    console.log("Fetching address data from Google Places API for placeId:", placeId);
    const googleApiUrl = `https://places.googleapis.com/v1/places/${placeId}?languageCode=pt-BR&regionCode=BR`;
    const googleResponse = await fetch(googleApiUrl, {
      method: "GET",
      headers: {
        "X-Goog-Api-Key": env.GOOGLE_API_KEY as string, // Assert as string (middleware checks)
        "X-Goog-FieldMask": "*", // Specify fields: id,formattedAddress,addressComponents,location
      },
    });

    const googleData: GooglePlaceDetails = await googleResponse.json();

    if (!googleResponse.ok) {
      console.error("Google Places API error:", googleResponse.status, googleData);
      return new Response(
        JSON.stringify({
          error: "Google Places API error",
          details: googleData,
        }),
        {
          status: googleResponse.status,
          headers: { "Content-Type": "application/json" },
        },
      );
    }

    // 3. Process and potentially save to DB
    const addressComponents = extractAddressComponents(googleData, placeId);

    // Validate essential components before processing
    if (!addressComponents.address_city || !addressComponents.address_state) {
      console.error("Google API response missing essential city/state components for placeId:", placeId, googleData);
      // Return Google data directly as processing isn't possible
      return new Response(JSON.stringify(googleData), {
        headers: { "Content-Type": "application/json" },
      });
    }

    const processedAddress = await processAddress(supabase, addressComponents);

    if (!processedAddress) {
      console.error("Failed to process address with hierarchical structure for placeId:", placeId);
      // Return Google data as a fallback if processing failed
      return new Response(JSON.stringify(googleData), {
        headers: { "Content-Type": "application/json" },
      });
    }

    // Return the processed (and potentially newly cached) address
    return new Response(JSON.stringify(processedAddress), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: unknown) {
    const message = error instanceof Error ? error.message : "Unknown error";
    console.error("handleAddressDetails error:", error);
    return new Response(JSON.stringify({ error: "Internal server error", details: message }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
