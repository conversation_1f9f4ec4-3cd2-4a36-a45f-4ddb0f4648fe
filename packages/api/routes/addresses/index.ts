import type { Env } from "@/api/types"; // Import Env type for the fetch handler
import { createUnauthorizedResponse, verifyAuthentication } from "@/api/utils"; // Import from new location
import { Hono } from "hono/tiny";
import { handleAddressAutocomplete, handleAddressDetails } from "./handlers"; // Removed HonoHandlerContext import

// Create Hono router with binding type for environment variables
// Define Variables type directly in Hono generic
const addressesRouter = new Hono<{
  Bindings: Env;
  Variables: { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };
}>();

// Middleware: Authentication Check for all routes in this router
addressesRouter.use(async (c, next) => {
  const env = c.env;

  // Pass the raw request directly to verifyAuthentication
  const auth = await verifyAuthentication(c.req.raw, env);
  if (!auth.isAuthenticated) {
    // Return the unauthorized response directly
    return createUnauthorizedResponse(auth.error || "Authentication required");
  }

  // Store auth data in the context variables for subsequent handlers/middleware
  c.set("auth", auth);

  // Proceed to the next middleware or route handler
  return await next();
});

// Define routes relative to the mount point (e.g., /addresses)
// POST /addresses/autocomplete
addressesRouter.post("/autocomplete", async (c) => {
  // Call the handler directly with the Hono context
  return await handleAddressAutocomplete(c);
});

// POST /addresses/details
addressesRouter.post("/details", async (c) => {
  // Call the handler directly with the Hono context
  return await handleAddressDetails(c);
});

// Export the configured router for addresses
export default addressesRouter;
