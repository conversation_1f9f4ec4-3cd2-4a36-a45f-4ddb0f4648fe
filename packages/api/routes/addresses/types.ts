export interface DetailsRequestBody {
  placeId?: string;
}

// Define interfaces for Google Places data structures (simplified)
export interface GoogleLocation {
  latitude: number;
  longitude: number;
}

export interface GoogleAddressComponent {
  longText: string;
  shortText: string;
  types: string[];
  languageCode: string;
}

export interface GooglePlaceDetails {
  id: string; // placeId is usually part of the response or context
  formattedAddress?: string;
  addressComponents?: GoogleAddressComponent[];
  location?: GoogleLocation;
  // Add other fields as needed
}

// Define interfaces for your database tables (adjust based on actual schema)
export interface DbCity {
  id: string; // Assuming UUID
  name: string;
  state: string;
  // other fields...
}

export interface DbNeighborhood {
  id: string;
  city_id: string;
  name: string;
  // other fields...
}

export interface DbStreet {
  id: string;
  city_id: string;
  neighborhood_id: string;
  name: string;
  // other fields...
}

export interface DbAddress {
  id: string;
  city_id: string;
  street_id: string;
  neighborhood_id: string;
  street_number?: string;
  postcode?: string;
  state: string;
  country?: string;
  google_maps_place_id?: string;
  formatted_address?: string;
  coordinates?: string; // Assuming PostGIS geometry string like 'POINT(lng lat)'
  has_coordinates?: boolean; // Derived or stored
  // Joined fields
  city?: DbCity;
  neighborhood?: DbNeighborhood;
  street?: DbStreet;
}

// Define the structure for extracted components
export interface ExtractedAddressComponents {
  address_street: string | null;
  address_number: string | null;
  address_neighborhood: string | null;
  address_city: string | null;
  address_state: string | null;
  address_country: string | null;
  address_postcode: string | null;
  formatted_address: string | null;
  google_maps_place_id: string; // placeId is required
  coordinates?: { lat: number; lng: number };
}
