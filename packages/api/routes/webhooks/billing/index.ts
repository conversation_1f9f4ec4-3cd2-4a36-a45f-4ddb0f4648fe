import { Hono } from "hono/tiny";
import type { Env } from "@/api/types";
import { handleWebhook } from "./handlers";

// Create Hono router with binding type for environment variables
const billingWebhooksRouter = new Hono<{ Bindings: Env }>();

// No authentication middleware for webhooks - they use their own signature verification

// Define routes
billingWebhooksRouter.post("/", handleWebhook);

export default billingWebhooksRouter;
