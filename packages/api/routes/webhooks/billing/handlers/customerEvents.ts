import type { Env } from "@/api/types";
import { initSupabase } from "@/api/utils";
import { getErrorMessage } from "../utils";
import type Stripe from "stripe";

export const handleCustomerEvents = async (
  event: Stripe.Event,
  stripeClient: Stripe,
  env: Env
): Promise<void> => {
  try {
    const customer = event.data.object as Stripe.Customer;
    console.log(`Processing ${event.type}. Customer:`, customer.id);

    // Initialize Supabase client with service role
    const supabase = initSupabase(env, env.SUPABASE_SERVICE_ROLE_KEY);

    console.log("Updating billing_customers table...");
    const { error: customerError } = await supabase.rpc("service_role_upsert_customer_subscription", {
      account_id: customer.metadata?.imoblr_team_id,
      customer: {
        id: customer.id,
        billing_email: customer.email || customer.metadata?.email,
        provider: "stripe",
      },
    });

    if (customerError) {
      console.error("Error updating billing_customers:", customerError.message);
      throw new Error(`Failed to update billing_customers: ${customerError.message}`);
    }
    console.log("Successfully updated billing_customers table");
  } catch (error) {
    console.error("Failed to update billing_customers:", getErrorMessage(error));
    throw error;
  }
};
