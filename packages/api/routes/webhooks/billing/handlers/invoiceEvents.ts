import type { Env } from "@/api/types";
import { getErrorMessage } from "../utils";
import type <PERSON><PERSON> from "stripe";

export const handleInvoiceEvents = async (
  event: Stripe.Event,
  stripeClient: Stripe,
  env: Env
): Promise<void> => {
  try {
    const invoice = event.data.object as Stripe.Invoice;
    // Only log detailed info for important invoice events
    const isImportantInvoiceEvent = invoice.status === 'open' ||
                                   event.type === 'invoice.payment_failed';

    if (isImportantInvoiceEvent) {
      console.log(`Processing ${event.type}: id=${invoice.id}, status=${invoice.status}, amount=${invoice.amount_due}`);
    }

    // Check if this is an invoice for a subscription
    if (!invoice.subscription) {
      if (isImportantInvoiceEvent) {
        console.log("Invoice not associated with a subscription");
      }
      return;
    }

    // Get the subscription
    const subscription = await stripeClient.subscriptions.retrieve(invoice.subscription as string);
    const customer = await stripeClient.customers.retrieve(subscription.customer as string);
    const accountId = subscription.metadata?.imoblr_team_id || 
      (customer as Stripe.Customer).metadata?.imoblr_team_id;

    if (!accountId) {
      console.error("No account ID found in subscription or customer metadata");
      return;
    }

    if (isImportantInvoiceEvent) {
      console.log(`${event.type} for account: ${accountId}, subscription status: ${subscription.status}`);
    }

    // The subscription status update will be handled by customer.subscription.updated
    // We don't need to do anything specific here
  } catch (error) {
    console.error(`Error processing ${event.type} event:`, getErrorMessage(error));
    throw error;
  }
};
