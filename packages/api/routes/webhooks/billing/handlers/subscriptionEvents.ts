import type { Env } from "@/api/types";
import { initSupabase } from "@/api/utils";
import { getErrorMessage } from "../utils";
import type Stripe from "stripe";

export const handleSubscriptionEvents = async (
  event: Stripe.Event,
  stripeClient: Stripe,
  env: Env
): Promise<void> => {
  try {
    const subscription = event.data.object as Stripe.Subscription;
    // Only log detailed info for important subscription status changes
    const isImportantStatusChange = event.type === 'customer.subscription.resumed' ||
                                   event.type === 'customer.subscription.paused' ||
                                   event.type === 'customer.subscription.created' ||
                                   event.type === 'customer.subscription.deleted' ||
                                   (event.type === 'customer.subscription.updated' &&
                                    event.data.previous_attributes?.status);

    if (isImportantStatusChange) {
      console.log(`Processing ${event.type} for subscription: ${subscription.id} (status: ${subscription.status})`);
      // Log only essential metadata fields
      const metadata = subscription.metadata || {};
      console.log(`Team ID: ${metadata.imoblr_team_id || 'N/A'}`);
    }

    // Initialize Supabase client with service role
    const supabase = initSupabase(env, env.SUPABASE_SERVICE_ROLE_KEY);

    // Get customer details first
    const customer = await stripeClient.customers.retrieve(subscription.customer as string);

    const accountId = subscription.metadata?.imoblr_team_id || 
      (customer as Stripe.Customer).metadata?.imoblr_team_id;

    if (!accountId) {
      throw new Error("No account_id found in subscription or customer metadata");
    }

    // Ensure customer exists in billing_customers
    if (isImportantStatusChange) {
      console.log("Ensuring customer exists in billing_customers...");
    }
    const { error: customerError } = await supabase.rpc("service_role_upsert_customer_subscription", {
      account_id: accountId,
      customer: {
        id: (customer as Stripe.Customer).id,
        billing_email: (customer as Stripe.Customer).email || (customer as Stripe.Customer).metadata?.email,
        provider: "stripe",
      },
    });

    if (customerError) {
      console.error("Error updating billing_customers:", customerError.message);
      throw new Error(`Failed to update billing_customers: ${customerError.message}`);
    }

    // Get price and product details
    const subscriptionItem = subscription.items.data[0];
    if (!subscriptionItem?.price?.id) {
      throw new Error("No price found in subscription");
    }

    // Only log for important status changes
    if (isImportantStatusChange) {
      console.log("Retrieving price details...");
    }
    const price = await stripeClient.prices.retrieve(subscriptionItem.price.id, {
      expand: ["product"],
    });
    const product = price.product as Stripe.Product;

    // Map Stripe status to basejump.subscription_status enum
    let status = subscription.status;
    switch (status) {
      case "active":
      case "trialing":
      case "past_due":
      case "unpaid":
      case "canceled":
      case "incomplete":
      case "incomplete_expired":
      case "paused":
        // These statuses match our enum directly
        break;
      default:
        // For any other status, mark as incomplete
        status = "incomplete";
        break;
    }

    // If this is a resumed subscription, add a note in metadata
    const baseMetadata = {
      account_id: accountId,
      price_id: subscriptionItem.price.id,
      product_name: product.name,
      price_type: price.type,
      billing_scheme: price.billing_scheme,
      currency: price.currency,
      unit_amount: price.unit_amount,
      interval: price.recurring?.interval,
      interval_count: price.recurring?.interval_count,
      is_yearly: subscription.metadata?.is_yearly === "true",
    };

    // Create a variable for the final metadata
    const subscriptionMetadata: Record<string, string | number | boolean> = { ...baseMetadata };

    // If this was a paused subscription that is now active, mark it
    if (event.type === "customer.subscription.updated" &&
        event.data.previous_attributes?.status === "paused" &&
        subscription.status === "active") {
      console.log("Subscription was resumed from paused state");
      // Add the previously_paused property to the metadata
      subscriptionMetadata.previously_paused = "true";

      // Update the subscription in Stripe to include this metadata
      await stripeClient.subscriptions.update(subscription.id, {
        metadata: {
          ...subscription.metadata,
          previously_paused: "true"
        }
      });
    }

    // Update billing_subscriptions table
    if (isImportantStatusChange) {
      console.log("Updating billing_subscriptions table...");
    }
    const { error: subscriptionError } = await supabase.rpc("service_role_upsert_customer_subscription", {
      account_id: accountId,
      subscription: {
        id: subscription.id,
        customer_id: subscription.customer,
        status: status,
        metadata: subscriptionMetadata,
        price_id: subscriptionItem.price.id,
        plan_name: product.name,
        quantity: subscriptionItem.quantity || 1,
        cancel_at_period_end: subscription.cancel_at_period_end,
        created: new Date(subscription.created * 1000).toISOString(),
        current_period_start: subscription.current_period_start
          ? new Date(subscription.current_period_start * 1000).toISOString()
          : null,
        current_period_end: subscription.current_period_end
          ? new Date(subscription.current_period_end * 1000).toISOString()
          : null,
        trial_start: subscription.trial_start ? new Date(subscription.trial_start * 1000).toISOString() : null,
        trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000).toISOString() : null,
        provider: "stripe",
      },
    });

    if (subscriptionError) {
      console.error("Error updating billing_subscriptions:", subscriptionError.message);
      throw new Error(`Failed to update billing_subscriptions: ${subscriptionError.message}`);
    }
    if (isImportantStatusChange) {
      console.log("Successfully updated billing_subscriptions table");
    }
  } catch (error) {
    console.error("Failed to process subscription:", getErrorMessage(error));
    throw error;
  }
};
