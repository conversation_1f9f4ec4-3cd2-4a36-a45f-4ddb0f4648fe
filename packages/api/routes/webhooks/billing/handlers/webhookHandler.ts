import type { Env } from "@/api/types";
import type { Context } from "hono";
import type Stripe from "stripe";
import { getErrorMessage, getErrorStack, initStripeClient, isImportantEvent, logImportantEvent, verifyStripeWebhookSignature } from "../utils";

// Import all event handlers
import { handleSetupIntentSucceeded } from "./setupIntentSucceeded";
import { handleSubscriptionResumed } from "./subscriptionResumed";
import { handleInvoiceCreated } from "./invoiceCreated";
import { handleInvoiceEvents } from "./invoiceEvents";
import { handleCustomerEvents } from "./customerEvents";
import { handlePaymentIntentEvents } from "./paymentIntentEvents";
import { handleSubscriptionEvents } from "./subscriptionEvents";
import { handleMinorEvents } from "./minorEvents";

// Main webhook handler
export const handleWebhook = async (c: Context<{ Bindings: Env }>) => {
  try {
    const signature = c.req.header("stripe-signature");
    if (!signature) {
      return c.json({ error: "No signature found" }, 400);
    }

    const webhookSecret = c.env.STRIPE_WEBHOOK_SIGNING_SECRET;
    if (!webhookSecret) {
      return c.json({ error: "Missing STRIPE_WEBHOOK_SIGNING_SECRET" }, 500);
    }

    const stripeClient = initStripeClient(c.env);
    const rawBody = await c.req.text();
    
    // Verify the webhook signature
    const event = await verifyStripeWebhookSignature(
      rawBody,
      signature,
      webhookSecret,
      stripeClient
    );

    if (!event) {
      return c.json({ error: "Invalid webhook signature" }, 400);
    }

    // Log important events with details, use minimal logging for routine events
    if (isImportantEvent(event.type)) {
      logImportantEvent(event);
    } else {
      // For routine events, just log the type without details
      console.log(`Received webhook: ${event.type}`);
    }

    try {
      // Route the event to the appropriate handler
      switch (event.type) {
        case "setup_intent.succeeded":
          await handleSetupIntentSucceeded(event, stripeClient, c.env);
          break;

        case "customer.subscription.resumed":
          await handleSubscriptionResumed(event, stripeClient, c.env);
          break;

        case "invoice.created":
          await handleInvoiceCreated(event, stripeClient, c.env);
          break;

        case "invoice.paid":
        case "invoice.updated":
        case "invoice.finalized":
        case "invoice.payment_succeeded":
          await handleInvoiceEvents(event, stripeClient, c.env);
          break;

        case "customer.created":
        case "customer.updated":
          await handleCustomerEvents(event, stripeClient, c.env);
          break;

        case "payment_intent.succeeded":
        case "payment_intent.created":
          await handlePaymentIntentEvents(event, stripeClient, c.env);
          break;

        case "customer.subscription.created":
        case "customer.subscription.updated":
        case "customer.subscription.paused":
        case "customer.subscription.pending_update_applied":
          await handleSubscriptionEvents(event, stripeClient, c.env);
          break;

        case "setup_intent.created":
        case "setup_intent.canceled":
        case "payment_method.attached":
        case "charge.succeeded":
          await handleMinorEvents(event, stripeClient, c.env);
          break;

        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      return c.json({ received: true }, 200);
    } catch (error) {
      console.error(`Error handling ${event.type}:`, getErrorMessage(error));
      return c.json({
        error: `Error handling ${event.type}: ${getErrorMessage(error)}`,
        details: getErrorStack(error),
      }, 500);
    }
  } catch (error) {
    console.error("Error in webhook handler:", getErrorMessage(error));
    return c.json({
      error: `Webhook Error: ${getErrorMessage(error)}`,
      details: getErrorStack(error),
    }, 500);
  }
};
