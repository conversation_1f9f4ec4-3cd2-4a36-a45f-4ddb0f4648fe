import type { Env } from "@/api/types";
import { getErrorMessage } from "../utils";
import type <PERSON><PERSON> from "stripe";

export const handleInvoiceCreated = async (
  event: Stripe.Event,
  stripeClient: Stripe,
  env: Env
): Promise<void> => {
  try {
    const invoice = event.data.object as Stripe.Invoice;
    // Only log detailed info for important invoice events
    const isImportantInvoiceEvent = invoice.status === 'open';

    if (isImportantInvoiceEvent) {
      console.log(`Processing ${event.type}: id=${invoice.id}, status=${invoice.status}, amount=${invoice.amount_due}`);
    }

    // Check if this is an invoice for a subscription
    if (!invoice.subscription) {
      if (isImportantInvoiceEvent) {
        console.log("Invoice not associated with a subscription");
      }
      return;
    }

    // Get the subscription
    const subscription = await stripeClient.subscriptions.retrieve(invoice.subscription as string);

    // Check if this is an open invoice for a paused or recently resumed subscription
    // We need to pay it manually in either case
    if (invoice.status === 'open') {
      console.log(`Found open invoice: ${invoice.id} for subscription with status: ${subscription.status}`);

      // For paused subscriptions or subscriptions that were recently resumed, we need to pay the invoice manually
      if (subscription.status === 'paused' ||
          (subscription.status === 'active' && subscription.metadata?.previously_paused === 'true')) {
        console.log("This is for a paused/resumed subscription, handling payment manually");

        // Make sure we have a default payment method before trying to pay
        if (!subscription.default_payment_method) {
          console.log("No default payment method found for subscription");

          // Try to get the customer's default payment method
          const customer = await stripeClient.customers.retrieve(subscription.customer as string);
          if ((customer as Stripe.Customer).invoice_settings?.default_payment_method) {
            console.log("Using customer's default payment method");

            // Update the subscription with the customer's default payment method
            await stripeClient.subscriptions.update(subscription.id, {
              default_payment_method: (customer as Stripe.Customer).invoice_settings?.default_payment_method,
            });
          } else {
            console.log("No default payment method found for customer either");
            return;
          }
        }

        // Pay the invoice if it's still open
        console.log("Paying invoice:", invoice.id, "with amount:", invoice.amount_due);
        try {
          const paidInvoice = await stripeClient.invoices.pay(invoice.id);
          console.log("Invoice payment result:", paidInvoice.status);

          // If the subscription is still paused, resume it
          // Only attempt to resume if it's actually in paused status to avoid errors
          if (subscription.status === 'paused') {
            try {
              console.log("Subscription is still paused, resuming it");
              await stripeClient.subscriptions.resume(subscription.id, {
                billing_cycle_anchor: "now"
              });
              console.log("Subscription resumed after invoice payment");
            } catch (resumeError) {
              // If the error is because the subscription is already being resumed, that's okay
              if (resumeError && typeof resumeError === 'object' && 'message' in resumeError &&
                  typeof resumeError.message === 'string' &&
                  resumeError.message.includes('only resume a subscription if it is `paused`')) {
                console.log("Subscription is already being resumed or is no longer paused");
              } else {
                // For other errors, log them but don't throw
                console.error("Error resuming subscription:", getErrorMessage(resumeError));
              }
            }
          }
        } catch (payError) {
          console.error("Error paying invoice:", getErrorMessage(payError));
          // If payment fails, we might need to update the payment method
          // This will be handled by the client-side retry mechanism
        }
      }
    }
  } catch (error) {
    console.error("Error processing invoice created event:", getErrorMessage(error));
    throw error;
  }
};
