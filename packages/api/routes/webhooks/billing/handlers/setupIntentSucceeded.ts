import type { Env } from "@/api/types";
import { getErrorMessage } from "../utils";
import type <PERSON><PERSON> from "stripe";

export const handleSetupIntentSucceeded = async (
  event: Stripe.Event,
  stripeClient: Stripe,
  env: Env
): Promise<void> => {
  try {
    const setupIntent = event.data.object as Stripe.SetupIntent;
    
    // Only log detailed info for setup intents with subscription metadata
    const metadata = setupIntent.metadata || {};
    if (metadata.subscription_id) {
      console.log(`Processing setup intent for subscription: ${metadata.subscription_id}`);
    }

    // Check if this is a payment setup for subscription
    if (!setupIntent.metadata?.imoblr_team_id || !setupIntent.metadata?.price_id) {
      console.log("Setup intent doesn't have required metadata for subscription handling");
      return;
    }

    // Attach payment method to customer
    console.log("Attaching payment method to customer:", setupIntent.customer);
    await stripeClient.paymentMethods.attach(setupIntent.payment_method as string, {
      customer: setupIntent.customer as string,
    });

    // Set the payment method as the default for the customer
    console.log("Setting payment method as default for customer:", setupIntent.customer);
    await stripeClient.customers.update(setupIntent.customer as string, {
      invoice_settings: {
        default_payment_method: setupIntent.payment_method,
      },
    });

    // Find existing subscription for the account
    const { imoblr_team_id, price_id, quantity } = setupIntent.metadata;
    console.log("Looking for existing subscription for account:", imoblr_team_id);
    const existingSubscriptions = await stripeClient.subscriptions.search({
      query: `metadata['imoblr_team_id']:'${imoblr_team_id}'`,
    });

    if (existingSubscriptions.data.length === 0) {
      console.error("No subscription found for account");
      return;
    }

    const subscription = existingSubscriptions.data[0];
    console.log("Found subscription:", subscription.id);

    // Update subscription with new price and payment method
    // The actual resumption or trial ending will be handled by other webhook events
    console.log("Updating subscription with new payment method and price:", subscription.id);
    await stripeClient.subscriptions.update(subscription.id, {
      items: [
        {
          id: subscription.items.data[0].id,
          price: price_id,
          quantity: Number.parseInt(quantity || "1", 10),
        },
      ],
      default_payment_method: setupIntent.payment_method,
      payment_settings: {
        payment_method_types: ["card"],
        save_default_payment_method: "on_subscription",
      }
    });

    // If subscription is paused, resume it
    // The actual resumption process will be handled by customer.subscription.resumed event
    if (subscription.status === 'paused') {
      console.log("Resuming paused subscription:", subscription.id);
      await stripeClient.subscriptions.resume(subscription.id, {
        billing_cycle_anchor: "now"
      });
      console.log("Resume request sent for subscription:", subscription.id);
    } else if (subscription.status === 'trialing') {
      // End trial immediately for non-paused subscriptions
      console.log("Ending trial for subscription:", subscription.id);
      await stripeClient.subscriptions.update(subscription.id, {
        trial_end: "now"
      });
      console.log("Trial ended for subscription:", subscription.id);
    }
  } catch (error) {
    console.error("Error processing setup intent:", getErrorMessage(error));
    throw error;
  }
};
