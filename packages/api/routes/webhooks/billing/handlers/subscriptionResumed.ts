import type { Env } from "@/api/types";
import { getErrorMessage } from "../utils";
import type <PERSON><PERSON> from "stripe";

export const handleSubscriptionResumed = async (
  event: Stripe.Event,
  stripeClient: Stripe,
  env: Env
): Promise<void> => {
  try {
    const subscription = event.data.object as Stripe.Subscription;
    console.log("Processing subscription resumed event:", subscription.id);

    // Get customer details
    const customer = await stripeClient.customers.retrieve(subscription.customer as string);
    const accountId = subscription.metadata?.imoblr_team_id || 
      (customer as Stripe.Customer).metadata?.imoblr_team_id;

    if (!accountId) {
      console.error("No account ID found in subscription or customer metadata");
      return;
    }

    console.log("Subscription resumed for account:", accountId);

    // Update subscription status in database
    // This will be handled by the customer.subscription.updated event
    // We don't need to do anything specific here
  } catch (error) {
    console.error("Error processing subscription resumed event:", getErrorMessage(error));
    throw error;
  }
};
