import type { Env } from "@/api/types";
import { getErrorMessage } from "../utils";
import type <PERSON><PERSON> from "stripe";

export const handlePaymentIntentEvents = async (
  event: Stripe.Event,
  stripeClient: Stripe,
  env: Env
): Promise<void> => {
  try {
    const paymentIntent = event.data.object as Stripe.PaymentIntent;
    const customerId = paymentIntent.customer;
    const paymentMethodId = paymentIntent.payment_method;

    // Only log detailed info for important payment intent events
    const isImportantPaymentEvent = event.type === 'payment_intent.payment_failed' ||
                                  paymentIntent.status === 'requires_action';

    if (isImportantPaymentEvent) {
      console.log(`Processing ${event.type}. Payment Intent: id=${paymentIntent.id}, status=${paymentIntent.status}, amount=${paymentIntent.amount}`);
    }

    // For payment_intent.created, we don't need to do anything else
    if (event.type === "payment_intent.created") {
      return;
    }

    // For payment_intent.succeeded, we need to attach the payment method
    if (!customerId) {
      throw new Error("No customer ID found in payment intent");
    }

    if (!paymentMethodId) {
      throw new Error("No payment method ID found in payment intent");
    }

    // Attach the payment method to the customer if not already attached
    console.log("Attaching payment method to customer...");
    try {
      await stripeClient.paymentMethods.attach(paymentMethodId as string, {
        customer: customerId as string,
      });

      // Set it as the default payment method
      await stripeClient.customers.update(customerId as string, {
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });
      console.log("Payment method attached and set as default");
    } catch (error) {
      // Only ignore the error if the payment method is already attached
      const errorMessage = getErrorMessage(error);

      if (errorMessage.includes("already been attached")) {
        console.log("Payment method already attached to customer");
      } else {
        console.error("Error attaching payment method:", errorMessage);
        throw error;
      }
    }
  } catch (error) {
    console.error(`Error processing ${event.type} event:`, getErrorMessage(error));
    throw error;
  }
};
