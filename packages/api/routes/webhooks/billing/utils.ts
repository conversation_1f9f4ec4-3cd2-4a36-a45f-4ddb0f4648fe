import type { Env } from "@/api/types";
import { initSupabase } from "@/api/utils";
import Stripe from "stripe";

// Initialize Stripe client
export const initStripeClient = (env: Env): Stripe => {
  return new Stripe(env.STRIPE_API_KEY, {
    apiVersion: "2022-11-15", // Use a specific API version compatible with the installed Stripe package
  });
};

// Verify Stripe webhook signature
export const verifyStripeWebhookSignature = async (
  rawBody: string,
  signature: string,
  webhookSecret: string,
  stripeClient: Stripe
): Promise<Stripe.Event | null> => {
  try {
    // Use constructEventAsync for compatibility
    const event = await stripeClient.webhooks.constructEventAsync(
      rawBody,
      signature,
      webhookSecret
    );
    return event;
  } catch (err) {
    const errorMessage = err && typeof err === 'object' && 'message' in err
      ? String(err.message) : 'Unknown error';
    console.error("Webhook signature verification failed:", errorMessage);
    return null;
  }
};

// Helper to safely extract error message from any error object
export const getErrorMessage = (error: unknown): string => {
  return error && typeof error === 'object' && 'message' in error
    ? String(error.message) : 'Unknown error';
};

// Helper to safely extract error stack from any error object
export const getErrorStack = (error: unknown): string => {
  return error && typeof error === 'object' && 'stack' in error
    ? String(error.stack) : 'No stack trace available';
};

// Helper to log important events with details
export const logImportantEvent = (event: Stripe.Event): void => {
  const obj = event.data.object as any;
  console.log(`Processing important webhook event: ${event.type}`);
  console.log(
    `Event ID: ${obj.id}, Object type: ${obj.object}${obj.customer ? `, Customer: ${obj.customer}` : ''}${obj.subscription ? `, Subscription: ${obj.subscription}` : ''}`
  );
};

// Helper to check if an event is important (for detailed logging)
export const isImportantEvent = (eventType: string): boolean => {
  return [
    'customer.subscription.resumed',
    'customer.subscription.paused',
    'customer.subscription.deleted',
    'customer.subscription.created',
    'invoice.payment_failed',
    'payment_intent.payment_failed'
  ].includes(eventType);
};
