import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse, verifyAuthentication } from "@/api/utils/auth";
import type { Context } from "hono";

export async function handleListTeamWebsites(c: Context<{ Bindings: Env }>) {
  try {
    // Verify authentication
    const auth = await verifyAuthentication(c.req.raw, c.env);
    if (!auth.isAuthenticated) {
      return createUnauthorizedResponse(auth.error || "Authentication required");
    }

    // Get the team ID from the URL params
    const { teamId } = c.req.param();

    if (!teamId) {
      return c.json({ error: "Team ID is required" }, 400);
    }

    // Get JWT token from request for user-specific operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

    // Initialize Supabase client
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // We'll rely on RLS policies to filter websites based on team access
    // No need to explicitly check team access here

    // Query the websites with their themes
    // Use the specific relationship to avoid ambiguity
    const { data: websites, error: websitesError } = await supabaseClient
      .from('websites')
      .select('*, theme:website_themes!websites_theme_id_fkey(*)')
      .eq('team_account_id', teamId);

    if (websitesError) {
      console.error("Error fetching websites:", websitesError);
      return c.json({
        error: "Failed to fetch websites",
        details: websitesError.message
      }, 500);
    }

    // Add cache headers for better performance
    c.header('Cache-Control', 'public, max-age=30, stale-while-revalidate=300');

    return c.json({
      success: true,
      websites: websites || []
    });
  } catch (error) {
    console.error("Error listing team websites:", error);
    return c.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error occurred"
    }, 500);
  }
}
