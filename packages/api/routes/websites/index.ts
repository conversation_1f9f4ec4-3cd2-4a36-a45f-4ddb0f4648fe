import { Hono } from "hono";
import type { Env } from "../../types";
import { handleCreateWebsite } from "./handleCreateWebsite";
import { handleGetWebsite } from "./handleGetWebsite";
import { handleListTeamWebsites } from "./handleListTeamWebsites";

const app = new Hono<{ Bindings: Env }>();

app.post("/", handleCreateWebsite);
app.get("/:websiteId", handleGetWebsite);
app.get("/team/:teamId", handleListTeamWebsites);

export default app;
