// Media API types for Imoblr Platform API

// Media processing metadata
export interface ProcessingMetadata {
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  width?: number;
  height?: number;
  originalWidth?: number;
  originalHeight?: number;
  format: string;
  quality: number;
  compressionAttempted: boolean;
  compressionSuccessful: boolean;
  error?: string;
}

// Media data after processing
export interface MediaData {
  publicUrl: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  processingMetadata?: ProcessingMetadata;
  dbRecord?: Record<string, unknown>;
}

// Request for getting an upload URL
export interface GetUploadUrlRequest {
  fileName: string;
  fileType: string;
  propertyId: string;
  teamId: string;
}

// Request for getting a website asset upload URL
export interface GetWebsiteUploadUrlRequest {
  fileName: string;
  fileType: string;
  websiteId: string;
  teamId: string;
  mediaType: 'logo' | 'hero' | 'institutional';
}

// Response for upload URL request
export interface GetUploadUrlResponse {
  uploadUrl: string;
  fileId: string;
  key: string;
}

// Response for website upload URL request
export interface GetWebsiteUploadUrlResponse {
  uploadUrl: string;
  fileId: string;
  key: string;
  mediaType: 'logo' | 'hero' | 'institutional';
}

// Successful upload response
export interface UploadSuccessResponse {
  success: true;
  key: string;
  message: string;
  mediaData: {
    url: string;
    fileName: string;
    fileType: string;
    fileSize: number;
    databaseId?: number;
  };
}

// Error response
export interface ErrorResponse {
  success: false;
  error: string;
}

// Combined response type
export type UploadResponse = UploadSuccessResponse | ErrorResponse;
