import type { Env } from "@/api/types";
import { PhotonImage, resize, SamplingFilter } from "@cf-wasm/photon";
import { AwsClient } from "aws4fetch";

/**
 * Constructs an R2 object URL while ensuring no double slashes
 */
export function constructR2Url(baseUrl: string, key: string): URL {
  const url = new URL(baseUrl);
  // Ensure base path ends with a slash and key doesn't start with one
  const basePath = url.pathname.endsWith("/") ? url.pathname : `${url.pathname}/`;
  const objectKey = key.startsWith("/") ? key.substring(1) : key;
  url.pathname = `${basePath}${objectKey}`;
  return url;
}

/**
 * Initialize AWS client for R2 operations
 */
export function initAwsClient(env: Env): AwsClient {
  if (!env.R2_ACCESS_KEY_ID || !env.R2_SECRET_ACCESS_KEY) {
    throw new Error("Server configuration error: R2 credentials missing.");
  }

  return new AwsClient({
    accessKeyId: env.R2_ACCESS_KEY_ID,
    secretAccessKey: env.R2_SECRET_ACCESS_KEY,
  });
}

/**
 * Process image with compression and resizing
 */

export async function compressImage(
  buffer: ArrayBuffer,
  fileName: string,
  options: {
    quality?: number;
    maxWidth?: number;
    format?: string;
    originalContentType?: string;
  } = {}
): Promise<{
  buffer: ArrayBuffer;
  metadata: {
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
    width?: number;
    height?: number;
    originalWidth?: number;
    originalHeight?: number;
    format: string;
    quality: number;
    compressionAttempted: boolean;
    compressionSuccessful: boolean;
    error?: string;
  };
  contentType: string;
}> {
  try {
    console.log(`Compressing image: ${fileName}`);
    const defaultOptions = {
      quality: 0.8, // Quality between 0 and 1 (for JPEG/WebP)
      maxWidth: 1920,
      format: "jpeg", // Default format
    };
    const settings = { ...defaultOptions, ...options };

    const uint8Array = new Uint8Array(buffer);
    const originalSize = uint8Array.length;

    const inputImage = PhotonImage.new_from_byteslice(uint8Array);
    const originalWidth = inputImage.get_width();
    const originalHeight = inputImage.get_height();

    let newWidth = originalWidth;
    let newHeight = originalHeight;

    if (originalWidth > settings.maxWidth) {
      const ratio = settings.maxWidth / originalWidth;
      newWidth = settings.maxWidth;
      newHeight = Math.round(originalHeight * ratio);
    }

    let processedImage: PhotonImage;
    if (newWidth !== originalWidth || newHeight !== originalHeight) {
      console.log(`Resizing image from ${originalWidth}x${originalHeight} to ${newWidth}x${newHeight}`);
      processedImage = resize(inputImage, newWidth, newHeight, SamplingFilter.Lanczos3);
      inputImage.free(); // Free original image memory
    } else {
      processedImage = inputImage; // No resize needed
    }

    let compressedBuffer: Uint8Array;
    const format = settings.format.toLowerCase();
    console.log(`Encoding image to ${format.toUpperCase()}`);

    if (format === "webp") {
      const webpQuality = Math.round(settings.quality * 100); // Photon uses 0-100 for WebP
      compressedBuffer = processedImage.get_bytes_webp_with_quality(webpQuality);
    } else if (format === "png") {
      compressedBuffer = processedImage.get_bytes(); // PNG is lossless, no quality setting
    } else {
      // Default to JPEG
      const jpegQuality = Math.round(settings.quality * 100); // Photon uses 0-100 for JPEG
      compressedBuffer = processedImage.get_bytes_jpeg(jpegQuality);
    }

    processedImage.free(); // Free processed image memory

    const compressedSize = compressedBuffer.length;
    // Only consider it compressed if the size is actually smaller
    const compressionSuccessful = compressedSize < originalSize;
    const compressionRatio = originalSize > 0 ? (1 - compressedSize / originalSize) * 100 : 0;

    console.log(`Image compression ${compressionSuccessful ? "successful" : "did not reduce size"}:`, {
      originalSize,
      compressedSize,
      compressionRatio: `${compressionRatio.toFixed(2)}%`,
      format: format,
      quality: settings.quality,
      newWidth,
      newHeight,
    });

    // Return the potentially compressed buffer and metadata
    return {
      buffer: compressionSuccessful ? compressedBuffer.buffer : buffer, // Return original if not smaller
      metadata: {
        originalSize,
        compressedSize: compressionSuccessful ? compressedSize : originalSize,
        compressionRatio: compressionSuccessful ? compressionRatio : 0,
        width: newWidth,
        height: newHeight,
        originalWidth,
        originalHeight,
        format: compressionSuccessful ? format : options.format, // Use original format if not compressed
        quality: settings.quality,
        compressionAttempted: true,
        compressionSuccessful: compressionSuccessful,
      },
      contentType: compressionSuccessful ? `image/${format}` : options.originalContentType,
    };
  } catch (error) {
    console.error(`Error compressing image ${fileName}:`, error);
    // Return original buffer if compression fails catastrophically
    return {
      buffer,
      metadata: {
        originalSize: buffer.byteLength,
        compressedSize: buffer.byteLength,
        compressionRatio: 0,
        compressionAttempted: true,
        compressionSuccessful: false,
        error: error.message,
      },
      contentType: options.originalContentType,
    };
  }
}
