import type { Env } from "@/api/types";
import { verifyTeamAccess } from "@/api/utils";
import type { Context } from "hono";
import type { GetUploadUrlRequest, GetUploadUrlResponse } from "../types";

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

/**
 * <PERSON><PERSON> for generating upload URLs
 * This endpoint creates a proxy URL for client-side uploads
 */
export async function handleGetUploadUrl(
  c: Context<{ Bindings: Env; Variables: HandlerVariables }>,
): Promise<Response> {
  try {
    const env = c.env;
    const auth = c.get("auth");
    const userId = auth?.userId;

    if (!userId) {
      return new Response(
        JSON.stringify({ error: "Authentication required: User ID not found" }),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }

    // Get JWT token from request for team access verification
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

    if (!jwt) {
      return new Response(
        JSON.stringify({ error: "Authentication required: JWT token not found" }),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }

    // Parse request body
    const body = await c.req.json<GetUploadUrlRequest>();
    const { fileName, fileType, propertyId, teamId } = body;

    if (!fileName || !fileType || !propertyId || !teamId) {
      return new Response(
        JSON.stringify({ error: "Missing required parameters: fileName, fileType, propertyId, teamId" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // Verify that the user is a member of the team
    const teamAccess = await verifyTeamAccess(teamId, env, jwt);
    if (!teamAccess.success) {
      return teamAccess.error || new Response(
        JSON.stringify({ error: "Access denied: You are not a member of this team" }),
        { status: 403, headers: { "Content-Type": "application/json" } }
      );
    }

    // Generate a unique file ID and storage key
    const fileId = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    // Sanitize filename to avoid problematic characters
    const sanitizedFileName = fileName.replace(/[\s<>:"/\\|?*]+/g, "_");
    // Store property assets in a nested folder structure under team
    const key = `teams/${teamId}/properties/${propertyId}/${fileId}-${sanitizedFileName}`;

    // Create the proxy URL which points back to the current host
    const request = c.req.raw;
    const url = new URL(request.url);

    // Change path to proxy-upload endpoint with the generated key
    url.pathname = `/media/proxy-upload/${key}`;
    url.search = ""; // Clear existing search params
    url.searchParams.set("fileType", fileType);

    console.log(`Generated upload URL for property ${propertyId} in team ${teamId}, key: ${key}, userId: ${userId}`);

    const response: GetUploadUrlResponse = {
      uploadUrl: url.toString(),
      fileId,
      key,
    };

    return new Response(
      JSON.stringify(response),
      { headers: { "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Error in handleGetUploadUrl:", error);
    return new Response(
      JSON.stringify({
        error: "Internal Server Error",
        message: error instanceof Error ? error.message : "Unknown error"
      }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}
