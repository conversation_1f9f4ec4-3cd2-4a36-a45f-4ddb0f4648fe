import type { Context } from "hono";
import type { Env } from "@/api/types";
import { initAwsClient } from "../utils";

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

/**
 * Handler for deleting files from R2 storage
 */
export async function handleDeleteFile(
  c: Context<{ Bindings: Env; Variables: HandlerVariables }>,
): Promise<Response> {
  try {
    const env = c.env;
    const auth = c.get("auth");
    const userId = auth?.userId;

    // Parse request body
    const body = await c.req.json<{ key: string }>();
    const { key } = body;

    if (!key) {
      return new Response(
        JSON.stringify({ error: "Missing required parameter: key" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    console.log(`Attempting to delete file with key: ${key}, requested by userId: ${userId}`);

    // Initialize AWS client for R2 operations
    const aws = initAwsClient(env);

    // Construct R2 URL for deletion
    if (!env.R2_BUCKET_URL) {
      throw new Error("Server configuration error: R2 bucket URL missing.");
    }

    // Construct the URL for the file to delete
    const bucketUrl = new URL(env.R2_BUCKET_URL);
    bucketUrl.pathname = `/${key}`;

    // Delete the file from R2
    console.log(`Deleting file from R2: ${bucketUrl.toString()}`);
    const deleteResponse = await aws.fetch(bucketUrl.toString(), {
      method: "DELETE",
    });

    if (!deleteResponse.ok) {
      const errorText = await deleteResponse.text().catch(() => "Unknown error");
      console.error(`R2 delete failed (${deleteResponse.status}): ${errorText}`);
      
      // If the file doesn't exist, consider it a success
      if (deleteResponse.status === 404) {
        return new Response(
          JSON.stringify({ success: true, message: "File not found, already deleted or never existed" }),
          { headers: { "Content-Type": "application/json" } }
        );
      }
      
      throw new Error(`R2 delete failed: ${deleteResponse.status} ${deleteResponse.statusText}`);
    }

    // Return success response
    return new Response(
      JSON.stringify({ success: true, message: "File deleted successfully" }),
      { headers: { "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Error in handleDeleteFile:", error);
    return new Response(
      JSON.stringify({ 
        success: false,
        error: "Internal Server Error", 
        message: error instanceof Error ? error.message : "Unknown error" 
      }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}
