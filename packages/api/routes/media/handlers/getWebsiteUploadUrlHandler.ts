import type { Env } from "@/api/types";
import { verifyTeamAccess } from "@/api/utils";
import type { Context } from "hono";
import type { GetWebsiteUploadUrlRequest, GetWebsiteUploadUrlResponse } from "../types";

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

/**
 * <PERSON><PERSON> for generating upload URLs for website assets
 * This endpoint creates a proxy URL for client-side uploads of website logo and hero images
 */
export async function handleGetWebsiteUploadUrl(
  c: Context<{ Bindings: Env; Variables: HandlerVariables }>,
): Promise<Response> {
  try {
    const env = c.env;
    const auth = c.get("auth");
    const userId = auth?.userId;

    if (!userId) {
      return new Response(
        JSON.stringify({ error: "Authentication required: User ID not found" }),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }

    // Get JWT token from request for team access verification
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

    if (!jwt) {
      return new Response(
        JSON.stringify({ error: "Authentication required: JWT token not found" }),
        { status: 401, headers: { "Content-Type": "application/json" } }
      );
    }

    // Parse request body
    const body = await c.req.json<GetWebsiteUploadUrlRequest>();
    const { fileName, fileType, websiteId, teamId, mediaType } = body;

    if (!fileName || !fileType || !websiteId || !teamId || !mediaType) {
      return new Response(
        JSON.stringify({ error: "Missing required parameters: fileName, fileType, websiteId, teamId, mediaType" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // Verify that the user is a member of the team
    const teamAccess = await verifyTeamAccess(teamId, env, jwt);
    if (!teamAccess.success) {
      return teamAccess.error || new Response(
        JSON.stringify({ error: "Access denied: You are not a member of this team" }),
        { status: 403, headers: { "Content-Type": "application/json" } }
      );
    }

    // Validate mediaType
    if (mediaType !== "logo" && mediaType !== "hero" && mediaType !== "institutional") {
      return new Response(
        JSON.stringify({ error: "Invalid mediaType. Must be 'logo', 'hero', or 'institutional'" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // Generate a unique file ID and storage key
    const fileId = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    // Sanitize filename to avoid problematic characters
    const sanitizedFileName = fileName.replace(/[\s<>:"/\\|?*]+/g, "_");
    // Store website assets in a nested folder structure under team
    const key = `teams/${teamId}/websites/${websiteId}/${mediaType}_${fileId}-${sanitizedFileName}`;

    // Create the proxy URL which points back to the current host
    const request = c.req.raw;
    const url = new URL(request.url);

    // Change path to proxy-upload endpoint with the generated key
    url.pathname = `/media/proxy-upload/${key}`;
    url.search = ""; // Clear existing search params
    url.searchParams.set("fileType", fileType);
    url.searchParams.set("mediaType", mediaType);
    url.searchParams.set("websiteId", websiteId);

    console.log(`Generated website ${mediaType} image upload URL for website ${websiteId} in team ${teamId}, key: ${key}, userId: ${userId}`);

    const response: GetWebsiteUploadUrlResponse = {
      uploadUrl: url.toString(),
      fileId,
      key,
      mediaType,
    };

    return new Response(
      JSON.stringify(response),
      { headers: { "Content-Type": "application/json" } }
    );
  } catch (error) {
    console.error("Error in handleGetWebsiteUploadUrl:", error);
    return new Response(
      JSON.stringify({
        error: "Internal Server Error",
        message: error instanceof Error ? error.message : "Unknown error"
      }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}
