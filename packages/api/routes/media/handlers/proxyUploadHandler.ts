import type { Env } from "@/api/types";
import { getSupabaseClient, verifyTeamAccess } from "@/api/utils";
import type { Context } from "hono";
import type { MediaData, UploadResponse } from "../types";
import { compressImage, constructR2Url, initAwsClient } from "../utils";

type HandlerVariables = { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };

/**
 * Add media record to Supabase database
 */
async function addMediaToDatabase(propertyId: string, mediaData: MediaData, env: Env, jwt?: string): Promise<Record<string, unknown> | null> {
  try {
    const { client: supabase, error: supabaseError } = getSupabaseClient(env, jwt);
    if (supabaseError) {
      console.error("Supabase client initialization error");
      return null;
    }

    console.log(`Adding media record to database for property: ${propertyId}`);

    const insertData = {
      property_id: propertyId,
      media_type: mediaData.fileType.startsWith("image/") ? "image" : "document",
      url: mediaData.publicUrl,
      file_name: mediaData.fileName,
      file_size: mediaData.fileSize,
      file_type: mediaData.fileType,
      created_at: new Date().toISOString(),
      // Optionally add compression metadata if needed
      // compression_metadata: mediaData.processingMetadata || null,
    };

    console.log("Inserting data into properties_media:", { ...insertData, url: "..." }); // Avoid logging full URL

    const { data, error } = await supabase.from("properties_media").insert(insertData).select().single();

    if (error) {
      console.error("Database insert error:", error);
      throw new Error(`Failed to add media to database: ${error.message}`);
    }

    console.log("Media added to database successfully, ID:", data.id);
    return data;
  } catch (error) {
    console.error("Error in addMediaToDatabase:", error);
    // Don't re-throw here, allow proxy upload to succeed even if DB fails
    return null;
  }
}

/**
 * Update website with image URL in Supabase database
 */
async function updateWebsiteWithImageUrl(websiteId: string, mediaType: string, imageUrl: string, env: Env, jwt?: string): Promise<Record<string, unknown> | null> {
  try {
    const { client: supabase, error: supabaseError } = getSupabaseClient(env, jwt);
    if (supabaseError) {
      console.error("Supabase client initialization error");
      return null;
    }

    console.log(`Updating website ${websiteId} with ${mediaType} image URL`);

    // Determine which column to update based on mediaType
    let columnToUpdate = "";
    if (mediaType === "logo") {
      columnToUpdate = "logo_image_url";
    } else if (mediaType === "hero") {
      columnToUpdate = "hero_image_url";
    } else if (mediaType === "institutional") {
      columnToUpdate = "institutional_image_url";
    } else {
      console.error(`Unknown mediaType: ${mediaType}`);
      return null;
    }

    const { data, error } = await supabase
      .from("websites")
      .update({ [columnToUpdate]: imageUrl })
      .eq("id", websiteId)
      .select()
      .single();

    if (error) {
      console.error("Database update error:", error);
      throw new Error(`Failed to update website with image URL: ${error.message}`);
    }

    console.log(`Website ${websiteId} updated with ${mediaType} image URL successfully`);
    return data;
  } catch (error) {
    console.error("Error in updateWebsiteWithImageUrl:", error);
    // Don't re-throw here, allow proxy upload to succeed even if DB fails
    return null;
  }
}

/**
 * Handler for proxy uploads to R2
 * This handles file uploads, image compression, and database record creation
 */
export async function handleProxyUpload(c: Context<{ Bindings: Env; Variables: HandlerVariables }>): Promise<Response> {
  const env = c.env;
  const auth = c.get("auth");
  const userId = auth?.userId;

  if (!userId) {
    return new Response(
      JSON.stringify({ error: "Authentication required: User ID not found" }),
      { status: 401, headers: { "Content-Type": "application/json" } }
    );
  }

  // Get JWT token from request for user-specific operations
  const authHeader = c.req.header("Authorization");
  const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

  if (!jwt) {
    return new Response(
      JSON.stringify({ error: "Authentication required: JWT token not found" }),
      { status: 401, headers: { "Content-Type": "application/json" } }
    );
  }

  try {
    const request = c.req.raw;
    const url = new URL(request.url);

    // Get the path parameter which contains everything after /proxy-upload/
    const key = c.req.param("path");
    console.log("Path parameter value:", key);

    if (!key) {
      console.error("Path parameter not found in request");
      return new Response(
        JSON.stringify({ success: false, error: "Invalid upload URL format - path parameter not found" }),
        { status: 400, headers: { "Content-Type": "application/json" } },
      );
    }
    const fileType = url.searchParams.get("fileType");

    if (!fileType) {
      return new Response(JSON.stringify({ success: false, error: "Missing fileType parameter" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Extract team ID and property/website ID from the key
    // New format: teams/[teamId]/properties/[propertyId]/
    const teamIdMatch = key.match(/^teams\/([^/]+)\//);
    const teamId = teamIdMatch ? teamIdMatch[1] : null;

    // Extract property ID
    const propertyIdMatch = key.match(/^teams\/[^/]+\/properties\/([^/]+)\//);
    // Check if this is a website asset upload
    // New format: teams/[teamId]/websites/[websiteId]/
    const websiteIdMatch = key.match(/^teams\/[^/]+\/websites\/([^/]+)\//);

    let propertyId: string | null = null;
    let websiteId: string | null = null;
    let mediaType: string | null = null;

    if (!teamId) {
      return new Response(JSON.stringify({
        success: false,
        error: "Invalid key format: missing team ID",
        message: "Expected format: teams/[teamId]/properties/[propertyId]/ or teams/[teamId]/websites/[websiteId]/",
        key: key
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Verify that the user is a member of the team
    const teamAccess = await verifyTeamAccess(teamId, env, jwt);
    if (!teamAccess.success) {
      return teamAccess.error || new Response(
        JSON.stringify({
          success: false,
          error: "Access denied: You are not a member of this team"
        }),
        { status: 403, headers: { "Content-Type": "application/json" } }
      );
    }

    if (propertyIdMatch) {
      propertyId = propertyIdMatch[1];
      console.log(`Processing upload for property: ${propertyId} in team: ${teamId}, key: ${key}, userId: ${userId}`);
    } else if (websiteIdMatch) {
      websiteId = websiteIdMatch[1];
      mediaType = url.searchParams.get("mediaType");
      console.log(`Processing upload for website: ${websiteId} in team: ${teamId}, mediaType: ${mediaType}, key: ${key}, userId: ${userId}`);
    } else {
      return new Response(JSON.stringify({
        success: false,
        error: "Invalid key format: missing property or website ID",
        message: "Expected format: teams/[teamId]/properties/[propertyId]/ or teams/[teamId]/websites/[websiteId]/",
        key: key
      }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Get the file data from the request
    const fileBuffer = await request.arrayBuffer();
    if (!fileBuffer || fileBuffer.byteLength === 0) {
      return new Response(JSON.stringify({ success: false, error: "Empty file or invalid data" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    const fileSize = fileBuffer.byteLength;
    console.log(`Received file: ${key}, size: ${fileSize} bytes, type: ${fileType}`);

    // Process the file - apply compression for images if applicable
    let processedBuffer = fileBuffer;
    let finalContentType = fileType;
    let processingMetadata = null;

    const fileName = key.split("/").pop() || "unknown-file";

    // Apply image compression for supported image types
    if (
      fileType.startsWith("image/") &&
      (fileType.includes("jpeg") || fileType.includes("jpg") || fileType.includes("png") || fileType.includes("webp"))
    ) {
      console.log(`Processing image: ${fileName}`);

      // Extract format preference from content type
      let preferredFormat = "jpeg";
      if (fileType.includes("png")) preferredFormat = "png";
      if (fileType.includes("webp")) preferredFormat = "webp";

      // Apply compression
      const processResult = await compressImage(fileBuffer, fileName, {
        maxWidth: 1920, // Maximum width for images
        quality: 0.8, // Quality setting (for formats that support it)
        format: preferredFormat,
        originalContentType: fileType,
      });

      processedBuffer = processResult.buffer;
      finalContentType = processResult.contentType || fileType;
      processingMetadata = processResult.metadata;

      console.log(`Image processing complete: ${fileName}, new size: ${processedBuffer.byteLength} bytes`);
    }

    // Initialize AWS client for R2 upload
    const aws = initAwsClient(env);

    // Prepare for upload to R2 bucket
    if (!env.R2_BUCKET_URL) {
      throw new Error("Server configuration error: R2 bucket URL missing.");
    }

    // Construct R2 URL for upload
    const bucketUrl = constructR2Url(env.R2_BUCKET_URL, key);

    // Upload to R2
    console.log(`Uploading to R2: ${bucketUrl.toString()}`);
    const uploadResponse = await aws.fetch(bucketUrl.toString(), {
      method: "PUT",
      body: processedBuffer,
      headers: {
        "Content-Type": finalContentType,
        "Content-Length": processedBuffer.byteLength.toString(),
        "Cache-Control": "public, max-age=31536000", // Cache for 1 year
      },
    });

    if (!uploadResponse.ok) {
      throw new Error(`R2 upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
    }

    // Construct public URL for the uploaded file
    if (!env.R2_PUBLIC_URL) {
      throw new Error("Server configuration error: R2 public URL missing.");
    }

    const publicUrl = constructR2Url(env.R2_PUBLIC_URL, key).toString();

    // Prepare media data for database
    const mediaData: MediaData = {
      publicUrl: publicUrl,
      fileName: fileName,
      fileType: finalContentType,
      fileSize: processedBuffer.byteLength,
      processingMetadata: processingMetadata,
      dbRecord: null, // Will be updated if DB upload succeeds
    };

    // Attempt to add to database (don't fail if this fails)
    try {
      if (propertyId) {
        // For property media
        mediaData.dbRecord = await addMediaToDatabase(propertyId, mediaData, env, jwt);
        if (!mediaData.dbRecord) {
          console.warn("Database record creation failed, but R2 upload succeeded.");
        }
      } else if (websiteId && mediaType) {
        // For website media
        mediaData.dbRecord = await updateWebsiteWithImageUrl(websiteId, mediaType, mediaData.publicUrl, env, jwt);
        if (!mediaData.dbRecord) {
          console.warn("Website update failed, but R2 upload succeeded.");
        }
      }
    } catch (dbError) {
      console.warn("Database operation failed, but R2 upload succeeded.");
    }

    // Return success response
    const response: UploadResponse = {
      success: true,
      key,
      message: "Upload processed successfully.",
      mediaData: {
        url: mediaData.publicUrl,
        fileName: mediaData.fileName,
        fileType: mediaData.fileType,
        fileSize: mediaData.fileSize,
        databaseId: mediaData.dbRecord?.id,
      },
    };

    return new Response(JSON.stringify(response), { headers: { "Content-Type": "application/json" } });
  } catch (error) {
    console.error("Error in handleProxyUpload:", error);
    const errorResponse: UploadResponse = {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };

    const status = error instanceof Error && error.message.startsWith("Server configuration error") ? 503 : 500;

    return new Response(JSON.stringify(errorResponse), { status, headers: { "Content-Type": "application/json" } });
  }
}
