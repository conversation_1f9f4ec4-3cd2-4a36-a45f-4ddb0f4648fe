import type { Env } from "@/api/types";
import { createUnauthorizedResponse, verifyAuthentication } from "@/api/utils";
import { Hono } from "hono/tiny";
import { handleGetUploadUrl, handleGetWebsiteUploadUrl, handleProxyUpload, handleDeleteFile } from "./handlers";

// Create Hono router with binding type for environment variables
const mediaRouter = new Hono<{
  Bindings: Env;
  Variables: { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };
}>();

// Middleware: Authentication Check for all routes in this router
mediaRouter.use(async (c, next) => {
  const env = c.env;

  // Pass the raw request directly to verifyAuthentication
  const auth = await verifyAuthentication(c.req.raw, env);
  if (!auth.isAuthenticated) {
    // Return the unauthorized response directly
    return createUnauthorizedResponse(auth.error || "Authentication required");
  }

  // Store auth data in the context variables for subsequent handlers/middleware
  c.set("auth", auth);

  // Proceed to the next middleware or route handler
  return await next();
});

// Define routes relative to the mount point (e.g., /media)
// POST /media/get-upload-url
mediaRouter.post("/get-upload-url", async (c) => {
  return await handleGetUploadUrl(c);
});

// POST /media/get-website-upload-url
mediaRouter.post("/get-website-upload-url", async (c) => {
  return await handleGetWebsiteUploadUrl(c);
});

// POST /media/delete-file
mediaRouter.post("/delete-file", async (c) => {
  return await handleDeleteFile(c);
});

// Use a specific catch-all route for the proxy-upload with a named parameter
// This pattern will explicitly define a path param that captures everything after /proxy-upload/
mediaRouter.post("/proxy-upload/:path{.*}", async (c) => {
  // Pass the specific path parameter name to the handler
  return await handleProxyUpload(c);
});

mediaRouter.put("/proxy-upload/:path{.*}", async (c) => {
  // Pass the specific path parameter name to the handler
  return await handleProxyUpload(c);
});

// Export the configured router for media
export default mediaRouter;
