import { handleStartTrial } from "@/api/routes/billing/handlers";
import type { Env } from "@/api/types";
import { initSupabase } from "@/api/utils";
import type { Context } from "hono";

// POST /teams
export const handleCreateTeam = async (c: Context<{ Bindings: Env }>) => {
  try {
    const body = await c.req.json();
    console.log("Creating team with args:", body);

    const { name, slug, billing_email, type } = body;

    if (!name || !slug || !billing_email) {
      return c.json({ error: "Missing required fields: name, slug, billing_email" }, 400);
    }

    if (!type) {
      console.warn("Team type not provided, proceeding without type");
    }

    // Get JWT token from request for user-specific operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : authHeader || "";

    // Initialize Supabase client with user JWT
    const supabaseClient = initSupabase(c.env, jwt);

    // Create team account using the RPC function
    const { data: teamAccount, error: teamError } = await supabaseClient.rpc("create_team_account", {
      name,
      slug,
    });

    // If team was created successfully and type is provided, update the metadata
    if (teamAccount && !teamError && type) {
      try {
        // Update the team account with the type in the metadata
        await supabaseClient.rpc("update_team_account", {
          team_account_id: teamAccount.id,
          public_metadata: { type },
        });

        // Add type to the returned team account object
        teamAccount.metadata = { type };
      } catch (updateError) {
        console.error("Error updating team metadata with type:", updateError);
        // Continue with the process even if metadata update fails
      }
    }

    if (teamError) {
      console.error("Error creating team account:", teamError);
      return c.json({ error: teamError.message || "Failed to create team account" }, 400);
    }

    // Get trial plan lookup key
    const lookupKey = c.env.DEFAULT_TRIAL_PLAN_LOOKUP_KEY;
    console.log("Trial plan lookup key:", lookupKey);

    if (!lookupKey) {
      console.error("Missing DEFAULT_TRIAL_PLAN_LOOKUP_KEY environment variable");
      return c.json({
        ...teamAccount,
        trial: null,
      });
    }

    // Start trial subscription
    console.log("Starting trial subscription for:", {
      account_id: teamAccount.id,
      name: teamAccount.name,
      lookup_key: lookupKey,
      email: billing_email,
    });

    let trialData = null;
    let trialError = null;

    try {
      // Create a request object with the trial data
      const trialRequest = {
        account_id: teamAccount.id,
        lookup_key: lookupKey,
        quantity: 1,
        email: billing_email,
        teamName: teamAccount.name,
      };

      // Create a mock Context object to pass to handleStartTrial
      const trialContext = {
        env: c.env,
        req: {
          json: async () => trialRequest,
        },
        json: (data: Record<string, unknown>, status?: number) => {
          if (status && status >= 400) {
            throw new Error(typeof data.error === 'string' ? data.error : "Failed to start trial");
          }
          return data;
        },
      };

      // Directly call the billing handler function
      // Cast to unknown first to avoid TypeScript errors
      trialData = await handleStartTrial(trialContext as unknown as Context<{ Bindings: Env }>);
      console.log("Trial subscription created successfully:", trialData);
    } catch (error) {
      trialError = error;
      console.error("Error starting trial:", error instanceof Error ? error.message : String(error));
    }

    return c.json({
      ...teamAccount,
      trial: trialError ? null : trialData,
    });
  } catch (error) {
    console.error("Error creating team:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to create team"
    }, 500);
  }
};
