import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse, verifyAuthentication } from "@/api/utils/auth";
import type { Context } from "hono";

export async function handleGetTeamProfile(c: Context<{ Bindings: Env }>) {
  try {
    // Verify authentication
    const auth = await verifyAuthentication(c.req.raw, c.env);
    if (!auth.isAuthenticated) {
      return createUnauthorizedResponse(auth.error || "Authentication required");
    }

    // Get the team ID from the URL params
    const { teamId } = c.req.param();

    if (!teamId) {
      return c.json({ error: "Team ID is required" }, 400);
    }

    // Get JWT token from request for user-specific operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

    // Initialize Supabase client
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // Try to get the existing profile
    const { data: profile, error: profileError } = await supabaseClient
      .from('public_team_profiles')
      .select('*')
      .eq('team_account_id', teamId)
      .maybeSingle();

    // If there's a profile, return it
    if (profile) {
      return c.json({
        success: true,
        profile
      });
    }

    // If there's an error other than not found
    if (profileError && profileError.code !== 'PGRST116') {
      console.error("Error fetching team profile:", profileError);
      return c.json({
        error: "Failed to fetch team profile",
        details: profileError.message
      }, 500);
    }

    // If no profile exists, get the team name and create one
    const { data: teamData, error: teamError } = await supabaseClient
      .from('basejump.team_accounts')
      .select('name')
      .eq('id', teamId)
      .single();

    if (teamError) {
      console.error("Error fetching team data:", teamError);
      return c.json({
        error: "Failed to fetch team data",
        details: teamError.message
      }, 500);
    }

    // Create a new profile
    const { data: newProfile, error: insertError } = await supabaseClient
      .from('public_team_profiles')
      .insert({
        team_account_id: teamId,
        name: teamData?.name || ""
      })
      .select()
      .single();

    if (insertError) {
      // If insert fails, try one more time to get the profile
      // (in case it was created by another concurrent request)
      const { data: retryData, error: retryError } = await supabaseClient
        .from('public_team_profiles')
        .select('*')
        .eq('team_account_id', teamId)
        .single();

      if (retryError) {
        console.error("Error creating team profile:", insertError);
        return c.json({
          error: "Failed to create team profile",
          details: insertError.message
        }, 500);
      }

      return c.json({
        success: true,
        profile: retryData
      });
    }

    return c.json({
      success: true,
      profile: newProfile
    });
  } catch (error) {
    console.error("Error getting team profile:", error);
    return c.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error occurred"
    }, 500);
  }
}
