import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { initAwsClient, constructR2Url } from "@/api/routes/media/utils";
import { initStripeClient } from "@/api/routes/billing/handlers/utils";
import { createUnauthorizedResponse, verifyAuthentication } from "@/api/utils/auth";
import type { Context } from "hono";

export async function handleDeleteTeam(c: Context<{ Bindings: Env }>) {
  try {
    // Verify authentication
    const auth = await verifyAuthentication(c.req.raw, c.env);
    if (!auth.isAuthenticated) {
      return createUnauthorizedResponse(auth.error || "Authentication required");
    }

    // Get the team ID from the URL params
    const { teamId } = c.req.param();

    if (!teamId) {
      return c.json({ error: "Team ID is required" }, 400);
    }

    // Get JWT token for Supabase operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

    // Initialize Supabase client
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt);
    if (supabaseError || !supabaseClient) {
      return c.json({ error: supabaseError || "Failed to initialize Supabase client" }, 500);
    }

    // Initialize AWS client for R2 operations
    const aws = initAwsClient(c.env);

    // Delete team folder from R2
    if (c.env.R2_BUCKET_URL) {
      try {
        const teamFolderKey = `teams/${teamId}`;
        const r2Url = constructR2Url(c.env.R2_BUCKET_URL, teamFolderKey);
        
        // Delete the team folder from R2
        await aws.fetch(r2Url.toString(), {
          method: "DELETE",
        });

        console.log(`Successfully deleted team folder from R2: ${teamFolderKey}`);
      } catch (r2Error) {
        console.error("Error deleting team folder from R2:", r2Error);
        // Continue with deletion even if R2 deletion fails
      }
    }

    // Initialize Stripe client
    const stripe = initStripeClient(c.env);

    try {
      // Find and cancel Stripe subscription
      const subscriptions = await stripe.subscriptions.list({
        limit: 1,
        expand: ["data.customer"],
      });

      const teamSubscription = subscriptions.data.find(
        (sub) => sub.metadata.imoblr_team_id === teamId
      );

      if (teamSubscription) {
        await stripe.subscriptions.cancel(teamSubscription.id);
        console.log(`Successfully canceled Stripe subscription for team: ${teamId}`);
      }
    } catch (stripeError) {
      console.error("Error canceling Stripe subscription:", stripeError);
      // Continue with deletion even if Stripe cancellation fails
    }

    // Delete team data from Supabase
    const { error: deleteError } = await supabaseClient
      .from("team_accounts")
      .delete()
      .eq("id", teamId);

    if (deleteError) {
      console.error("Error deleting team from Supabase:", deleteError);
      return c.json({ error: "Failed to delete team", details: deleteError.message }, 500);
    }

    return c.json({ success: true, message: "Team deleted successfully" });
  } catch (error) {
    console.error("Error in handleDeleteTeam:", error);
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
    return c.json({ error: "Failed to delete team", details: errorMessage }, 500);
  }
}