import type { Env } from "@/api/types";
import { getSupabaseClient } from "@/api/utils";
import { createUnauthorizedResponse, verifyAuthentication } from "@/api/utils/auth";
import type { Context } from "hono";

interface TeamProfileUpdateData {
  name?: string;
  address?: string;
  phone_number?: string;
  whatsapp_number?: string;
  instagram_url?: string;
  facebook_url?: string;
  youtube_url?: string;
  about_us?: string;
}

export async function handleUpdateTeamProfile(c: Context<{ Bindings: Env }>) {
  try {
    // Verify authentication
    const auth = await verifyAuthentication(c.req.raw, c.env);
    if (!auth.isAuthenticated) {
      return createUnauthorizedResponse(auth.error || "Authentication required");
    }

    // Get the team ID from the URL params
    const { teamId } = c.req.param();

    if (!teamId) {
      return c.json({ error: "Team ID is required" }, 400);
    }

    // Get request body
    const requestData = await c.req.json<TeamProfileUpdateData>();

    // Get JWT token from request for user-specific operations
    const authHeader = c.req.header("Authorization");
    const jwt = authHeader?.startsWith("Bearer ") ? authHeader.substring(7) : undefined;

    // Initialize Supabase client
    const { client: supabaseClient, error: supabaseError } = getSupabaseClient(c.env, jwt);
    if (supabaseError || !supabaseClient) {
      console.error("Supabase client initialization error:", supabaseError);
      return c.json({
        error: "Server configuration error: Unable to connect to database",
        details: "Failed to initialize Supabase client"
      }, 500);
    }

    // First check if the profile exists
    const { data: existingProfile, error: checkError } = await supabaseClient
      .from('public_team_profiles')
      .select('id')
      .eq('team_account_id', teamId)
      .maybeSingle();

    // If there's an error other than not found
    if (checkError && checkError.code !== 'PGRST116') {
      console.error("Error checking team profile:", checkError);
      return c.json({
        error: "Failed to check team profile",
        details: checkError.message
      }, 500);
    }

    let profile: any;

    // If profile exists, update it
    if (existingProfile) {
      const { data: updatedProfile, error: updateError } = await supabaseClient
        .from('public_team_profiles')
        .update({
          name: requestData.name,
          address: requestData.address,
          phone_number: requestData.phone_number,
          whatsapp_number: requestData.whatsapp_number,
          instagram_url: requestData.instagram_url,
          facebook_url: requestData.facebook_url,
          youtube_url: requestData.youtube_url,
          about_us: requestData.about_us
        })
        .eq('team_account_id', teamId)
        .select()
        .single();

      if (updateError) {
        console.error("Error updating team profile:", updateError);
        return c.json({
          error: "Failed to update team profile",
          details: updateError.message
        }, 500);
      }

      profile = updatedProfile;
    } else {
      // If profile doesn't exist, create it
      const { data: newProfile, error: insertError } = await supabaseClient
        .from('public_team_profiles')
        .insert({
          team_account_id: teamId,
          name: requestData.name,
          address: requestData.address,
          phone_number: requestData.phone_number,
          whatsapp_number: requestData.whatsapp_number,
          instagram_url: requestData.instagram_url,
          facebook_url: requestData.facebook_url,
          youtube_url: requestData.youtube_url,
          about_us: requestData.about_us
        })
        .select()
        .single();

      if (insertError) {
        console.error("Error creating team profile:", insertError);
        return c.json({
          error: "Failed to create team profile",
          details: insertError.message
        }, 500);
      }

      profile = newProfile;
    }

    return c.json({
      success: true,
      profile
    });
  } catch (error) {
    console.error("Error updating team profile:", error);
    return c.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error occurred"
    }, 500);
  }
}
