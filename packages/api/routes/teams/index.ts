import { Hono } from "hono/tiny";
import type { Env } from "../../types";
import { createUnauthorizedResponse, verifyAuthentication } from "../../utils";
import { handleCreateTeam, handleDeleteTeam, handleGetTeamProfile, handleUpdateTeamProfile } from "./handlers";

// Create Hono router with binding type for environment variables
const teamsRouter = new Hono<{
  Bindings: Env;
  Variables: { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };
}>();

// Middleware: Authentication Check for all routes in this router
teamsRouter.use(async (c, next) => {
  const env = c.env;

  // Pass the raw request directly to verifyAuthentication
  const auth = await verifyAuthentication(c.req.raw, env);
  if (!auth.isAuthenticated) {
    // Return the unauthorized response directly
    return createUnauthorizedResponse(auth.error || "Authentication required");
  }

  // Store auth data in the context variables for subsequent handlers/middleware
  c.set("auth", auth);

  // Proceed to the next middleware or route handler
  return await next();
});

// Define routes
teamsRouter.post("/", handleCreateTeam);

// Team profile routes
teamsRouter.get("/:teamId/profile", handleGetTeamProfile);
teamsRouter.put("/:teamId/profile", handleUpdateTeamProfile);
teamsRouter.delete("/:teamId", handleDeleteTeam);

export default teamsRouter;
