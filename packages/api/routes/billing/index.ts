import { Hono } from "hono/tiny";
import type { Env } from "../../types";
import { createUnauthorizedResponse, verifyAuthentication } from "../../utils";
import {
  handleCreatePaymentIntent,
  handleGetBillingStatus,
  handleGetPortalSession,
  handleGetProducts,
  handlePaymentSuccess,
  handlePayOpenInvoices, 
  handleSetupPayment,
  handleStartTrial
} from "./handlers";

// Create Hono router with binding type for environment variables
const billingRouter = new Hono<{
  Bindings: Env;
  Variables: { auth?: { isAuthenticated: boolean; userId?: string; error?: string } };
}>();

// Middleware: Authentication Check for all routes in this router
billingRouter.use(async (c, next) => {
  const env = c.env;

  // Pass the raw request directly to verifyAuthentication
  const auth = await verifyAuthentication(c.req.raw, env);
  if (!auth.isAuthenticated) {
    // Return the unauthorized response directly
    return createUnauthorizedResponse(auth.error || "Authentication required");
  }

  // Store auth data in the context variables for subsequent handlers/middleware
  c.set("auth", auth);

  // Proceed to the next middleware or route handler
  return await next();
});

// Define routes
billingRouter.get("/products", handleGetProducts);
billingRouter.get("/status/:teamId", handleGetBillingStatus);
billingRouter.post("/trial", handleStartTrial);
billingRouter.post("/payment-setup", handleSetupPayment);
billingRouter.post("/payment-intent", handleCreatePaymentIntent);
billingRouter.get("/portal-session", handleGetPortalSession);
billingRouter.post("/payment-success", handlePaymentSuccess);
billingRouter.post("/pay-invoices", handlePayOpenInvoices);

export default billingRouter;
