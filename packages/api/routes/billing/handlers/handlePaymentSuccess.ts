import type { Env } from "@/api/types";
import type { Context } from "hono";
import { initStripeClient } from "./utils";

// POST /billing/payment-success
export const handlePaymentSuccess = async (c: Context<{ Bindings: Env }>) => {
  try {
    const body = await c.req.json();
    console.log("Handling payment success:", body);

    const { setup_intent_id } = body;

    if (!setup_intent_id) {
      return c.json({ error: "Missing required field: setup_intent_id" }, 400);
    }

    const stripeClient = initStripeClient(c.env);

    // Get the setup intent to access metadata and customer
    const setupIntent = await stripeClient.setupIntents.retrieve(setup_intent_id);
    console.log(`Retrieved setup intent: id=${setupIntent.id}, status=${setupIntent.status}`);
    // Only log team ID for context
    if (setupIntent.metadata?.imoblr_team_id) {
      console.log(`Team ID: ${setupIntent.metadata.imoblr_team_id}`);
    }

    // The actual processing will be handled by the webhook handler for setup_intent.succeeded
    // This function now just returns a success response to the client
    // This avoids race conditions and ensures proper handling through the event-driven architecture

    return c.json({
      success: true,
      message: "Payment setup successful. Processing subscription update...",
      setup_intent_id: setup_intent_id
    });
  } catch (error: unknown) {
    console.error("Error handling payment success:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to handle payment success"
    }, 500);
  }
};
