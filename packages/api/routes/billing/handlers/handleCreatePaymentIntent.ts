import type { Env } from "@/api/types";
import type { Context } from "hono";
import { initStripeClient } from "./utils";

// POST /billing/payment-intent
export const handleCreatePaymentIntent = async (c: Context<{ Bindings: Env }>) => {
  try {
    const body = await c.req.json();
    console.log("Payment intent args:", JSON.stringify(body, null, 2));

    const { subscription_id, account_id } = body;

    if (!subscription_id) {
      return c.json({ error: "Missing required field: subscription_id" }, 400);
    }

    if (!account_id) {
      return c.json({ error: "Missing required field: account_id" }, 400);
    }

    const stripeClient = initStripeClient(c.env);

    // Get the subscription
    const subscription = await stripeClient.subscriptions.retrieve(subscription_id);

    if (subscription.metadata.account_id !== account_id) {
      return c.json({ error: "Subscription does not belong to this account" }, 403);
    }

    // Create a setup intent for collecting payment method
    const setupIntent = await stripeClient.setupIntents.create({
      customer: subscription.customer as string,
      payment_method_types: ["card"],
      metadata: {
        subscription_id,
        account_id,
      },
    });

    return c.json({
      clientSecret: setupIntent.client_secret,
      customerId: subscription.customer,
      subscriptionId: subscription.id,
    });
  } catch (error: unknown) {
    console.error("Error creating payment intent:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to create payment intent"
    }, 500);
  }
};
