import type { Env } from "@/api/types";
import type { Context } from "hono";
import type { Strip<PERSON> } from "stripe";
import { initStripeClient } from "./utils";

// Define a type for the customer with invoice_settings
interface CustomerWithInvoiceSettings {
  id: string;
  invoice_settings?: {
    default_payment_method?: string;
  };
}

// POST /billing/pay-invoices
export const handlePayOpenInvoices = async (c: Context<{ Bindings: Env }>) => {
  try {
    const body = await c.req.json();
    console.log("Manually paying open invoices:", body);

    const { team_id } = body;

    if (!team_id) {
      return c.json({ error: "Missing required field: team_id" }, 400);
    }

    const stripeClient = initStripeClient(c.env);

    // Find the subscription for this team
    console.log("Looking for subscription for team:", team_id);
    const subscriptions = await stripeClient.subscriptions.search({
      query: `metadata['imoblr_team_id']:'${team_id}'`,
    });

    if (subscriptions.data.length === 0) {
      return c.json({ error: "No subscription found for team" }, 404);
    }

    const subscription = subscriptions.data[0];
    console.log(`Found subscription: id=${subscription.id}, status=${subscription.status}`);
    // Only log team ID for context
    const metadata = subscription.metadata || {};
    console.log(`Team ID: ${metadata.imoblr_team_id || 'N/A'}`);

    // Find any open invoices for this subscription
    console.log("Looking for open invoices for subscription:", subscription.id);
    const invoices = await stripeClient.invoices.list({
      subscription: subscription.id,
      status: 'open',
    });

    if (invoices.data.length === 0) {
      console.log("No open invoices found for subscription");
      return c.json({
        success: true,
        message: "No open invoices found to pay",
        subscription_id: subscription.id,
        subscription_status: subscription.status,
      });
    }

    console.log(`Found ${invoices.data.length} open invoice(s) for subscription`);

    // Make sure we have a default payment method
    if (!subscription.default_payment_method) {
      console.log("No default payment method found for subscription");

      // Try to get the customer's default payment method
      const customerResponse = await stripeClient.customers.retrieve(subscription.customer as string);
      const customer = customerResponse as unknown as CustomerWithInvoiceSettings;
      if (customer.invoice_settings?.default_payment_method) {
        console.log("Using customer's default payment method");

        // Update the subscription with the customer's default payment method
        await stripeClient.subscriptions.update(subscription.id, {
          default_payment_method: customer.invoice_settings.default_payment_method,
        });
      } else {
        return c.json({ error: "No default payment method found for customer" }, 400);
      }
    }

    // Pay each open invoice
    const paymentResults = [];
    for (const invoice of invoices.data) {
      console.log("Paying invoice:", invoice.id, "with amount:", invoice.amount_due);
      try {
        const paidInvoice = await stripeClient.invoices.pay(invoice.id);
        console.log("Invoice payment result:", paidInvoice.status);
        paymentResults.push({
          invoice_id: invoice.id,
          status: paidInvoice.status,
          success: true,
        });
      } catch (error) {
        const errorMessage = error && typeof error === 'object' && 'message' in error ?
          String(error.message) : 'Unknown error';
        console.error("Error paying invoice:", errorMessage);
        paymentResults.push({
          invoice_id: invoice.id,
          status: 'failed',
          success: false,
          error: errorMessage,
        });
      }
    }

    // If the subscription is still paused, resume it
    if (subscription.status === 'paused') {
      console.log("Subscription is still paused, resuming it");
      try {
        await stripeClient.subscriptions.resume(subscription.id, {
          billing_cycle_anchor: "now"
        });
        console.log("Subscription resumed successfully");
      } catch (error) {
        // If the error is because the subscription is already being resumed, that's okay
        if (error && typeof error === 'object' && 'message' in error &&
            typeof error.message === 'string' &&
            error.message.includes('only resume a subscription if it is `paused`')) {
          console.log("Subscription is already being resumed or is no longer paused");
        } else {
          const errorMessage = error && typeof error === 'object' && 'message' in error ?
            String(error.message) : 'Unknown error';
          console.error("Error resuming subscription:", errorMessage);
        }
      }
    }

    // Get the updated subscription status
    const updatedSubscription = await stripeClient.subscriptions.retrieve(subscription.id);

    return c.json({
      success: true,
      message: "Processed open invoices",
      subscription_id: subscription.id,
      previous_status: subscription.status,
      current_status: updatedSubscription.status,
      payment_results: paymentResults,
    });
  } catch (error: unknown) {
    console.error("Error paying open invoices:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to pay open invoices"
    }, 500);
  }
};
