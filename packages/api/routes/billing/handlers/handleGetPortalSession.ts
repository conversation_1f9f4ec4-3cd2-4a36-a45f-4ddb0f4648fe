import type { Env } from "@/api/types";
import type { Context } from "hono";
import { initStripeClient } from "./utils";

// GET /billing/portal-session
export const handleGetPortalSession = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { account_id, return_url } = c.req.query();

    if (!account_id) {
      return c.json({ error: "Missing required query parameter: account_id" }, 400);
    }

    if (!return_url) {
      return c.json({ error: "Missing required query parameter: return_url" }, 400);
    }

    const stripeClient = initStripeClient(c.env);

    // Find customer by account_id
    console.log("Looking for customer by account_id:", account_id);
    const customers = await stripeClient.customers.search({
      query: `metadata['imoblr_team_id']:'${account_id}'`,
    });

    if (customers.data.length === 0) {
      return c.json({ error: "No customer found for account" }, 404);
    }

    const customer = customers.data[0];
    console.log("Found customer:", customer.id);

    // Create portal session
    console.log("Creating portal session for customer:", customer.id);
    const session = await stripeClient.billingPortal.sessions.create({
      customer: customer.id,
      return_url: return_url,
    });

    return c.json({
      url: session.url,
    });
  } catch (error: unknown) {
    console.error("Error creating portal session:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to create portal session"
    }, 500);
  }
};
