import type { Env } from "@/api/types";
import type { Context } from "hono";
import { initStripeClient } from "./utils";

// GET /billing/products
export const handleGetProducts = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log("Fetching products and prices");
    const stripeClient = initStripeClient(c.env);

    // Get all active products
    const products = await stripeClient.products.list({
      active: true,
      expand: ["data.default_price"],
    });

    // Get all prices for these products with tiers information
    const prices = await stripeClient.prices.list({
      active: true,
      type: "recurring",
      expand: ["data.tiers"],
    });

    // Organize prices by product
    const productMap = new Map();
    for (const product of products.data) {
      productMap.set(product.id, {
        id: product.id,
        name: product.name,
        description: product.description,
        // Use optional chaining for features which might not exist on all products
        features: product.features as any,
        metadata: product.metadata,
        prices: [],
      });
    }

    // Add prices to their respective products
    for (const price of prices.data) {
      if (productMap.has(price.product as string)) {
        const product = productMap.get(price.product);
        product.prices.push({
          id: price.id,
          currency: price.currency,
          unit_amount: price.unit_amount,
          recurring: price.recurring,
          type: price.type,
          metadata: price.metadata,
          tiers: price.tiers,
          billing_scheme: price.billing_scheme,
          lookup_key: price.lookup_key,
        });
      }
    }

    return c.json({
      products: Array.from(productMap.values()),
    });
  } catch (error: unknown) {
    console.error("Error fetching products:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to fetch products"
    }, 500);
  }
};
