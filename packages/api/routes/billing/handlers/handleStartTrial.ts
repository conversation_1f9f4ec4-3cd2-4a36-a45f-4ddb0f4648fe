import type { Env } from "@/api/types";
import type { Context } from "hono";
import type { Customer } from "stripe";
import { initStripeClient } from "./utils";

// POST /billing/trial
export const handleStartTrial = async (c: Context<{ Bindings: Env }>) => {
  try {
    const body = await c.req.json();
    console.log("Starting trial with:", body);

    const { account_id, lookup_key, quantity = 1, email, teamName } = body;

    if (!account_id) {
      return c.json({ error: "Missing required field: account_id" }, 400);
    }

    if (!lookup_key) {
      return c.json({ error: "Missing required field: lookup_key" }, 400);
    }

    if (!email) {
      return c.json({ error: "Missing required field: email" }, 400);
    }

    if (!teamName) {
      return c.json({ error: "Missing required field: teamName" }, 400);
    }

    const stripeClient = initStripeClient(c.env);

    console.log("Looking for existing customer by account_id:", account_id);
    const existingCustomers = await stripeClient.customers.search({
      query: `metadata['imoblr_team_id']:'${account_id}'`,
    });

    let customer: Customer;
    if (existingCustomers.data.length === 0) {
      console.log("No existing customer found, creating new customer with email:", email);
      customer = await stripeClient.customers.create({
        metadata: {
          imoblr_team_id: account_id,
        },
        email: email,
        name: teamName,
      });
      console.log("Created new customer:", customer.id);
    } else {
      customer = existingCustomers.data[0];
      if (!customer.email && email) {
        customer = await stripeClient.customers.update(customer.id, {
          email: email,
        });
      }
      console.log("Found existing customer:", customer.id);
    }

    console.log("Looking up price by lookup_key:", lookup_key);
    const prices = await stripeClient.prices.list({
      lookup_keys: [lookup_key],
      expand: ["data.product"],
    });

    if (prices.data.length === 0) {
      return c.json({ error: `No price found for lookup key: ${lookup_key}` }, 400);
    }

    const price = prices.data[0];
    console.log("Found price:", price.id);

    // Create subscription with trial period, no payment required
    console.log("Creating trial subscription with price_id:", price.id);
    const subscription = await stripeClient.subscriptions.create({
      customer: customer.id,
      items: [
        {
          price: price.id,
          quantity: quantity,
        },
      ],
      trial_period_days: 7,
      trial_settings: {
        end_behavior: {
          missing_payment_method: 'pause',
        },
      },
      metadata: {
        price_id: price.id,
        imoblr_team_id: account_id,
        quantity: quantity.toString(),
        email,
      },
    });

    console.log("Created trial subscription:", subscription.id);

    return c.json({
      customerId: customer.id,
      subscriptionId: subscription.id,
      trialEnd: subscription.trial_end,
    });
  } catch (error: unknown) {
    console.error("Error starting trial:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to start trial"
    }, 500);
  }
};
