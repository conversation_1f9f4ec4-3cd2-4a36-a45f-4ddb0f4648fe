import { getTeamBillingStatus } from "@/api/services/billing";
import type { Env } from "@/api/types";
import { verifyAuthentication } from "@/api/utils";
import { createDbClient } from "@/api/utils/db";
import type { Context } from "hono";

// GET /billing/status/:teamId
export const handleGetBillingStatus = async (c: Context<{ Bindings: Env }>) => {
  try {
    const teamId = c.req.param("teamId");
    console.log("Getting billing status for team:", teamId);

    if (!teamId) {
      return c.json({ error: "Missing required parameter: teamId" }, 400);
    }

    // Verify authentication and get user ID
    const auth = await verifyAuthentication(c.req.raw, c.env);
    if (!auth.isAuthenticated || !auth.userId) {
      return c.json({ error: "Authentication required" }, 401);
    }

    // Initialize Drizzle client
    const db = createDbClient(c.env);

    // Get billing status using Drizzle ORM
    console.log("Querying database directly for team billing status:", teamId);
    const billingStatus = await getTeamBillingStatus(db, teamId, auth.userId);

    if (!billingStatus) {
      console.log("No billing data found for team:", teamId);
      return c.json({
        status: "not_setup",
        subscription_active: false,
        billing_enabled: false,
      });
    }

    console.log("Billing status data retrieved");

    // Map the database response to the expected API response format
    // This ensures compatibility with the client code
    return c.json({
      status: billingStatus.status || "not_setup",
      subscription_active: billingStatus.status === "active" || billingStatus.status === "trialing",
      billing_enabled: billingStatus.billing_enabled || false,
      subscription_id: billingStatus.subscription_id,
      billing_customer_id: billingStatus.customer_id,
      current_period_start: billingStatus.current_period_start,
      current_period_end: billingStatus.current_period_end,
      trial_end: billingStatus.trial_end,
      account_role: billingStatus.account_role,
      is_primary_owner: billingStatus.is_primary_owner,
      billing_email: billingStatus.billing_email,
      plan: billingStatus.plan_name ? {
        name: billingStatus.plan_name,
        description: `${billingStatus.plan_name} subscription`,
      } : {
        name: "No Plan",
        description: "No active subscription",
      },
      // Include any other fields that might be needed by the client
    });
  } catch (error) {
    console.error("Error getting billing status:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to get billing status"
    }, 500);
  }
};
