import type { Env } from "@/api/types";
import type { Context } from "hono";
import type { Customer } from "stripe";
import { initStripeClient } from "./utils";

// POST /billing/payment-setup
export const handleSetupPayment = async (c: Context<{ Bindings: Env }>) => {
  try {
    const body = await c.req.json();
    console.log("Setting up payment with args:", body);

    const { account_id, lookup_key, quantity = 1, subscription_id } = body;

    if (!account_id) {
      return c.json({ error: "Missing required field: account_id" }, 400);
    }

    if (!lookup_key) {
      return c.json({ error: "Missing required field: lookup_key" }, 400);
    }

    const stripeClient = initStripeClient(c.env);

    console.log("Looking for customer by account_id:", account_id);
    const existingCustomers = await stripeClient.customers.search({
      query: `metadata['imoblr_team_id']:'${account_id}'`,
    });

    let customer: Customer;
    if (existingCustomers.data.length > 0) {
      customer = existingCustomers.data[0];
      console.log("Found existing customer:", customer.id);
    } else {
      console.log("Creating new customer for account:", account_id);
      customer = await stripeClient.customers.create({
        metadata: {
          imoblr_team_id: account_id,
        },
      });
    }

    console.log("Looking up price by lookup_key:", lookup_key);
    const prices = await stripeClient.prices.search({
      query: `active:'true' AND lookup_key:'${lookup_key}'`,
    });

    if (prices.data.length === 0) {
      return c.json({ error: `No price found for lookup key: ${lookup_key}` }, 400);
    }

    const price = prices.data[0];
    console.log("Found price:", price.id);

    // If we have a subscription ID, we're updating an existing subscription
    // Otherwise, we're creating a new one
    const metadata = subscription_id
      ? {
          imoblr_team_id: account_id,
          subscription_id,
          price_id: price.id,
          quantity: quantity.toString(),
          mode: "update",
        }
      : {
          imoblr_team_id: account_id,
          price_id: price.id,
          quantity: quantity.toString(),
          mode: "new",
        };

    console.log("Creating setup intent for customer:", customer.id);
    const setupIntent = await stripeClient.setupIntents.create({
      customer: customer.id,
      payment_method_types: ["card"],
      metadata: metadata as Record<string, string>,
    });

    console.log("Created setup intent:", setupIntent.id);

    return c.json({
      clientSecret: setupIntent.client_secret,
    });
  } catch (error: unknown) {
    console.error("Error setting up payment:", error);
    return c.json({
      error: error instanceof Error ? error.message : "Failed to setup payment"
    }, 500);
  }
};
