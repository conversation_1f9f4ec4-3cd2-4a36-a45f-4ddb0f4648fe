{"extends": "../../tsconfig.base.json", "compilerOptions": {"types": ["@cloudflare/workers-types"], "experimentalDecorators": true, "esModuleInterop": true, "outDir": "./dist", "rootDir": ".", "strict": true, "baseUrl": ".", "paths": {"@/api/types": ["./types"], "@/api/types/*": ["./types/*"], "@/api/utils": ["./utils"], "@/api/utils/*": ["./utils/*"], "@/api/services": ["./services"], "@/api/services/*": ["./services/*"], "@/api/routes": ["./routes"], "@/api/routes/*": ["./routes/*"]}}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist"], "references": []}