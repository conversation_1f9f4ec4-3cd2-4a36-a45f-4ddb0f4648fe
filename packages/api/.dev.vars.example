# Supabase credentials
SUPABASE_URL=http://localhost:54321
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_JWT_SECRET=your-jwt-secret
SUPABASE_ANON_KEY=your-anon-key

# Google API key for address lookup
GOOGLE_API_KEY=your-google-api-key

# Stripe API key
STRIPE_API_KEY=your-stripe-api-key

# Stripe webhook signing secret
STRIPE_WEBHOOK_SIGNING_SECRET=your-stripe-webhook-signing-secret

# Default trial plan lookup key
DEFAULT_TRIAL_PLAN_LOOKUP_KEY=your-default-trial-plan-lookup-key

# Database connection URL for Drizzle ORM
# Format: postgres://[user]:[password]@[host]:[port]/[database]
# For local Supabase development, use port 54322 (not 5432)
# The default credentials for local Supabase are:
DATABASE_URL=postgres://postgres:postgres@localhost:54322/postgres
# If you're having issues with the connection, try adding search_path:
# DATABASE_URL=postgres://postgres:postgres@localhost:54322/postgres?search_path=public,basejump,auth

# Cloudflare-specific variables (only needed in production)
# CLOUDFLARE_ACCOUNT_ID=your-cloudflare-account-id
# CLOUDFLARE_CUSTOM_HOSTNAMES_API_TOKEN=your-cloudflare-custom-hostnames-api-token
# CLOUDFLARE_ZONE_ID=your-cloudflare-zone-id
# R2_BUCKET_URL=your-r2-bucket-url
# R2_PUBLIC_URL=your-r2-public-url
# R2_ACCESS_KEY_ID=e135917e4fdfea9cda289bd0ef3443f2
# R2_SECRET_ACCESS_KEY=b14e8c65cb040dc77472f65417ad2b86e151c500b106f63b65bc56ac8038e23f
