{"name": "api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/api", "projectType": "application", "targets": {"build": {"executor": "nx:run-commands", "options": {"cwd": "packages/api", "command": "wrangler deploy --dry-run --outdir dist"}}, "serve": {"executor": "nx:run-commands", "options": {"cwd": "packages/api", "command": "wrangler dev"}}, "deploy": {"executor": "nx:run-commands", "options": {"cwd": "packages/api", "command": "wrangler deploy"}}, "lint": {"executor": "nx:run-commands", "options": {"cwd": "packages/api", "command": "eslint ."}}}, "tags": []}