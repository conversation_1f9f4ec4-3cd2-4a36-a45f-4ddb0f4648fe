{"name": "customer-sites", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/customer-sites", "projectType": "application", "targets": {"build": {"executor": "nx:run-commands", "options": {"cwd": "packages/customer-sites", "command": "astro build"}}, "dev": {"executor": "nx:run-commands", "options": {"cwd": "packages/customer-sites", "command": "astro dev --host 0.0.0.0"}}, "preview": {"executor": "nx:run-commands", "options": {"cwd": "packages/customer-sites", "command": "astro preview"}}, "lint": {"executor": "nx:run-commands", "options": {"cwd": "packages/customer-sites", "command": "biome lint --unsafe"}}}, "tags": []}