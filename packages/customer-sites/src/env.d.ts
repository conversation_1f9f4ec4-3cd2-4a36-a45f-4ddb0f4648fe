/// <reference types="astro/client" />

declare namespace App {
  interface Locals {
    websiteInfo: {
      title: string;
      description: string;
      subdomain: string;
      domain: string;
      environment: string;
      websiteId: string | null;
      teamId: string | null;
      website?: {
      id: string;
      title: string;
      subdomain: string;
      description?: string;
      team_account_id: string;
      logo_image_url?: string;
      hero_image_url?: string;
      institutional_image_url?: string;
      theme?: {
        primary_color: string;
        secondary_color: string;
        font_family: string;
      };
      [key: string]: any; // Allow other properties
    }; // Full website object
    };
    websiteTheme: {
      primary_color: string;
      secondary_color: string;
      font_family: string;
    };
    teamProfile: {
      name: string;
      address: string;
      phone_number: string;
      whatsapp_number: string;
      instagram_url: string;
      facebook_url: string;
      youtube_url: string;
      about_us?: string;
    };
  }
}
