---
// Component for displaying the property image gallery
const { property } = Astro.props;

// Get the website theme from Astro.locals
const { websiteTheme } = Astro.locals;

// Set the primary color from theme or use a default color
const primaryColor = websiteTheme?.primary_color || '#3b82f6';
---

{property?.media && property.media.length > 0 && (
  <div class="mb-4 lg:mb-8">
    <div class="flex flex-col lg:flex-row gap-4 relative h-[calc(40vh+1rem)]">
      {/* Main large image with Swiper carousel */}
      <div class="flex-1 relative lg:rounded-lg overflow-hidden h-full">
        <div class="swiper property-swiper h-full">
          <div class="swiper-wrapper">
            {property.media.map((image: { url: string }) => (
              <div class="swiper-slide">
                <img
                  src={image.url}
                  alt={property.title}
                  class="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
          <div class="swiper-pagination"></div>
          <div class="swiper-button-prev"></div>
          <div class="swiper-button-next"></div>
        </div>
      </div>

      {/* Right side images - grid layout */}
      <div class="hidden lg:grid grid-cols-2 grid-rows-2 gap-4 h-full">
        {/* Image 1 */}
        {property.media.length > 1 ? (
          <div class="relative rounded-lg overflow-hidden h-[20vh] w-[20vh] cursor-pointer thumbnail" data-index="1">
            <img
              src={property.media[1].url}
              alt={property.title}
              class="w-full h-full object-cover"
            />
          </div>
        ) : (
          <div class="bg-gray-100 rounded-lg flex items-center justify-center h-[20vh] w-[20vh]">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        )}

        {/* Image 2 */}
        {property.media.length > 2 ? (
          <div class="relative rounded-lg overflow-hidden h-[20vh] w-[20vh] cursor-pointer thumbnail" data-index="2">
            <img
              src={property.media[2].url}
              alt={property.title}
              class="w-full h-full object-cover"
            />
          </div>
        ) : (
          <div class="bg-gray-100 rounded-lg flex items-center justify-center h-[20vh] w-[20vh]">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        )}

        {/* Image 3 */}
        {property.media.length > 3 ? (
          <div class="relative rounded-lg overflow-hidden h-[20vh] w-[20vh] cursor-pointer thumbnail" data-index="3">
            <img
              src={property.media[3].url}
              alt={property.title}
              class="w-full h-full object-cover"
            />
          </div>
        ) : (
          <div class="bg-gray-100 rounded-lg flex items-center justify-center h-[20vh] w-[20vh]">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        )}

        {/* Image 4 */}
        {property.media.length > 4 ? (
          <div class="relative rounded-lg overflow-hidden h-[20vh] w-[20vh] cursor-pointer thumbnail" data-index="4">
            <img
              src={property.media[4].url}
              alt={property.title}
              class="w-full h-full object-cover"
            />
          </div>
        ) : (
          <div class="bg-gray-100 rounded-lg flex items-center justify-center h-[20vh] w-[20vh]">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        )}
      </div>

      {/* Photo count and virtual tour button */}
      <div class="absolute top-4 md:bottom-4 md:top-[unset] left-4 flex space-x-2 z-10">
        <div class="bg-white/80 backdrop-blur-sm rounded-md px-3 py-1 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          <span>{property.media.length} fotos</span>
        </div>
        <button class="bg-white/80 backdrop-blur-sm rounded-md px-3 py-1 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
          Tur virtual
        </button>
      </div>
    </div>
  </div>
)}

<style define:vars={{ primaryColor }}>
  /* Custom styles for Swiper */
  .property-swiper .swiper-button-next,
  .property-swiper .swiper-button-prev {
    color: white;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
    z-index: 20;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .property-swiper .swiper-button-next:after,
  .property-swiper .swiper-button-prev:after {
    font-size: 18px;
  }

  .property-swiper .swiper-pagination {
    position: absolute;
    bottom: 10px;
    right: 10px;
    left: auto;
    width: auto;
    z-index: 20;
    padding: 5px 10px;
    border-radius: 20px;
    background-color: rgba(255, 255, 255, 0.5);
    display: flex;
    align-items: center;
    height: 30px;
  }

  .property-swiper .swiper-pagination-bullet {
    background: white;
    opacity: 0.7;
    width: 8px;
    height: 8px;
    margin: 0 3px;
    vertical-align: middle;
  }

  /* Increase specificity and use !important to override Swiper's default styles */
  .property-swiper .swiper-pagination .swiper-pagination-bullet-active,
  .property-swiper .swiper-pagination .custom-bullet-active {
    opacity: 1 !important;
    background: var(--bullet-color, var(--primaryColor)) !important;
  }

  .property-swiper .swiper-slide {
    height: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* Thumbnail hover effect */
  .thumbnail {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin: 0;
  }

  .thumbnail:hover {
    transform: scale(1.03);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 2px var(--primaryColor);
  }
</style>

<script is:inline>
  // Initialize Swiper when the DOM is fully loaded
  document.addEventListener('DOMContentLoaded', function() {

    // Initialize the main Swiper
    const propertySwiper = new Swiper('.property-swiper', {
      // Optional parameters
      loop: true,
      autoplay: {
        delay: 5000,
        disableOnInteraction: false,
      },
      effect: 'slide',
      speed: 800,
      on: {
        init: function() {
          // Apply primary color to active bullet on initialization
          setTimeout(() => {
            document.querySelectorAll('.property-swiper .swiper-pagination-bullet-active').forEach(bullet => {
              bullet.style.backgroundColor = 'var(--primaryColor)';
            });
          }, 100);
        },
      },

      // If we need pagination
      pagination: {
        el: '.swiper-pagination',
        clickable: true,
      },

      // Navigation arrows
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
      },
    });

    // Add click event listeners to thumbnails
    const thumbnails = document.querySelectorAll('.thumbnail');
    thumbnails.forEach(thumbnail => {
      thumbnail.addEventListener('click', function() {
        const index = parseInt(this.getAttribute('data-index'), 10);
        if (!isNaN(index)) {
          // Slide to the corresponding slide
          propertySwiper.slideTo(index);

          // Pause autoplay for a moment
          propertySwiper.autoplay.stop();
          setTimeout(() => {
            propertySwiper.autoplay.start();
          }, 5000);
        }
      });
    });

    // Apply custom styles to active pagination bullet
    propertySwiper.on('slideChange', function () {
      // Force the active bullet to use the primary color
      document.querySelectorAll('.property-swiper .swiper-pagination-bullet-active').forEach(bullet => {
        bullet.style.backgroundColor = 'var(--primaryColor)';
      });
    });

    // Also apply on initial load
    document.querySelectorAll('.property-swiper .swiper-pagination-bullet-active').forEach(bullet => {
      bullet.style.backgroundColor = 'var(--primaryColor)';
    });
  });
</script>
