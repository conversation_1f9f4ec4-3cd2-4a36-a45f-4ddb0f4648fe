---
// Component for displaying the building amenities
const { property } = Astro.props;
---

{property?.buildingAmenities && Object.values(property.buildingAmenities).some(value => value) && (
  <div class="mb-8">
    <h2 class="font-semibold mb-4">Comodidades do condomínio</h2>
    <div>
      <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
        {Object.entries(property.buildingAmenities).map(([key, value]) => {
          if (value && key !== 'id' && key !== 'property_id') {
            // Convert snake_case to Title Case and handle special cases
            let label = key
              .split('_')
              .map(word => word.charAt(0).toUpperCase() + word.slice(1))
              .join(' ');

            // Handle special cases
            if (key === 'shared_pool') label = 'Piscina';
            if (key === 'shared_barbecue_grill') label = 'Churrasqueira';
            if (key === 'shared_garage') label = 'Garagem';
            if (key === 'shared_gourmet_space') label = 'Espaço Gourmet';
            if (key === 'shared_laundry') label = 'Lavanderia';
            if (key === 'shared_sauna') label = 'Sauna';
            if (key === 'shared_spa') label = 'Spa';
            if (key === 'shared_security_system') label = 'Sistema de Segurança';
            if (key === 'shared_water_heating') label = 'Aquecimento de Água';
            if (key === 'gated_community') label = 'Condomínio Fechado';
            if (key === 'private_security') label = 'Segurança Privada';
            if (key === 'party_room') label = 'Salão de Festas';
            if (key === 'game_room') label = 'Salão de Jogos';
            if (key === 'sports_area') label = 'Área de Esportes';
            if (key === 'tennis_court') label = 'Quadra de Tênis';
            if (key === 'green_area') label = 'Área Verde';
            if (key === 'bicycle_storage') label = 'Bicicletário';
            if (key === 'power_generator') label = 'Gerador de Energia';

            return (
              <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <span>{label}</span>
              </div>
            );
          }
          return null;
        })}
      </div>
    </div>
  </div>
)}
