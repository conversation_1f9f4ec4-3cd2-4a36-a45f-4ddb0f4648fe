---
// Component for displaying the property details
const { property } = Astro.props;

// Get the website theme from Astro.locals
const { websiteTheme } = Astro.locals;

// Set the primary color from theme or use a default color
const primaryColor = websiteTheme?.primary_color || '#e11d48';
---

<div class="mb-8">
  <div class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-0.5">
    {property?.totalArea && (
      <div class="flex items-center px-4 py-3 bg-gray-400/5 rounded-sm">
        <svg class="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={`color: ${primaryColor}`}>
          <path d="M2 6C2.1305 4.6645 2.4262 3.7663 3.09625 3.09625C3.7663 2.4262 4.6645 2.1305 6 2M6 22C4.6645 21.8695 3.7663 21.5738 3.09625 20.9037C2.4262 20.2337 2.1305 19.3355 2 18M22 6C21.8695 4.6645 21.5738 3.7663 20.9037 3.09625C20.2337 2.4262 19.3355 2.1305 18 2M2 10V14M14 2H10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M13.7083 10.7393C14.0363 10.7393 14.3021 11.0051 14.3021 11.333V20.833C14.3021 21.1609 14.0363 21.4268 13.7083 21.4268H7.375C7.04708 21.4268 6.78125 21.1609 6.78125 20.833V12.0116C6.78125 11.6837 7.04708 11.4178 7.375 11.4178C7.70292 11.4178 7.96875 11.6837 7.96875 12.0116V20.2393H13.1146V11.333C13.1146 11.0051 13.3804 10.7393 13.7083 10.7393Z" fill="currentColor"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M21.6247 11.5312C21.9526 11.5312 22.2184 11.7971 22.2184 12.125V20.8333C22.2184 21.1613 21.9526 21.4271 21.6247 21.4271H13.708C13.3801 21.4271 13.1143 21.1613 13.1143 20.8333C13.1143 20.5054 13.3801 20.2396 13.708 20.2396H21.0309V12.125C21.0309 11.7971 21.2968 11.5312 21.6247 11.5312Z" fill="currentColor"/>
        </svg>
        <div>
          <p class="text-sm">{property.totalArea}m² total</p>
        </div>
      </div>
    )}

    {property?.rooms !== undefined && (
      <div class="flex items-center px-4 py-3 bg-gray-400/5 rounded-sm">
        <svg class="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={`color: ${primaryColor}`}>
          <path d="M22 17.5H2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M22 21V16C22 14.1144 22 13.1716 21.4142 12.5858C20.8284 12 19.8856 12 18 12H6C4.11438 12 3.17157 12 2.58579 12.5858C2 13.1716 2 14.1144 2 16V21" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M11 12V10.2134C11 9.83272 10.9428 9.70541 10.6497 9.55538C10.0395 9.24292 9.29865 9 8.5 9C7.70135 9 6.96055 9.24292 6.35025 9.55538C6.05721 9.70541 6 9.83272 6 10.2134L6 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          <path d="M18 12V10.2134C18 9.83272 17.9428 9.70541 17.6497 9.55538C17.0395 9.24292 16.2987 9 15.5 9C14.7013 9 13.9605 9.24292 13.3503 9.55538C13.0572 9.70541 13 9.83272 13 10.2134L13 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          <path d="M21 12V7.36057C21 6.66893 21 6.32311 20.8079 5.99653C20.6157 5.66995 20.342 5.50091 19.7944 5.16283C17.5869 3.79978 14.8993 3 12 3C9.10067 3 6.41314 3.79978 4.20558 5.16283C3.65804 5.50091 3.38427 5.66995 3.19213 5.99653C3 6.32311 3 6.66893 3 7.36057V12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
        </svg>
        <div>
          <p class="text-sm">{property.rooms} {property.rooms === 1 ? 'quarto' : 'quartos'}</p>
        </div>
      </div>
    )}

    {property?.bathrooms !== undefined && (
      <div class="flex items-center px-4 py-3 bg-gray-400/5 rounded-sm">
        <svg class="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={`color: ${primaryColor}`}>
          <path d="M8 11H17.1351C18.6001 11 19.3326 11 19.7749 11.7353C20.2172 12.4706 19.9567 12.9393 19.4357 13.8769C18.4017 15.738 16.3955 17 14.0901 17C12.5456 17 11.1353 16.4335 10.0618 15.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M8 11V4C8 3.05719 8 2.58579 7.70711 2.29289C7.41421 2 6.94281 2 6 2C5.05719 2 4.58579 2 4.29289 2.29289C4 2.58579 4 3.05719 4 4V11C4 11.9428 4 12.4142 4.29289 12.7071C4.58579 13 5.05719 13 6 13C6.94281 13 7.41421 13 7.70711 12.7071C8 12.4142 8 11.9428 8 11Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M7 7L10 7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M16 17C15 18 16 21 17.9996 22H4C5 21 6.7 17.8 5.5 13" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <div>
          <p class="text-sm">{property.bathrooms} {property.bathrooms === 1 ? 'banheiro' : 'banheiros'}</p>
        </div>
      </div>
    )}

    {property?.parkingSpots !== undefined && (
      <div class="flex items-center px-4 py-3 bg-gray-400/5 rounded-sm">
        <svg class="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={`color: ${primaryColor}`}>
          <path d="M2 8.5L4 10L5.76114 10.4403C5.91978 10.4799 6.08269 10.5 6.24621 10.5H17.7538C17.9173 10.5 18.0802 10.4799 18.2389 10.4403L20 10L22 8.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M18 14V14.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M6 14V14.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M3 8.5V19C3 19.9428 3 20.4142 3.29289 20.7071C3.58579 21 4.05719 21 5 21H19C19.9428 21 20.4142 21 20.7071 20.7071C21 20.4142 21 19.9428 21 19V8.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
          <path d="M8 3.5L8.53513 5.60528C8.84255 6.96598 9.84648 8 11 8H13C14.1535 8 15.1575 6.96598 15.4649 5.60528L16 3.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <div>
          <p class="text-sm">{property.parkingSpots} {property.parkingSpots === 1 ? 'vaga' : 'vagas'}</p>
        </div>
      </div>
    )}

    {property?.floor !== undefined && property?.floor && (
      <div class="flex items-center px-4 py-3 bg-gray-400/5 rounded-sm">
        <svg class="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={`color: ${primaryColor}`}>
          <path d="M2 22H22" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          <path d="M18 9H14C11.518 9 11 9.518 11 12V22H21V12C21 9.518 20.482 9 18 9Z" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round" />
          <path d="M15 22H3V5C3 2.518 3.518 2 6 2H12C14.482 2 15 2.518 15 5V9" stroke="currentColor" stroke-width="1.5" stroke-linejoin="round" />
          <path d="M3 6H6M3 10H6M3 14H6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          <path d="M15 13H17M15 16H17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          <path d="M16 22L16 19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <div>
          <p class="text-sm">{property.floor}º andar</p>
        </div>
      </div>
    )}

    {property?.petsAllowed !== undefined && property.petsAllowed && (
      <div class="flex items-center px-4 py-3 bg-gray-400/5 rounded-sm">
        <svg class="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={`color: ${primaryColor}`}>
          <path d="M4.5 15.5C4.5 15.5 3 15.5 3 14C3 12.5 4.5 9.5 4.5 9.5C4.5 9.5 6 9.5 6 11C6 12.5 4.5 15.5 4.5 15.5Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M9 13.5C9 13.5 7.5 13.5 7.5 12C7.5 10.5 9 7.5 9 7.5C9 7.5 10.5 7.5 10.5 9C10.5 10.5 9 13.5 9 13.5Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M13.5 13.5C13.5 13.5 12 13.5 12 12C12 10.5 13.5 7.5 13.5 7.5C13.5 7.5 15 7.5 15 9C15 10.5 13.5 13.5 13.5 13.5Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M18 15.5C18 15.5 16.5 15.5 16.5 14C16.5 12.5 18 9.5 18 9.5C18 9.5 19.5 9.5 19.5 11C19.5 12.5 18 15.5 18 15.5Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M11.5 20C14.5376 20 17 18.5376 17 16.5C17 14.4624 14.5376 13 11.5 13C8.46243 13 6 14.4624 6 16.5C6 18.5376 8.46243 20 11.5 20Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <div>
          <p class="text-sm">Aceita pet</p>
        </div>
      </div>
    )}

    {property?.builtArea && (
      <div class="flex items-center px-4 py-3 bg-gray-400/5 rounded-sm">
        <svg class="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={`color: ${primaryColor}`}>
          <path d="M2 22H22" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          <path d="M17 22V6C17 4.11438 17 3.17157 16.4142 2.58579C15.8284 2 14.8856 2 13 2H11C9.11438 2 8.17157 2 7.58579 2.58579C7 3.17157 7 4.11438 7 6V22" stroke="currentColor" stroke-width="1.5" />
          <path d="M17 6H21V16C21 17.8856 21 18.8284 20.4142 19.4142C19.8284 20 18.8856 20 17 20" stroke="currentColor" stroke-width="1.5" />
          <path d="M7 16H3V20C3 20.9428 3 21.4142 3.29289 21.7071C3.58579 22 4.05719 22 5 22" stroke="currentColor" stroke-width="1.5" />
          <path d="M12 22V19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          <path d="M12 12V15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          <path d="M12 5V8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
        </svg>
        <div>
          <p class="text-sm">{property.builtArea}m²</p>
        </div>
      </div>
    )}

    {property?.furnished !== undefined && property.furnished && (
      <div class="flex items-center px-4 py-3 bg-gray-400/5 rounded-sm">
        <svg class="w-6 h-6 mr-3" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={`color: ${primaryColor}`}>
          <path d="M4 19V5C4 3.89543 4.89543 3 6 3H18C19.1046 3 20 3.89543 20 5V19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          <path d="M1 19H23" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          <path d="M7 19V16C7 14.8954 7.89543 14 9 14H15C16.1046 14 17 14.8954 17 16V19" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          <path d="M12 14V11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
          <path d="M8 8H16" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
        </svg>
        <div>
          <p class="text-sm">Mobiliado</p>
        </div>
      </div>
    )}
  </div>
</div>
