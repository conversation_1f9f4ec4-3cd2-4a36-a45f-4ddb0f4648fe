---
// Component for displaying the property prices
import { formatPrice } from '../../../../utils/formatPrice.js';

const { property } = Astro.props;
---

<div class="p-6 lg:p-8">
  {/* Display a message if no prices are available */}
  {(!property?.prices?.sale &&
    !property?.prices?.rent &&
    !property?.prices?.condominium &&
    !property?.prices?.iptu) ? (
    <p class="text-gray-500">Valores indisponíveis</p>
  ) : (
    <>
      {/* Display formatted prices */}
      {property?.prices?.sale && (
        <div class="mb-4 flex items-center flex-row justify-between">
          <p class="text-gray-500 font-bold text-lg">Venda</p>
          <p class="text-lg font-bold text-primary">{formatPrice(property.prices.sale)}</p>
        </div>
      )}
      {property?.prices?.rent && (
        <div class="mb-4 flex items-center flex-row justify-between">
          <p class="text-gray-500 font-bold text-lg">Aluguel</p>
          <p class="text-lg font-bold text-primary">{formatPrice(property.prices.rent)}</p>
        </div>
      )}
      {property?.prices?.condominium && (
        <div class="mb-2 flex items-center flex-row justify-between">
          <p class="text-gray-500 text-sm">Condomínio</p>
          <p class="font-medium text-sm">{formatPrice(property.prices.condominium)}</p>
        </div>
      )}
      {property?.prices?.iptu && (
        <div class="mb-2 flex items-center flex-row justify-between">
          <p class="text-gray-500 text-sm">IPTU</p>
          <p class="font-medium text-sm">{formatPrice(property.prices.iptu)}</p>
        </div>
      )}
    </>
  )}
</div>
