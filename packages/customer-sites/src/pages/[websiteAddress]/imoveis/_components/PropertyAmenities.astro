---
// Component for displaying the property amenities
const { property } = Astro.props;
---

{property?.amenities && Object.values(property.amenities).some(value => value) && (
  <div class="mb-8">
    <h2 class="font-semibold mb-4">Comodidades do imóvel</h2>
    <div>
      <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
        {Object.entries(property.amenities).map(([key, value]) => {
          if (value && key !== 'id' && key !== 'property_id') {
            // Convert snake_case to Title Case and handle special cases
            let label = key
              .split('_')
              .map(word => word.charAt(0).toUpperCase() + word.slice(1))
              .join(' ');

            // Handle special cases
            if (key === 'barbecue_grill') label = 'Churrasqueira';
            if (key === 'gourmet_space') label = 'Espaço Gourmet';
            if (key === 'garden') label = 'Jardim';
            if (key === 'pool') label = 'Piscina';
            if (key === 'backyard') label = 'Quintal';
            if (key === 'water_heating') label = 'Aquecimento de Água';
            if (key === 'heating') label = 'Aquecimento';
            if (key === 'air_conditioning') label = 'Ar Condicionado';
            if (key === 'internet') label = 'Internet';
            if (key === 'garage') label = 'Garagem';
            if (key === 'fireplace') label = 'Lareira';
            if (key === 'laundry') label = 'Lavanderia';
            if (key === 'sauna') label = 'Sauna';
            if (key === 'spa') label = 'Spa';
            if (key === 'security_system') label = 'Sistema de Segurança';

            return (
              <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                <span>{label}</span>
              </div>
            );
          }
          return null;
        })}
      </div>
    </div>
  </div>
)}
