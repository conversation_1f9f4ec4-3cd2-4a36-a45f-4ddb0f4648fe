---
// Component for displaying the property header with title, location, and navigation buttons
const { property } = Astro.props;
---

<div class="flex justify-between items-center mb-2">
  <!-- <a href="/imoveis/" class="flex text-sm items-center text-gray-400 hover:text-gray-600 transition-colors">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
    </svg>
    Voltar para lista de imóveis
  </a> -->
  <!-- <div class="flex space-x-2">
    <button class="border bg-white cursor-pointer rounded-md px-4 py-2 flex items-center hover:bg-gray-50 transition-colors">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
      </svg>
      Share
    </button>
    <button class="border bg-white cursor-pointer rounded-md px-4 py-2 flex items-center hover:bg-gray-50 transition-colors">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
      </svg>
      Favourite
    </button>
  </div> -->
</div>

<h1 class="text-xl md:text-2xl lg:text-3xl font-bold mb-1">{property?.title || 'Detalhes do imóvel'}</h1>

{property?.location && (
  <p class="text-gray-500 mb-6 text-xs md:text-sm lg:text-base">
    <span class="inline-block mr-1">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block -mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    </span>
    {property.location}
  </p>
)}
