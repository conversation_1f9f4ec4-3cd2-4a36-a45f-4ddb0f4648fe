---
import BaseLayout from '../../../layouts/BaseLayout.astro';

// Import components
import PropertyHeader from './_components/PropertyHeader.astro';
import PropertyImageGallery from './_components/PropertyImageGallery.astro';
import PropertyDetails from './_components/PropertyDetails.astro';
import PropertyPrices from './_components/PropertyPrices.astro';
import PropertyDescription from './_components/PropertyDescription.astro';
import PropertyAmenities from './_components/PropertyAmenities.astro';
import BuildingAmenities from './_components/BuildingAmenities.astro';
import ContactSection from './_components/ContactSection.astro';
import Container from '@/components/Container.astro';

export const prerender = false;

// Get the property ID and website address from the URL parameters
const { propertyId, websiteAddress } = Astro.params;

// Fetch property data from API
let property = null;
let error = null;

try {
  if (propertyId) {
    // Get the current URL to construct an absolute URL
    const currentUrl = new URL(Astro.request.url);
    const baseUrl = `${currentUrl.protocol}//${currentUrl.host}`;

    // Use the detailed property API endpoint at the root level
    const apiUrl = `${baseUrl}/api/get-property-details?propertyId=${propertyId}`;
    console.log(`Fetching property data from API: ${apiUrl}`);

    try {
      const response = await fetch(apiUrl);
      if (response.ok) {
        const data = await response.json();
        console.log('API response:', data);
        property = data.property;
        console.log('Property data:', property);
      } else {
        const errorText = await response.text();
        console.error(`API error response: ${errorText}`);
        error = `Failed to fetch property data: ${response.status} ${response.statusText}`;
      }
    } catch (apiError: unknown) {
      console.error('API error:', apiError);
      error = `Error fetching property data: ${apiError instanceof Error ? apiError.message : 'Unknown error'}`;
    }
  } else {
    error = 'Property ID is required';
    console.error(error);
  }
} catch (e: unknown) {
  error = `Error fetching property data: ${e instanceof Error ? e.message : 'Unknown error'}`;
  console.error(error);
}
---

<BaseLayout>
  <main>
    {error ? (
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
        <p>{error}</p>
        <p class="mt-2">
          <a href="/" class="text-blue-500 hover:underline">Voltar para página inicial</a>
        </p>
      </div>
    ) : (
      <div>
        {/* Property Header */}
        <Container class="mt-2">
          <PropertyHeader property={property} />
        </Container>

        {/* Property Image Gallery */}
        <Container class="px-0 lg:px-4">
          <PropertyImageGallery property={property} />
        </Container>

        {/* Property Prices and Details */}
        <Container class="flex flex-col lg:flex-row">
          <div class="flex-1">
            <PropertyDetails property={property} />

            {/* Property Description */}
            <PropertyDescription property={property} />

            {/* Property Amenities */}
            <PropertyAmenities property={property} />

            {/* Building Amenities */}
            <BuildingAmenities property={property} />
          </div>

          <aside class="w-full lg:w-[calc(40vh+1rem)] m-0 lg:ml-[1rem] order-first lg:order-last">
            <div class="sticky top-8 mb-8 bg-white border border-gray-200 rounded-lg">
              <PropertyPrices property={property} />
              <div class="p-6 lg:p-8 border-t border-gray-200">
                <button class="w-full cursor-pointer text-white font-bold px-4 py-3 rounded-md hover:opacity-80 transition-colors" style={`background-color: ${Astro.locals.websiteTheme?.primary_color};`}>
                  Tenho interesse
                </button>
              </div>
            </div>
          </aside>

        </Container>

        {/* Contact Section */}
        <Container>
          <ContactSection />
        </Container>
      </div>
    )}
  </main>
</BaseLayout>
