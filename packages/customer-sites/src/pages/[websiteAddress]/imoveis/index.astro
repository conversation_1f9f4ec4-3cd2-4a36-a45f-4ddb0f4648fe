---
import BaseLayout from '../../../layouts/BaseLayout.astro';
import { formatPrice } from '../../../utils/formatPrice.js';

// Property type mapping from codes to display names
const PROPERTY_TYPE_VALUE_LABEL_MAP = {
  apartment: "Apartamento",
  house: "Casa",
  condominium_house: "Casa de Condomínio",
  penthouse: "Cobertura",
  flat: "Flat",
  studio: "Kitnet / Conjugado",
  lot: "Lote / Terreno",
  townhouse: "Sobrado",
  residential_building: "Edifício Residencial",
  rural_property: "Fazenda / Sítios / Chácaras",
  medical_office: "Consultório",
  warehouse: "Galpão / Depósito / Armazém",
  commercial_property: "Imóvel Comercial",
  commercial_lot: "Lote / Terreno Comercial",
  store: "Ponto Comercial / Loja / Box",
  office: "Sala/Conjunto",
  commercial_building: "Prédio/casa comercial",
};

export const prerender = false;

// Get the website address from the URL parameters
const { websiteAddress } = Astro.params;

// Access the team profile data from Astro.locals
const { teamProfile, websiteInfo } = Astro.locals;

// Get query parameters for filtering
const url = new URL(Astro.request.url);
const locationParam = url.searchParams.get('location');
const typeParam = url.searchParams.get('type');

// Log search parameters for debugging
console.log('Search params:', { location: locationParam, type: typeParam });

// Fetch team properties from API
let properties = [];
let error = null;

try {
  // Try to get the team ID from different sources
  const teamId = websiteInfo?.teamId || websiteInfo?.website?.team_account_id;

  if (teamId) {
    // Get the current URL to construct an absolute URL
    const currentUrl = new URL(Astro.request.url);
    const baseUrl = `${currentUrl.protocol}//${currentUrl.host}`;
    const apiUrl = `${baseUrl}/api/get-team-properties-by-team-id?teamId=${teamId}&limit=20`;

    try {
      const propertiesResponse = await fetch(apiUrl);

      if (propertiesResponse.ok) {
        const data = await propertiesResponse.json();

        if (data.properties && data.properties.length > 0) {
          // Store the properties
          properties = data.properties;
          const allProperties = properties;
          console.log(`Received ${allProperties.length} properties from API`);

          // Filter properties based on query parameters if they exist
          let filteredProperties = allProperties;

          // Apply filtering if we have filter parameters
          if (locationParam || typeParam) {
            // Filter by location if location parameter exists
            if (locationParam && locationParam !== 'none') {
              // Location parameter is in format "cityId-neighborhoodId"
              // We're not using the neighborhood ID for filtering yet
              locationParam.split('-');

              // Filter properties by neighborhood ID
              filteredProperties = filteredProperties.filter((property: any) => {
                // Check if the property has address information with the right neighborhood ID
                if (!property.neighborhood) {
                  return false;
                }

                // We need to check the raw property data to get the neighborhood ID
                // This is a workaround since we don't have the neighborhood ID in the formatted properties
                // In a real implementation, we would include the neighborhood ID in the formatted properties
                return true; // For now, we'll just return true to avoid filtering out properties
              });
            }

            // Filter by property type if type parameter exists
            if (typeParam && typeParam !== 'none') {
              // Filter properties by type
              filteredProperties = filteredProperties.filter((property: any) => {
                // Check if the property has type information
                if (!property.type) {
                  return false;
                }

                return property.type === typeParam;
              });
            }
          }

          properties = filteredProperties;
          console.log(`Found ${properties.length} properties after filtering`);
        } else {
          console.log('No properties found for team');
        }
      } else {
        error = `Error response from properties API: ${propertiesResponse.status}`;
        console.error(error);
      }
    } catch (fetchError: any) {
      error = `Fetch error: ${fetchError.message}`;
      console.error('Fetch error:', fetchError);
    }
  } else {
    error = 'No team ID available from any source';
    console.log(error);
  }
} catch (error) {
  console.error('Error fetching team properties:', error);
}
---

<BaseLayout>
  <main class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-4 text-center">Nossos Imóveis</h1>

    {/* Display active filters if any */}
    {(locationParam || typeParam) && (
      <div class="flex flex-wrap justify-center gap-2 mb-6">
        <span class="text-gray-600">Filtros ativos:</span>
        {locationParam && locationParam !== 'none' && (
          <span class="bg-gray-100 px-3 py-1 rounded-full text-sm flex items-center">
            Localização
            <a href={`/${websiteAddress}/imoveis${typeParam ? `?type=${typeParam}` : ''}`} class="ml-2 text-gray-500 hover:text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </a>
          </span>
        )}
        {typeParam && typeParam !== 'none' && (
          <span class="bg-gray-100 px-3 py-1 rounded-full text-sm flex items-center">
            {PROPERTY_TYPE_VALUE_LABEL_MAP[typeParam as keyof typeof PROPERTY_TYPE_VALUE_LABEL_MAP] || typeParam}
            <a href={`/${websiteAddress}/imoveis${locationParam ? `?location=${locationParam}` : ''}`} class="ml-2 text-gray-500 hover:text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </a>
          </span>
        )}
        {(locationParam || typeParam) && (
          <a href={`/${websiteAddress}/imoveis`} class="text-blue-500 hover:underline text-sm flex items-center">
            Limpar todos
          </a>
        )}
      </div>
    )}

    {error && (
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
        <p>{error}</p>
      </div>
    )}

    {properties.length === 0 && !error && (
      <div class="text-center py-12">
        <p class="text-gray-600 text-lg">Nenhum imóvel encontrado.</p>
      </div>
    )}

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {properties.map((property: any) => (
        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
          <a href={`/${websiteAddress}/imoveis/${property.id}`} class="block">
            <div class="h-48 bg-gray-200 relative">
              {property.images && property.images.length > 0 ? (
                <img
                  src={property.images[0]}
                  alt={property.title}
                  class="w-full h-full object-cover"
                />
              ) : (
                <div class="w-full h-full flex items-center justify-center bg-gray-200">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              )}
              <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                <span class="text-white font-semibold">
                  {property.transaction_type === 'sale' ? 'Venda' :
                   property.transaction_type === 'rent' ? 'Aluguel' :
                   'Venda/Aluguel'}
                </span>
              </div>
            </div>
            <div class="p-4">
              <h2 class="text-xl font-semibold mb-2 line-clamp-1">{property.title}</h2>
              <p class="text-gray-600 mb-2 line-clamp-1">{property.neighborhood}, {property.city}</p>
              <div class="flex justify-between items-center">
                <div>
                  {property.transaction_type === 'sale' || property.transaction_type === 'both' ? (
                    <p class="font-bold text-lg">
                      {property.sale_price ? formatPrice(property.sale_price) : 'Preço sob consulta'}
                    </p>
                  ) : null}
                  {property.transaction_type === 'rent' || property.transaction_type === 'both' ? (
                    <p class="text-gray-700">
                      {property.rent_price ? `${formatPrice(property.rent_price)}/mês` : 'Aluguel sob consulta'}
                    </p>
                  ) : null}
                </div>
                <div class="flex space-x-3 text-gray-600">
                  {property.bedrooms ? (
                    <span class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                      </svg>
                      {property.bedrooms}
                    </span>
                  ) : null}
                  {property.bathrooms ? (
                    <span class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      {property.bathrooms}
                    </span>
                  ) : null}
                  {property.area ? (
                    <span class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                      </svg>
                      {property.area}m²
                    </span>
                  ) : null}
                </div>
              </div>
            </div>
          </a>
        </div>
      ))}
    </div>
  </main>
</BaseLayout>
