---
import BaseLayout from '../../layouts/BaseLayout.astro';
import Container from '@/components/Container.astro';
import { formatToPhone } from 'brazilian-values';

export const prerender = false;

// Get the website address from the URL parameters
const { websiteAddress } = Astro.params;

// Access the team profile data from Astro.locals
const { teamProfile, websiteInfo } = Astro.locals;

// Format the phone number if available
const formattedPhone = teamProfile?.phone ? formatToPhone(teamProfile.phone) : null;

// Format the WhatsApp number if available
const formattedWhatsApp = teamProfile?.whatsapp ? formatToPhone(teamProfile.whatsapp) : null;
---

<BaseLayout>
  <main>
    <Container class="py-12">
      <h1 class="text-3xl font-bold mb-8 text-center">Sobre Nós</h1>
      
      <div class="flex flex-col md:flex-row gap-8">
        {/* Team Image */}
        <div class="md:w-1/3">
          {teamProfile?.institutional_image_url ? (
            <img 
              src={teamProfile.institutional_image_url} 
              alt={`Equipe ${teamProfile.name}`}
              class="w-full h-auto rounded-lg shadow-md"
            />
          ) : (
            <div class="w-full h-64 bg-gray-200 rounded-lg flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
          )}
        </div>
        
        {/* Team Information */}
        <div class="md:w-2/3">
          <h2 class="text-2xl font-semibold mb-4">{teamProfile?.name}</h2>
          
          {teamProfile?.about_us ? (
            <div class="prose max-w-none mb-6">
              <p>{teamProfile.about_us}</p>
            </div>
          ) : (
            <p class="text-gray-600 italic mb-6">Informações sobre nossa equipe em breve.</p>
          )}
          
          {/* Contact Information */}
          <div class="bg-gray-50 p-6 rounded-lg shadow-sm">
            <h3 class="text-xl font-semibold mb-4">Informações de Contato</h3>
            
            <div class="space-y-3">
              {teamProfile?.email && (
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <a href={`mailto:${teamProfile.email}`} class="text-blue-600 hover:underline">{teamProfile.email}</a>
                </div>
              )}
              
              {formattedPhone && (
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                  </svg>
                  <a href={`tel:${teamProfile.phone}`} class="hover:underline">{formattedPhone}</a>
                </div>
              )}
              
              {formattedWhatsApp && (
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  <a 
                    href={`https://wa.me/${teamProfile.whatsapp.replace(/\D/g, '')}`} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    class="text-green-600 hover:underline"
                  >
                    {formattedWhatsApp}
                  </a>
                </div>
              )}
              
              {teamProfile?.address && (
                <div class="flex items-start mt-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-600 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <address class="not-italic">
                    {teamProfile.address}
                  </address>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Container>
  </main>
</BaseLayout>
