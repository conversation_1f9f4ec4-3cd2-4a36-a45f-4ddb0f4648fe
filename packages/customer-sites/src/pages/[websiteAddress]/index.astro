---
import BaseLayout from '../../layouts/BaseLayout.astro';
// Component Imports
import Hero from '../../components/Hero.astro';
import FeaturedProperties from '../../components/FeaturedProperties.astro';
import { PopularNeighborhoods } from '../../components/PopularNeighborhoods';
import AboutUsPreview from '../../components/AboutUsPreview.astro';

export const prerender = false;

// Get the website address from the URL parameters
const { websiteAddress } = Astro.params;

// Access the website data from Astro.locals
const { websiteInfo, websiteTheme, teamProfile } = Astro.locals;

// If no website data is found, this will be handled by the middleware
---

<BaseLayout>
    <div class="relative">
        <!-- Hero Component -->
        <Hero />
    </div>

    <!-- Main Content -->
    <main>
        <!-- Featured Properties Section -->
        <FeaturedProperties />

        <!-- About Us Preview Section -->
        <AboutUsPreview />

        <!-- Popular Neighborhoods Section -->
        <PopularNeighborhoods
            client:load
            teamId={websiteInfo?.teamId}
            primaryColor={websiteTheme?.primary_color || '#3B82F6'}
        />
    </main>
</BaseLayout>
