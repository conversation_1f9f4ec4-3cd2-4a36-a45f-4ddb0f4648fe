---
import BaseLayout from '../layouts/BaseLayout.astro';

export const prerender = true;

// Get the URL to determine if it's a domain or subdomain issue
const url = Astro.url;
const hostname = url.hostname;
const isDomain = hostname.includes('.');
const identifier = isDomain ? hostname : hostname.split('.')[0];

// Get the website address from the x-website-address header
const headers = Astro.request.headers;
const websiteAddress = headers.get('x-website-address') || 'default';

// Default error message
let title = "Página não encontrada";
let message = "A página que você está procurando não existe.";
let explanation = "Verifique se o endereço foi digitado corretamente ou volte para a página inicial.";

// Check if this is a website not found error (from headers or query params)
const searchParams = url.searchParams;
const errorType = searchParams.get('error');

if (errorType === 'website-not-found') {
  title = "Site não encontrado";
  message = isDomain
    ? `O domínio "${identifier}" não está registrado no nosso sistema.`
    : `O subdomínio "${identifier}" não está registrado no nosso sistema.`;
  explanation = "Verifique se o endereço foi digitado corretamente ou entre em contato com o administrador.";
} else if (errorType === 'website-not-published') {
  title = "Site não publicado";
  message = isDomain
    ? `O site associado ao domínio "${identifier}" existe, mas ainda não foi publicado.`
    : `O site associado ao subdomínio "${identifier}" existe, mas ainda não foi publicado.`;
  explanation = "Entre em contato com o administrador do site para solicitar a publicação.";
}
---

<BaseLayout>
  <main class="container mx-auto px-4 py-16 flex flex-col items-center justify-center min-h-[70vh]">
    <div class="text-center max-w-2xl mx-auto">
      <h1 class="text-6xl font-bold text-gray-800 mb-4">404</h1>
      <h2 class="text-2xl font-semibold text-gray-700 mb-6">{title}</h2>

      <div class="bg-white p-8 rounded-lg shadow-md">
        <p class="text-lg text-gray-600 mb-4">{message}</p>
        <p class="text-gray-500 mb-8">{explanation}</p>

        <div class="flex flex-col sm:flex-row justify-center gap-4">
          <a
            href={`/${websiteAddress}`}
            class="px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors"
          >
            Voltar para a página inicial
          </a>

          <a
            href="https://imoblr.com.br/contato"
            target="_blank"
            class="px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-md hover:bg-gray-300 transition-colors"
          >
            Contatar suporte
          </a>
        </div>
      </div>
    </div>
  </main>
</BaseLayout>
