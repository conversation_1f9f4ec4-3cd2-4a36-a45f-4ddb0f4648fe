import { createClient } from '@supabase/supabase-js';

export async function GET({ request, locals }) {
  try {
    // Get the headers from the request
    const headers = request.headers;
    const subdomain = headers.get('x-customer-subdomain');
    const domain = headers.get('x-customer-domain');
    const environment = headers.get('x-supabase-environment');

    // Initialize Supabase client with service role key to bypass RLS
    const runtime = locals.runtime;
    const supabaseUrl = (runtime?.env?.SUPABASE_URL) || import.meta.env.SUPABASE_URL;
    const supabaseServiceKey = (runtime?.env?.SUPABASE_SERVICE_ROLE_KEY) || import.meta.env.SUPABASE_SERVICE_ROLE_KEY;

    // Check if we have the required environment variables
    if (!supabaseUrl || !supabaseServiceKey) {
      return new Response(
        JSON.stringify({
          error: 'Server configuration error',
          details: 'Missing Supabase credentials in environment variables'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    let websiteData = null;

    // Query based on subdomain or domain
    if (subdomain && subdomain !== 'default') {
      const { data, error } = await supabase
        .from('websites')
        .select('*, theme:website_themes!websites_theme_id_fkey(*)')
        .eq('subdomain', subdomain)
        .single();

      if (!error && data) {
        websiteData = data;
      }
    } else if (domain) {
      // Try different formats of the domain
      const domainVariations = [
        domain,
        domain.toLowerCase(),
        domain.trim(),
        // Add www. if it doesn't have it
        !domain.startsWith('www.') ? `www.${domain}` : domain,
        // Remove www. if it has it
        domain.startsWith('www.') ? domain.substring(4) : domain
      ];

      // Try to find a match with any of the domain variations
      let domainData = null;

      for (const domainVariation of domainVariations) {
        const result = await supabase
          .from('custom_domains')
          .select('website_id')
          .eq('domain_name', domainVariation)
          .maybeSingle();

        if (result.data) {
          domainData = result.data;
          break;
        }
      }

      if (domainData?.website_id) {
        // Then get the website data
        const { data: websiteFromDomain, error: websiteError } = await supabase
          .from('websites')
          .select('*, theme:website_themes!websites_theme_id_fkey(*)')
          .eq('id', domainData.website_id)
          .single();

        if (!websiteError && websiteFromDomain) {
          websiteData = websiteFromDomain;
        }
      }
    }

    // Return the website data or default values
    return new Response(
      JSON.stringify({
        id: websiteData?.id || null,
        title: websiteData?.title || 'Imoblr - O maior luxo do mercado imobiliário é espaço',
        description: websiteData?.description || 'Pague menos no m2 e maximize a área do seu imóvel',
        subdomain: subdomain || 'N/A',
        domain: domain || 'N/A',
        environment: environment || 'N/A',
        team_account_id: websiteData?.team_account_id || null,
        website: websiteData,
        // Include theme data if available
        theme: websiteData?.theme || {
          primary_color: '#3B82F6',
          secondary_color: '#10B981',
          font_family: 'Poppins'
        }
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=60, stale-while-revalidate=300' // Cache for 1 minute
        }
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: 'Failed to fetch website information',
        message: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
