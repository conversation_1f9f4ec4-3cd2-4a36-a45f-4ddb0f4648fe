export async function GET({ request }) {
  try {
    // Get the property ID from the query parameters
    const url = new URL(request.url);
    const propertyId = url.searchParams.get('propertyId');

    console.log(`Test API: Received propertyId: ${propertyId}`);

    // Return a simple response
    return new Response(
      JSON.stringify({
        success: true,
        propertyId: propertyId,
        message: `Successfully received property ID: ${propertyId}`
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  } catch (error) {
    console.error('Test API error:', error);
    
    return new Response(
      JSON.stringify({
        error: 'Test API error',
        message: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
