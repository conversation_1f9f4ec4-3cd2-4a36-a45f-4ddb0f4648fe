import { createClient } from '@supabase/supabase-js';

export async function GET({ request, locals }) {
  try {
    // Get the team ID from the query parameters
    const url = new URL(request.url);
    const teamId = url.searchParams.get('teamId');
    const limit = parseInt(url.searchParams.get('limit') || '8', 10); // Default to 8 properties

    if (!teamId) {
      return new Response(
        JSON.stringify({ error: 'Team ID parameter is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client
    const runtime = locals.runtime;
    const supabaseUrl = (runtime?.env?.SUPABASE_URL) || import.meta.env.SUPABASE_URL;
    const supabaseAnonKey = (runtime?.env?.SUPABASE_ANON_KEY) || import.meta.env.SUPABASE_ANON_KEY;

    // Check if we have the required environment variables
    if (!supabaseUrl || !supabaseAnonKey) {
      return new Response(
        JSON.stringify({
          error: 'Server configuration error',
          details: 'Missing Supabase credentials in environment variables'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // Get the properties for this team
    const { data: properties, error: propertiesError } = await supabase
      .from('properties')
      .select(`
        id,
        title,
        description,
        type,
        purpose,
        created_at,
        rooms_count,
        bathrooms_count,
        parking_spots_count,
        total_area,
        built_area,
        address:address_id (
          id,
          street_number,
          neighborhood:neighborhood_id (id, name),
          city:city_id (id, name, state)
        ),
        prices:properties_prices(
          sale_price,
          rent_price,
          bnb_price
        ),
        properties_media(
          id,
          url,
          media_type
        )
      `)
      .eq('team_account_id', teamId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (propertiesError) {
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch properties',
          message: propertiesError.message
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }


    // Process the properties to format them for the frontend
    const formattedProperties = properties.map(property => {
      try {
        // Get the first image from properties_media
        const firstImage = property.properties_media && property.properties_media.length > 0
          ? property.properties_media[0]
          : null;

        // Get the location from the address
        const neighborhood = property.address?.neighborhood?.name || '';
        const city = property.address?.city?.name || '';
        const location = neighborhood ? `${neighborhood}, ${city}` : city || 'Localização não especificada';

        // Extract price values from the prices object
        let salePrice = null;
        let rentPrice = null;
        let bnbPrice = null;

        // Check if the prices object exists
        if (property.prices && typeof property.prices === 'object') {
          salePrice = property.prices.sale_price;
          rentPrice = property.prices.rent_price;
          bnbPrice = property.prices.bnb_price;
        }

        // Format the price with currency
        const price = salePrice || rentPrice || bnbPrice || null;
        const formattedPrice = price ? new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
          minimumFractionDigits: 2
        }).format(price) : '';

        // Determine transaction type based on prices
        const numericSalePrice = salePrice ? Number(salePrice) : 0;
        const numericRentPrice = rentPrice ? Number(rentPrice) : 0;

        let transactionType = 'both';
        if (numericSalePrice > 0 && numericRentPrice <= 0) {
          transactionType = 'sale';
        } else if (numericSalePrice <= 0 && numericRentPrice > 0) {
          transactionType = 'rent';
        }

        return {
          id: property.id,
          title: property.title || `${property.type || 'Imóvel'} à ${property.purpose === 'residential' ? 'venda' : 'locação'}`,
          location: location,
          neighborhood: property.address?.neighborhood?.name || '',
          city: property.address?.city?.name || '',
          type: property.type,
          purpose: property.purpose,
          transaction_type: transactionType,
          // Convert prices to numbers explicitly
          sale_price: salePrice ? Number(salePrice) : null,
          rent_price: rentPrice ? Number(rentPrice) : null,
          bedrooms: property.rooms_count,
          bathrooms: property.bathrooms_count,
          area: property.total_area || property.built_area,
          price: formattedPrice,
          images: property.properties_media?.map(media => media.url) || [],
          image: {
            src: firstImage?.url || 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?q=80&w=1470&auto=format&fit=crop',
            width: 1470,
            height: 980
          }
        };
      } catch (error) {
        console.error(`Error formatting property ${property?.id}:`, error);
        // Return a default property object if there's an error
        return {
          id: property?.id || 'unknown',
          title: property?.title || 'Imóvel',
          location: 'Localização não disponível',
          neighborhood: '',
          city: '',
          type: property?.type || '',
          purpose: property?.purpose || '',
          transaction_type: 'both',
          sale_price: null,
          rent_price: null,
          bedrooms: null,
          bathrooms: null,
          area: null,
          price: '',
          images: [],
          image: {
            src: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?q=80&w=1470&auto=format&fit=crop',
            width: 1470,
            height: 980
          }
        };
      }
    });

    // Return the properties data
    return new Response(
      JSON.stringify({
        properties: formattedProperties
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=60, stale-while-revalidate=300' // Cache for 1 minute
        }
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: 'Failed to fetch properties',
        message: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
