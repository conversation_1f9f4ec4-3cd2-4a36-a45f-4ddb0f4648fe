import { createClient } from '@supabase/supabase-js';

export async function GET({ request, locals }) {
  try {
    // Get the website ID from the query parameters
    const url = new URL(request.url);
    const websiteId = url.searchParams.get('websiteId');

    if (!websiteId) {
      return new Response(
        JSON.stringify({ error: 'Website ID parameter is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client
    const runtime = locals.runtime;
    const supabaseUrl = (runtime?.env?.SUPABASE_URL) || import.meta.env.SUPABASE_URL;
    const supabaseAnonKey = (runtime?.env?.SUPABASE_ANON_KEY) || import.meta.env.SUPABASE_ANON_KEY;

    // Check if we have the required environment variables
    if (!supabaseUrl || !supabaseAnonKey) {
      return new Response(
        JSON.stringify({
          error: 'Server configuration error',
          details: 'Missing Supabase credentials in environment variables'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // First, get the team_account_id from the website
    const { data: website, error: websiteError } = await supabase
      .from('websites')
      .select('team_account_id')
      .eq('id', websiteId)
      .single();

    if (websiteError) {
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch website',
          message: websiteError.message
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (!website) {
      return new Response(
        JSON.stringify({
          error: 'Website not found',
          websiteId: websiteId
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Now get the team profile using the team_account_id
    const { data: teamProfile, error: profileError } = await supabase
      .from('public_team_profiles')
      .select('*')
      .eq('team_account_id', website.team_account_id)
      .maybeSingle();

    if (profileError && profileError.code !== 'PGRST116') {
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch team profile',
          message: profileError.message
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Return the team profile data or default values
    return new Response(
      JSON.stringify({
        teamProfile: teamProfile || {
          name: '',
          address: '',
          phone_number: '',
          whatsapp_number: '',
          instagram_url: '',
          facebook_url: '',
          youtube_url: '',
          about_us: ''
        }
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=60, stale-while-revalidate=300' // Cache for 1 minute
        }
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: 'Failed to fetch team profile information',
        message: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
