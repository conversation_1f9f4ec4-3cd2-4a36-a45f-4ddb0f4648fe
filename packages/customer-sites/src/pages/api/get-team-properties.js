import { createClient } from '@supabase/supabase-js';

export async function GET({ request, locals }) {
  try {
    // Get the website ID from the query parameters
    const url = new URL(request.url);
    const websiteId = url.searchParams.get('websiteId');
    const limit = parseInt(url.searchParams.get('limit') || '8', 10); // Default to 8 properties

    if (!websiteId) {
      return new Response(
        JSON.stringify({ error: 'Website ID parameter is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client
    const runtime = locals.runtime;
    const supabaseUrl = (runtime?.env?.SUPABASE_URL) || import.meta.env.SUPABASE_URL;
    const supabaseAnonKey = (runtime?.env?.SUPABASE_ANON_KEY) || import.meta.env.SUPABASE_ANON_KEY;

    // Check if we have the required environment variables
    if (!supabaseUrl || !supabaseAnonKey) {
      return new Response(
        JSON.stringify({
          error: 'Server configuration error',
          details: 'Missing Supabase credentials in environment variables'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // First, get the team_account_id from the website
    const { data: website, error: websiteError } = await supabase
      .from('websites')
      .select('team_account_id')
      .eq('id', websiteId)
      .single();

    if (websiteError) {
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch website',
          message: websiteError.message
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (!website) {
      return new Response(
        JSON.stringify({
          error: 'Website not found',
          websiteId: websiteId
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Now get the properties for this team
    const { data: properties, error: propertiesError } = await supabase
      .from('properties')
      .select(`
        id,
        title,
        description,
        type,
        purpose,
        created_at,
        address:address_id (
          id,
          street_number,
          neighborhood:neighborhood_id (id, name),
          city:city_id (id, name, state)
        ),
        prices:properties_prices(
          sale_price,
          rent_price,
          bnb_price
        ),
        properties_media(
          id,
          url,
          media_type
        )
      `)
      .eq('team_account_id', website.team_account_id)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (propertiesError) {
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch properties',
          message: propertiesError.message
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Process the properties to format them for the frontend
    const formattedProperties = properties.map(property => {
      // Get the first image from properties_media
      const firstImage = property.properties_media && property.properties_media.length > 0
        ? property.properties_media[0]
        : null;
      
      // Get the location from the address
      const neighborhood = property.address?.neighborhood?.name || '';
      const city = property.address?.city?.name || '';
      const location = neighborhood ? `${neighborhood}, ${city}` : city;
      
      // Get the price based on availability
      const prices = property.prices && property.prices.length > 0 ? property.prices[0] : null;
      const price = prices?.sale_price || prices?.rent_price || prices?.bnb_price || null;
      
      // Format the price with currency
      const formattedPrice = price ? new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(price) : '';

      return {
        id: property.id,
        title: property.title || `${property.type} à ${property.purpose === 'residential' ? 'venda' : 'locação'}`,
        location: location,
        price: formattedPrice,
        image: {
          src: firstImage?.url || 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?q=80&w=1470&auto=format&fit=crop',
          width: 1470,
          height: 980
        }
      };
    });

    // Return the properties data
    return new Response(
      JSON.stringify({
        properties: formattedProperties
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=60, stale-while-revalidate=300' // Cache for 1 minute
        }
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: 'Failed to fetch properties',
        message: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
