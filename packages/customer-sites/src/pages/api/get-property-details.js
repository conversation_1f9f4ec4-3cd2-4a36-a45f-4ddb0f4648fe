import { createClient } from '@supabase/supabase-js';

export async function GET({ request, locals }) {
  try {
    // Get the property ID from the query parameters
    const url = new URL(request.url);
    const propertyId = url.searchParams.get('propertyId');

    if (!propertyId) {
      return new Response(
        JSON.stringify({ error: 'Property ID parameter is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client
    const runtime = locals.runtime;
    const supabaseUrl = (runtime?.env?.SUPABASE_URL) || import.meta.env.SUPABASE_URL;
    const supabaseAnonKey = (runtime?.env?.SUPABASE_ANON_KEY) || import.meta.env.SUPABASE_ANON_KEY;

    // Check if we have the required environment variables
    if (!supabaseUrl || !supabaseAnonKey) {
      return new Response(
        JSON.stringify({
          error: 'Server configuration error',
          details: 'Missing Supabase credentials in environment variables'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    try {
      // Try to get the property prices first
      let pricesCheck = [];
      const { data: pricesData, error: pricesError } = await supabase
        .from('properties_prices')
        .select('*')
        .eq('property_id', propertyId);

      if (!pricesError && pricesData) {
        pricesCheck = pricesData;
      }

      // Get the property details
      const { data: property, error: propertyError } = await supabase
        .from('properties')
        .select(`
          id,
          title,
          description,
          type,
          purpose,
          created_at,
          rooms_count,
          bathrooms_count,
          parking_spots_count,
          suites_count,
          floors_count,
          floor,
          total_area,
          built_area,
          pets_allowed,
          furnished,
          address:address_id (
            id,
            street_number,
            postcode,
            state,
            country,
            neighborhood:neighborhood_id (id, name),
            city:city_id (id, name, state),
            street:street_id (id, name)
          ),
          prices:properties_prices(
            id,
            sale_price,
            rent_price,
            bnb_price,
            condominium_monthly_tax,
            iptu_monthly_tax,
            insurance_monthly_tax,
            other_monthly_tax
          ),
          property_amenities:property_amenities_id(
            id,
            barbecue_grill,
            gourmet_space,
            garden,
            pool,
            backyard,
            water_heating,
            heating,
            air_conditioning,
            internet,
            garage,
            fireplace,
            laundry,
            sauna,
            spa,
            security_system
          ),
          building_amenities:building_amenities_id(
            id,
            shared_barbecue_grill,
            shared_gourmet_space,
            bicycle_storage,
            intercom,
            gym,
            green_area,
            playground,
            shared_pool,
            tennis_court,
            sports_area,
            party_room,
            game_room,
            storage,
            shared_laundry,
            elevator,
            shared_garage,
            shared_water_heating,
            power_generator,
            reception,
            shared_sauna,
            shared_spa,
            shared_security_system,
            gated_community,
            private_security
          ),
          properties_media(
            id,
            url,
            media_type,
            created_at
          )
        `)
        .eq('id', propertyId)
        .single();

      if (propertyError?.code === 'PGRST116' ||
          (propertyError?.details && propertyError.details.includes('0 rows'))) {
        // No property found with this ID
        return new Response(
          JSON.stringify({
            error: 'Property not found',
            propertyId: propertyId
          }),
          {
            status: 404,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      } else if (propertyError) {
        // Other database error
        return new Response(
          JSON.stringify({
            error: 'Failed to fetch property',
            message: propertyError.message,
            details: propertyError
          }),
          {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      if (!property) {
        return new Response(
          JSON.stringify({
            error: 'Property not found',
            propertyId: propertyId
          }),
          {
            status: 404,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      // Process the property data
      try {
        // Get all media items
        const mediaItems = property.properties_media || [];

        // Sort media items by created_at (newest first)
        mediaItems.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

        // Get the location from the address
        const street = property.address?.street?.name || '';
        const streetNumber = property.address?.street_number || '';
        const neighborhood = property.address?.neighborhood?.name || '';
        const city = property.address?.city?.name || '';
        const state = property.address?.state || '';

        // Format the address
        const addressParts = [
          street && streetNumber ? `${street}, ${streetNumber}` : street || '',
          neighborhood,
          city,
          state
        ].filter(Boolean);

        const location = addressParts.join(', ');

        // Get the prices directly from the database
        let prices = null;

        // Use the prices we already fetched earlier
        if (pricesCheck && pricesCheck.length > 0) {
          prices = pricesCheck[0];
        } else if (property.prices && property.prices.length > 0) {
          // Fallback to prices from the property query if available
          prices = property.prices[0];
        }

        // Format the prices with currency - handle string values
        const formatCurrency = (value) => {
          if (value === null || value === undefined) return null;

          console.log(`Formatting price value: ${value} (type: ${typeof value})`);

          // Convert string to number if needed
          let numValue = value;
          if (typeof value === 'string') {
            numValue = parseFloat(value.replace(/[^0-9.,]/g, '').replace(',', '.'));
          }

          // Check if it's a valid number
          if (isNaN(numValue)) {
            console.log(`Invalid price value: ${value}, converted to: ${numValue}`);
            return null;
          }

          // Divide by 100 to get the correct decimal placement
          numValue = numValue / 100;

          console.log(`Formatting price: ${value} -> ${numValue}`);
          return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL',
            minimumFractionDigits: 2
          }).format(numValue);
        };

        // Log prices for debugging

        const formattedProperty = {
          id: property.id,
          title: property.title || `${property.type || 'Imóvel'} à ${property.purpose === 'residential' ? 'venda' : 'locação'}`,
          description: property.description,
          type: property.type,
          purpose: property.purpose,
          location: location,
          rooms: property.rooms_count,
          bathrooms: property.bathrooms_count,
          parkingSpots: property.parking_spots_count,
          suites: property.suites_count,
          floors: property.floors_count,
          floor: property.floor,
          totalArea: property.total_area,
          builtArea: property.built_area,
          petsAllowed: property.pets_allowed,
          furnished: property.furnished,
          prices: {
            sale: formatCurrency(prices?.sale_price),
            rent: formatCurrency(prices?.rent_price),
            bnb: formatCurrency(prices?.bnb_price),
            condominium: formatCurrency(prices?.condominium_monthly_tax),
            iptu: formatCurrency(prices?.iptu_monthly_tax),
            insurance: formatCurrency(prices?.insurance_monthly_tax),
            other: formatCurrency(prices?.other_monthly_tax)
          },
          // Include raw prices data for debugging
          rawPrices: prices,
          amenities: property.property_amenities || {},
          buildingAmenities: property.building_amenities || {},
          media: mediaItems.map(item => ({
            id: item.id,
            url: item.url,
            type: item.media_type
          }))
        };

        // Return the property data
        return new Response(
          JSON.stringify({
            property: formattedProperty
          }),
          {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'public, max-age=60, stale-while-revalidate=300' // Cache for 1 minute
            }
          }
        );
      } catch (formatError) {
        console.error('Error formatting property data:', formatError);

        // Return the raw property data if formatting fails
        return new Response(
          JSON.stringify({
            property: property
          }),
          {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'public, max-age=60, stale-while-revalidate=300'
            }
          }
        );
      }
    } catch (queryError) {
      console.error('API query error:', queryError);
      console.error('API query error stack:', queryError.stack);

      return new Response(
        JSON.stringify({
          error: 'Failed to query property',
          message: queryError.message,
          stack: queryError.stack
        }),
        {
          status: 500,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }
  } catch (error) {
    console.error('API error:', error);
    console.error('API error stack:', error.stack);

    return new Response(
      JSON.stringify({
        error: 'Failed to fetch property',
        message: error.message,
        stack: error.stack
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
