import { createClient } from '@supabase/supabase-js';

export async function GET({ request, locals }) {
  try {
    // Get the team ID from the query parameters
    const url = new URL(request.url);
    const teamId = url.searchParams.get('teamId');

    if (!teamId) {
      return new Response(
        JSON.stringify({ error: 'Team ID parameter is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client
    const runtime = locals.runtime;
    const supabaseUrl = (runtime?.env?.SUPABASE_URL) || import.meta.env.SUPABASE_URL;
    const supabaseAnonKey = (runtime?.env?.SUPABASE_ANON_KEY) || import.meta.env.SUPABASE_ANON_KEY;

    // Check if we have the required environment variables
    if (!supabaseUrl || !supabaseAnonKey) {
      return new Response(
        JSON.stringify({
          error: 'Server configuration error',
          details: 'Missing Supabase credentials in environment variables'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    console.log(`API: Fetching unique property types for team ID: ${teamId}`);

    // Use a more efficient query that only gets the distinct property types
    const { data, error } = await supabase
      .from('properties')
      .select('type')
      .eq('team_account_id', teamId)
      .not('type', 'is', null);

    if (error) {
      console.error('Error fetching property types:', error);
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch property types',
          message: error.message
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Extract unique property types
    const propertyTypes = [...new Set(data.map(item => item.type))];
    console.log(`API: Found ${propertyTypes.length} unique property types`);

    // Return the property types
    return new Response(
      JSON.stringify({
        propertyTypes
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400' // Cache for 1 hour, stale for 1 day
        }
      }
    );
  } catch (error) {
    console.error('Error in get-team-property-types:', error);
    return new Response(
      JSON.stringify({
        error: 'Failed to fetch property types',
        message: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
