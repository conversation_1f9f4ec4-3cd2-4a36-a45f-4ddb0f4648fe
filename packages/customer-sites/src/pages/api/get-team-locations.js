import { createClient } from '@supabase/supabase-js';

export async function GET({ request, locals }) {
  try {
    // Get the team ID from the query parameters
    const url = new URL(request.url);
    const teamId = url.searchParams.get('teamId');

    if (!teamId) {
      return new Response(
        JSON.stringify({ error: 'Team ID parameter is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client
    const runtime = locals.runtime;
    const supabaseUrl = (runtime?.env?.SUPABASE_URL) || import.meta.env.SUPABASE_URL;
    const supabaseAnonKey = (runtime?.env?.SUPABASE_ANON_KEY) || import.meta.env.SUPABASE_ANON_KEY;

    // Check if we have the required environment variables
    if (!supabaseUrl || !supabaseAnonKey) {
      return new Response(
        JSON.stringify({
          error: 'Server configuration error',
          details: 'Missing Supabase credentials in environment variables'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    console.log(`API: Fetching locations for team ID: ${teamId}`);

    // Query neighborhoods and cities directly instead of using a database function
    console.log(`API: Fetching locations for team ID: ${teamId}`);

    // First, get all team neighborhoods
    const { data: teamNeighborhoods, error: teamNeighborhoodsError } = await supabase
      .from('team_neighborhoods')
      .select('neighborhood_id')
      .eq('team_account_id', teamId);

    if (teamNeighborhoodsError) {
      console.error('Error fetching team neighborhoods:', teamNeighborhoodsError);
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch team neighborhoods',
          message: teamNeighborhoodsError.message
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // If no neighborhoods found for this team
    if (!teamNeighborhoods || teamNeighborhoods.length === 0) {
      console.log(`No neighborhoods found for team ID: ${teamId}`);
      return new Response(
        JSON.stringify({
          locations: []
        }),
        {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400'
          }
        }
      );
    }

    // Extract neighborhood IDs
    const neighborhoodIds = teamNeighborhoods.map(tn => tn.neighborhood_id);

    // Then, get the neighborhood details with city information
    const { data, error } = await supabase
      .from('neighborhoods')
      .select(`
        id,
        name,
        city_id,
        cities(id, name)
      `)
      .in('id', neighborhoodIds)
      .order('name');

    if (error) {
      console.error('Error fetching team locations:', error);
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch team locations',
          message: error.message
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Process the data to group neighborhoods by city
    const citiesMap = new Map();

    // Process the data to create city and neighborhood structure
    data.forEach(item => {
      if (!citiesMap.has(item.city_id)) {
        citiesMap.set(item.city_id, {
          id: item.city_id,
          name: item.cities.name,
          neighborhoods: []
        });
      }

      const city = citiesMap.get(item.city_id);
      city.neighborhoods.push({
        id: item.id,
        name: item.name
      });
    });

    // Convert the map to an array
    const locations = Array.from(citiesMap.values());

    // Sort cities alphabetically
    locations.sort((a, b) => a.name.localeCompare(b.name));

    // Sort neighborhoods within each city
    locations.forEach(city => {
      city.neighborhoods.sort((a, b) => a.name.localeCompare(b.name));
    });

    console.log(`API: Found ${locations.length} cities with neighborhoods`);

    // Return the locations
    return new Response(
      JSON.stringify({
        locations
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400' // Cache for 1 hour, stale for 1 day
        }
      }
    );
  } catch (error) {
    console.error('Error in get-team-locations:', error);
    return new Response(
      JSON.stringify({
        error: 'Failed to fetch team locations',
        message: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
