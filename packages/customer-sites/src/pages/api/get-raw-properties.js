import { createClient } from '@supabase/supabase-js';

export async function GET({ request, locals }) {
  try {
    // Get the team ID from the query parameters
    const url = new URL(request.url);
    const teamId = url.searchParams.get('teamId');

    if (!teamId) {
      return new Response(
        JSON.stringify({ error: 'Team ID parameter is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client
    const runtime = locals.runtime;
    const supabaseUrl = (runtime?.env?.SUPABASE_URL) || import.meta.env.SUPABASE_URL;
    const supabaseAnonKey = (runtime?.env?.SUPABASE_ANON_KEY) || import.meta.env.SUPABASE_ANON_KEY;

    // Check if we have the required environment variables
    if (!supabaseUrl || !supabaseAnonKey) {
      return new Response(
        JSON.stringify({
          error: 'Server configuration error',
          details: 'Missing Supabase credentials in environment variables'
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // Get the raw properties data for this team
    const { data: properties, error: propertiesError } = await supabase
      .from('properties')
      .select(`
        id,
        title,
        description,
        type,
        purpose,
        created_at,
        address:address_id (
          id,
          street_number,
          neighborhood:neighborhood_id (id, name),
          city:city_id (id, name, state)
        )
      `)
      .eq('team_account_id', teamId);

    if (propertiesError) {
      return new Response(
        JSON.stringify({
          error: 'Failed to fetch raw properties',
          message: propertiesError.message
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Return the raw properties data
    return new Response(
      JSON.stringify({
        properties: properties
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=60, stale-while-revalidate=300' // Cache for 1 minute
        }
      }
    );
  } catch (error) {
    return new Response(
      JSON.stringify({
        error: 'Failed to fetch raw properties',
        message: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
