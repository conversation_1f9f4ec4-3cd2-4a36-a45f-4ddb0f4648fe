import { defineMiddleware } from "astro:middleware";

// Middleware to fetch website info and theme data from API and set in context.locals
export const onRequest = defineMiddleware(async (context, next) => {
    console.log('Middleware: Starting execution', context.request.url);

    // Check if we're in a build/prerender context by looking for build-specific environment variables
    const isBuildTime = process.env.NODE_ENV === 'production' && 
                       (process.env.CF_PAGES === '1' || process.env.CI === 'true') &&
                       !context.locals?.runtime;
    
    if (isBuildTime) {
        console.log('Middleware: Skipping during build/prerender phase');
        // Set default values for build time
        context.locals.websiteInfo = {
            title: 'Defina um título no seu painel',
            description: 'Defina uma descrição no seu painel',
            subdomain: 'N/A',
            domain: 'N/A',
            websiteId: null,
            teamId: null,
            website: null
        };
        context.locals.websiteTheme = {
            primary_color: '#3B82F6',
            secondary_color: '#10B981',
            font_family: 'Poppins'
        };
        context.locals.teamProfile = {
            name: '',
            address: '',
            phone_number: '',
            whatsapp_number: '',
            instagram_url: '',
            facebook_url: '',
            youtube_url: ''
        };
        return next();
    }

    // Get the headers from the request
    const headers = context.request.headers;
    const customerSubdomain = headers.get('x-customer-subdomain');
    const customerDomain = headers.get('x-customer-domain');

    // Get the URL to check if this is a static asset request
    const url = new URL(context.request.url);
    const path = url.pathname;
    const isStaticAsset = path.match(/\.(svg|png|jpg|jpeg|gif|ico|css|js)$/i);

    // Extract website address from URL path if it exists
    // Format: /[websiteAddress]/... or /api/...
    let websiteAddress = null;
    const pathParts = path.split('/');

    // Check if this is a path with a website address
    if (pathParts.length > 1 && pathParts[1] && pathParts[1] !== 'api' && !isStaticAsset) {
        websiteAddress = pathParts[1];
        console.log(`Middleware: Extracted website address from path: ${websiteAddress}`);
    }

    // If we couldn't extract from the path, check the x-website-address header
    // This is set by the Cloudflare worker for backward compatibility
    if (!websiteAddress) {
        websiteAddress = headers.get('x-website-address');
        if (websiteAddress) {
            console.log(`Middleware: Using website address from header: ${websiteAddress}`);
        }
    }

    // Check if this is a dynamic route for property details
    const isPropertyDetailRoute = path.match(/^\/[\w-]+\/imoveis\/[\w-]+$/i);
    if (isPropertyDetailRoute) {
        console.log(`Middleware: Detected property detail route: ${path}`);
    }

    // Skip middleware processing for static assets and API requests
    if (isStaticAsset || path.startsWith('/api/')) {
        console.log(`Middleware: Skipping for static asset or API request: ${path}`);
        return next();
    }

    // Skip middleware processing for favicon.svg and other special paths
    if (customerSubdomain === 'favicon.svg' || customerSubdomain === '_image' ||
        customerSubdomain === '@vite' || customerSubdomain === '@id' ||
        customerSubdomain === 'node_modules' || path.includes('__x00__astro')) {
        console.log(`Middleware: Skipping for special path: ${customerSubdomain || path}`);
        return next();
    }

    // We'll get the team ID from the website info
    // This ensures we never hard-code sensitive data

    // Default website info
    let websiteInfo = {
        title: 'Defina um título no seu painel',
        description: 'Defina uma descrição no seu painel',
        subdomain: customerSubdomain || 'N/A',
        domain: customerDomain || 'N/A',
        websiteId: null, // Add websiteId field
        teamId: null, // Add teamId field
        website: null // Add website object field
    };

    // Default theme
    let websiteTheme = {
        primary_color: '#3B82F6',
        secondary_color: '#10B981',
        font_family: 'Poppins'
    };

    // Default team profile
    let teamProfile = {
        name: '',
        address: '',
        phone_number: '',
        whatsapp_number: '',
        instagram_url: '',
        facebook_url: '',
        youtube_url: ''
    };

    // No default team ID - we'll get it from the website info

    // Debug headers
    console.log('Headers received in middleware:');
    console.log('x-customer-subdomain:', customerSubdomain);
    console.log('x-customer-domain:', customerDomain);

    // We'll get the team ID from the website info object
    // No hard-coded values, even in development

    try {
        // Use the website address as the identifier for fetching website info
        let identifier = websiteAddress || 'default';

        console.log(`Middleware: Using identifier: ${identifier}`);

        // Fetch website information from our API endpoints
        if (identifier && identifier !== 'default' && !['_image', 'favicon.svg'].includes(identifier)) {
            // Determine if this is a domain or subdomain based on whether it contains a dot
            const isDomain = identifier.includes('.');
            let apiEndpoint = '';

            if (isDomain) {
                apiEndpoint = `/api/get-website-by-domain?domain=${encodeURIComponent(identifier)}`;
                console.log(`Middleware: Fetching website info for domain: ${identifier}`);
            } else {
                apiEndpoint = `/api/get-website-by-subdomain?subdomain=${encodeURIComponent(identifier)}`;
                console.log(`Middleware: Fetching website info for subdomain: ${identifier}`);
            }

            // Call our API endpoint to get website info
            const currentUrl = new URL(context.request.url);
            const apiUrl = new URL(apiEndpoint, currentUrl.origin);
            console.log(`Middleware: API URL: ${apiUrl.toString()}`);
            
            let response;
            try {
                response = await fetch(apiUrl.toString());
            } catch (fetchError) {
                console.error('Middleware: Fetch failed, likely during build time:', fetchError.message);
                // Continue with default values if fetch fails
                response = null;
            }

            if (response && response.ok) {
                const data = await response.json();
                console.log('Middleware: Subdomain API response:', data);

                if (data.title) websiteInfo.title = data.title;
                if (data.description) websiteInfo.description = data.description;

                // Store the website object and ID
                if (data.id) {
                    websiteInfo.websiteId = data.id;
                    // For domain API, use the nested website object
                    websiteInfo.website = data.website || data;

                    // Store the team ID if available
                    if (data.team_account_id) {
                        websiteInfo.teamId = data.team_account_id;
                        console.log(`Middleware: Found team ID: ${data.team_account_id}`);
                    }
                }

                // Extract theme data if available
                if (data.theme) {
                    websiteTheme = data.theme;
                    console.log('Middleware: Theme data from subdomain API:', data.theme);
                }

                // Fetch team profile data if we have a website ID
                if (data.id) {
                    console.log(`Middleware: Fetching team profile for website ID: ${data.id}`);
                    const teamProfileUrl = new URL(`/api/get-team-profile?websiteId=${encodeURIComponent(data.id)}`, currentUrl.origin);
                    console.log(`Middleware: Team profile API URL: ${teamProfileUrl.toString()}`);
                    const teamProfileResponse = await fetch(teamProfileUrl.toString());

                    if (teamProfileResponse.ok) {
                        const teamProfileData = await teamProfileResponse.json();
                        console.log('Middleware: Team profile API response:', teamProfileData);

                        if (teamProfileData.teamProfile) {
                            teamProfile = teamProfileData.teamProfile;
                        }
                    } else {
                        console.error('Middleware: Error fetching team profile:', await teamProfileResponse.text());
                    }
                }
            } else if (response) {
                const responseText = await response.text();
                console.error('Middleware: Error fetching website by subdomain:', responseText);

                // Check if this is a 404 error (website not found or not published)
                if (response.status === 404) {
                    try {
                        const errorData = JSON.parse(responseText);
                        let errorType = 'website-not-found';

                        // Check if this is a 'not published' error
                        if (errorData.message && errorData.message.includes('not published')) {
                            errorType = 'website-not-published';
                        }

                        // Redirect to the 404 page with appropriate error type
                        // If we have a website address in the URL, use that in the redirect
                        if (websiteAddress) {
                            return context.redirect(`/${websiteAddress}/404?error=${errorType}`);
                        } else {
                            return context.redirect(`/404?error=${errorType}`);
                        }
                    } catch (parseError) {
                        console.error('Middleware: Error parsing API response:', parseError);
                        // Redirect to generic 404 page if we can't parse the error
                        if (websiteAddress) {
                            return context.redirect(`/${websiteAddress}/404`);
                        } else {
                            return context.redirect('/404');
                        }
                    }
                }
            } else {
                console.log('Middleware: No response received, continuing with defaults');
            }
        } else if (customerDomain) {
            console.log(`Middleware: Fetching website info for domain: ${customerDomain}`);

            // Call our API endpoint to get website info by domain
            const currentUrl = new URL(context.request.url);
            const apiUrl = new URL(`/api/get-website-by-domain?domain=${encodeURIComponent(customerDomain)}`, currentUrl.origin);
            console.log(`Middleware: Domain API URL: ${apiUrl.toString()}`);
            
            let response;
            try {
                response = await fetch(apiUrl.toString());
            } catch (fetchError) {
                console.error('Middleware: Domain fetch failed, likely during build time:', fetchError.message);
                response = null;
            }

            if (response && response.ok) {
                const data = await response.json();
                console.log('Middleware: Domain API response:', data);

                if (data.title) websiteInfo.title = data.title;
                if (data.description) websiteInfo.description = data.description;

                // Store the website object and ID
                if (data.id) {
                    websiteInfo.websiteId = data.id;
                    websiteInfo.website = data;

                    // Store the team ID if available
                    if (data.team_account_id) {
                        websiteInfo.teamId = data.team_account_id;
                        console.log(`Middleware: Found team ID: ${data.team_account_id}`);
                    }
                }

                // Extract theme data if available
                if (data.theme) {
                    websiteTheme = data.theme;
                    console.log('Middleware: Theme data from domain API:', data.theme);
                }

                // Fetch team profile data if we have a website ID
                if (data.id) {
                    console.log(`Middleware: Fetching team profile for website ID: ${data.id}`);
                    const teamProfileUrl = new URL(`/api/get-team-profile?websiteId=${encodeURIComponent(data.id)}`, currentUrl.origin);
                    console.log(`Middleware: Team profile API URL: ${teamProfileUrl.toString()}`);
                    const teamProfileResponse = await fetch(teamProfileUrl.toString());

                    if (teamProfileResponse.ok) {
                        const teamProfileData = await teamProfileResponse.json();
                        console.log('Middleware: Team profile API response:', teamProfileData);

                        if (teamProfileData.teamProfile) {
                            teamProfile = teamProfileData.teamProfile;
                        }
                    } else {
                        console.error('Middleware: Error fetching team profile:', await teamProfileResponse.text());
                    }
                }
            } else if (response) {
                const responseText = await response.text();
                console.error('Middleware: Error fetching website by domain:', responseText);

                // Check if this is a 404 error (website not found or not published)
                if (response.status === 404) {
                    try {
                        const errorData = JSON.parse(responseText);
                        let errorType = 'website-not-found';

                        // Check if this is a 'not published' error
                        if (errorData.message && errorData.message.includes('not published')) {
                            errorType = 'website-not-published';
                        }

                        // Redirect to the 404 page with appropriate error type
                        // If we have a website address in the URL, use that in the redirect
                        if (websiteAddress) {
                            return context.redirect(`/${websiteAddress}/404?error=${errorType}`);
                        } else {
                            return context.redirect(`/404?error=${errorType}`);
                        }
                    } catch (parseError) {
                        console.error('Middleware: Error parsing API response:', parseError);
                        // Redirect to generic 404 page if we can't parse the error
                        if (websiteAddress) {
                            return context.redirect(`/${websiteAddress}/404`);
                        } else {
                            return context.redirect('/404');
                        }
                    }
                }
            } else {
                console.log('Middleware: No domain response received, continuing with defaults');
            }
        }
    } catch (error) {
        console.error('Middleware: Error fetching website info:', error);
    }

    // Log the final data
    console.log('Middleware: Final websiteInfo:', websiteInfo);
    console.log('Middleware: Final websiteTheme:', websiteTheme);
    console.log('Middleware: Final teamProfile:', teamProfile);
    console.log('Middleware: Team ID set to:', websiteInfo.teamId);

    // Ensure theme data has primary_color
    if (!websiteTheme.primary_color) {
        console.warn('Middleware: Warning - websiteTheme is missing primary_color, using default');
        websiteTheme.primary_color = '#3B82F6';
    }

    // Set the values in context.locals
    context.locals.websiteInfo = websiteInfo;
    context.locals.websiteTheme = websiteTheme;
    context.locals.teamProfile = teamProfile;

    // If we extracted a website address from the URL, add it to context.locals
    if (websiteAddress) {
        context.locals.websiteAddress = websiteAddress;
    }

    console.log('Middleware: After setting - context.locals:', context.locals);

    // Continue to the endpoint
    return next();
});