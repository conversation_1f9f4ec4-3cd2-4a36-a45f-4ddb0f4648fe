/**
 * Format price function - converts numeric values to properly formatted currency
 * @param {number|string|null|undefined} price - The price to format
 * @returns {string} The formatted price
 */
export const formatPrice = (price) => {
  if (!price) return "";
  
  // Convert to number if it's a string
  const numericPrice = typeof price === 'string' ? parseFloat(price.replace(/[^0-9]/g, '')) : price;
  
  // Divide by 100 to get the correct decimal placement
  const priceWithDecimals = numericPrice / 100;
  
  // Format as Brazilian currency
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
    minimumFractionDigits: 2
  }).format(priceWithDecimals);
};
