---
// Import the PropertyTypeSelect component
import { PropertyTypeSelect } from "../components/PropertyTypeSelect";
import { PropertyLocationSelect } from "../components/PropertyLocationSelect";

// Get website info and theme from Astro.locals (set by middleware)
// Add fallback values in case the properties don't exist in Astro.locals
const defaultWebsiteTheme = {
  primary_color: "#3B82F6",
  secondary_color: "#10B981",
  font_family: "Poppins",
};

// Get values from Astro.locals
const { websiteInfo, websiteTheme } = Astro.locals || {};

// Use fallbacks if values are not available
const theme = websiteTheme || defaultWebsiteTheme;

// Calculate a darker version of the primary color for headings
import Color from "colorjs.io";

// Create a darker version of primary_color for headings
let headingColor = "#6b0f2b"; // Default fallback color
let lighterHeadingColor = "#6b0f2b"; // Default fallback color
if (theme?.primary_color) {
  try {
    // Create a Color object from the primary color
    const primaryColor = new Color(theme.primary_color);

    // Mix with black to darken it (30% black, 70% primary color)
    const darkColor = Color.mix(primaryColor, "black", 0.3, { space: "lch" });

    // Convert to sRGB and get the hex string
    headingColor = darkColor.to("srgb").toString({ format: "hex" });
    lighterHeadingColor = Color.mix(primaryColor, "white", 0.9, {
      space: "lch",
    })
      .to("srgb")
      .toString({ format: "hex" });
  } catch (error) {
    console.error("Error calculating heading color:", error);
    // Keep the default fallback color if there's an error
  }
}

// No need to fetch website info here as it's already available in Astro.locals

// Get the hero image URL if available from the website object
const heroImageUrl = websiteInfo?.website?.hero_image_url;

// Defining the hero image source
// Use the website's hero image if available, otherwise use a default image
const baseImageUrl =
  heroImageUrl ||
  "https://images.unsplash.com/photo-1512699355324-f07e3106dae5";

// If it's our own image, we don't need to use wsrv.nl optimization
const heroImage = {
  src: heroImageUrl
    ? baseImageUrl
    : `https://wsrv.nl/?url=${baseImageUrl}&w=1920&output=webp&q=85`,
  width: 1920,
  height: 1080,
};

// Log the hero image URL for debugging
console.log("Hero component: heroImageUrl:", heroImageUrl);
console.log("Hero component: final image src:", heroImage.src);
---

<!-- Full-width wrapper -->
<div
  class="relative w-screen flex items-center h-[calc(100vh-4rem)] lg:h-[50vh] lg:mb-16"
>
  <!-- Left sub-wrapper (50% of screen width) -->
  <div
    class="absolute lg:relative w-full lg:w-1/2 flex justify-end pl-4 lg:pl-8 h-full z-20"
  >
    <!-- Container that's 50% width of .container class (aligned with other container elements) -->
    <div class="w-full max-w-[40rem] pr-4 lg:pr-8 flex items-center">
      <!-- 40rem is 50% of the 80rem container max-width -->
      <div class="lg:p-12 w-full">
        <h1
          class="text-2xl md:text-3xl line-clamp-2 font-bold mb-1 heading-color"
        >
          {websiteInfo?.title}
        </h1>
        <p
          class="text-md md:text-lg line-clamp-4 text-white/90 md:text-gray-600 mb-12"
        >
          {websiteInfo?.description}
        </p>

        <!-- Tabs for Alugar/Comprar -->
        <div class="flex mb-6 border-b border-gray-50/25 md:border-gray-200">
          <button
            class="flex-1 pb-2 px-4 text-white/80 md:text-gray-700 transition-colors"
            onmouseover={`this.style.color='${headingColor}'`}
            onmouseout="this.style.color=''"
          >
            Alugar
          </button>
          <button class="flex-1 pb-2 px-4 font-medium heading-color border-b-2">
            Comprar
          </button>
        </div>

        <!-- Search Fields -->
        <div class="flex flex-col gap-4">
          <!-- Location Field -->
          <PropertyLocationSelect
            client:load
            primaryColor={theme.primary_color}
            teamId={websiteInfo?.teamId}
          />

          <!-- Property Type Field -->
          <PropertyTypeSelect
            client:idle
            primaryColor={theme.primary_color}
            teamId={websiteInfo?.teamId}
          />

          <!-- Search Button -->
          <button
            id="search-properties-button"
            class="w-full py-3 text-white font-medium rounded-md transition-colors"
            style={`background-color: ${theme.primary_color};`}
            onmouseover={`this.style.backgroundColor='${headingColor}'`}
            onmouseout={`this.style.backgroundColor='${theme.primary_color}'`}
          >
            Buscar imóvel
          </button>

          <script>
            // Declare the global variables for TypeScript
            declare global {
              interface Window {
                __selectedLocation?: string;
                __selectedPropertyType?: string;
                websiteAddress?: string;
              }
            }

            // Wait for the DOM to be fully loaded
            document.addEventListener("DOMContentLoaded", () => {
              // Get the search button
              const searchButton = document.getElementById(
                "search-properties-button",
              );

              if (searchButton) {
                searchButton.addEventListener("click", () => {
                  const currentUrl = new URL(window.location.href);

                  // In production (non-localhost), use simple origin + /imoveis
                  // In development, use the website address logic for dynamic routing
                  let targetUrl;

                  if (
                    currentUrl.hostname === "localhost" ||
                    currentUrl.hostname.startsWith("127.0.0.1")
                  ) {
                    // Development: use website address logic
                    const pathParts = currentUrl.pathname.split("/");
                    let websiteAddress = window.websiteAddress || "";

                    // If no websiteAddress from window object, try to extract from URL path
                    if (
                      !websiteAddress &&
                      pathParts.length > 1 &&
                      pathParts[1]
                    ) {
                      websiteAddress = pathParts[1];
                    }

                    targetUrl = `${currentUrl.origin}/${websiteAddress}/imoveis`;
                  } else {
                    // Production: simple redirect to /imoveis
                    targetUrl = `${currentUrl.origin}/imoveis`;
                  }

                  console.log("Target URL:", targetUrl);

                  // Get the selected location and property type values from the global variables
                  // These variables are set by the respective components
                  const selectedLocation = window.__selectedLocation || "";
                  const selectedPropertyType =
                    window.__selectedPropertyType || "";

                  // Debug log to see if the global variables are set
                  console.log("Selected location:", selectedLocation);
                  console.log("Selected property type:", selectedPropertyType);

                  // Add query parameters if values are selected
                  const params = new URLSearchParams();

                  if (selectedLocation && selectedLocation !== "none") {
                    params.append("location", selectedLocation);
                  }

                  if (selectedPropertyType && selectedPropertyType !== "none") {
                    params.append("type", selectedPropertyType);
                  }

                  // Add the query parameters to the URL if any exist
                  if (params.toString()) {
                    targetUrl += `?${params.toString()}`;
                  }

                  // Navigate to the properties page
                  window.location.href = targetUrl;
                });
              }
            });
          </script>
        </div>
      </div>
    </div>
  </div>

  <!-- Right sub-wrapper (50% of screen width) -->
  <div class="w-full absolute lg:relative lg:w-1/2 h-full">
    <!-- Content takes 100% of the right sub-wrapper -->
    <div class="block md:hidden absolute inset-0 bg-black opacity-60 z-10">
    </div>
    <div
      class="relative h-full overflow-hidden rounded-b-none lg:rounded-br-none lg:rounded-bl-[10vh] bg-cover bg-center"
      style={`background-image: url('${heroImage.src}');`}
      role="img"
      aria-label={websiteInfo?.description || "Imóvel em destaque"}
    >
    </div>
  </div>
</div>

<style define:vars={{ headingColor, lighterHeadingColor }}>
  .heading-color {
    color: var(--headingColor);
    border-color: var(--headingColor);
    @media screen and (max-width: 768px) {
      color: var(--lighterHeadingColor);
      border-color: var(--lighterHeadingColor);
    }
  }
</style>
