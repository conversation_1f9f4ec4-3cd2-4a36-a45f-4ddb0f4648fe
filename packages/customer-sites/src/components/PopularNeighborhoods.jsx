import React, { useState, useEffect } from 'react';

export function PopularNeighborhoods({ teamId, primaryColor }) {
  const [locations, setLocations] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(0);
  const locationsPerPage = 16; // Number of locations to show per page

  // Check if we're in a browser environment where sessionStorage is available
  const isBrowser = typeof window !== 'undefined' && typeof sessionStorage !== 'undefined';

  // Safely access sessionStorage
  const safeSessionStorage = {
    getItem: (key) => {
      try {
        return isBrowser ? sessionStorage.getItem(key) : null;
      } catch (e) {
        console.warn('Error accessing sessionStorage:', e);
        return null;
      }
    },
    setItem: (key, value) => {
      try {
        if (isBrowser) sessionStorage.setItem(key, value);
      } catch (e) {
        console.warn('Error writing to sessionStorage:', e);
      }
    }
  };

  useEffect(() => {
    // Function to fetch locations (cities and neighborhoods)
    const fetchLocations = async () => {
      console.log('PopularNeighborhoods: useEffect triggered with teamId:', teamId);

      // Skip if no teamId is provided
      if (!teamId) {
        console.log('PopularNeighborhoods: No teamId provided, skipping fetch');
        return;
      }

      // Check if we have cached locations for this team
      const sessionStorageKey = `teamLocations_${teamId}`;
      let cachedLocations;

      // Use our safe wrapper to access sessionStorage
      cachedLocations = safeSessionStorage.getItem(sessionStorageKey);

      if (cachedLocations) {
        try {
          const parsedLocations = JSON.parse(cachedLocations);

          // Only use cached locations if they're not empty
          if (parsedLocations && parsedLocations.length > 0) {
            console.log('PopularNeighborhoods: Using cached locations');
            setLocations(parsedLocations);
            return;
          }
        } catch (err) {
          console.error('Error parsing cached locations:', err);
          // Continue with fetching if parsing fails
        }
      }

      setIsLoading(true);
      setError(null);

      try {
        // Get the current URL to construct an absolute URL
        const currentUrl = new URL(window.location.href);
        const baseUrl = `${currentUrl.protocol}//${currentUrl.host}`;
        const apiUrl = `${baseUrl}/api/get-team-locations?teamId=${teamId}`;

        console.log(`PopularNeighborhoods: Fetching locations from: ${apiUrl}`);

        const response = await fetch(apiUrl);
        console.log(`PopularNeighborhoods: API response status:`, response.status, response.statusText);

        // Check if the response is ok
        if (!response.ok) {
          const errorText = await response.text();
          console.error(`API error response:`, errorText);
          throw new Error(`Failed to fetch locations: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('PopularNeighborhoods: API response data:', data);

        if (!data.locations || !Array.isArray(data.locations)) {
          console.warn('PopularNeighborhoods: No locations found or invalid response format');
          setLocations([]);
          setIsLoading(false);
          return;
        }

        console.log('PopularNeighborhoods: Found locations:', data.locations.length);

        // Update state with the locations
        setLocations(data.locations);

        // Store in sessionStorage to avoid redundant API calls
        safeSessionStorage.setItem(sessionStorageKey, JSON.stringify(data.locations));
      } catch (err) {
        console.error('Error fetching locations:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    // Call the fetch function
    fetchLocations();
  }, [teamId]);

  // Calculate total number of pages
  const totalNeighborhoods = locations.reduce((count, city) => count + city.neighborhoods.length, 0);
  const totalPages = Math.ceil(totalNeighborhoods / locationsPerPage);

  // Get the current page of neighborhoods
  const getCurrentPageNeighborhoods = () => {
    let allNeighborhoods = [];

    // Flatten the neighborhoods array with city information
    locations.forEach(city => {
      city.neighborhoods.forEach(neighborhood => {
        allNeighborhoods.push({
          cityId: city.id,
          cityName: city.name,
          neighborhoodId: neighborhood.id,
          neighborhoodName: neighborhood.name
        });
      });
    });

    // Sort alphabetically by city name, then neighborhood name
    allNeighborhoods.sort((a, b) => {
      if (a.cityName !== b.cityName) {
        return a.cityName.localeCompare(b.cityName);
      }
      return a.neighborhoodName.localeCompare(b.neighborhoodName);
    });

    // Get the current page slice
    const startIndex = currentPage * locationsPerPage;
    return allNeighborhoods.slice(startIndex, startIndex + locationsPerPage);
  };

  // Handle pagination
  const handlePrevPage = () => {
    setCurrentPage(prev => Math.max(0, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages - 1, prev + 1));
  };

  // Get neighborhoods for the current page
  const currentNeighborhoods = getCurrentPageNeighborhoods();

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="mb-16">
          <h2 className="text-2xl lg:text-3xl font-bold text-center mb-10">Nossos imóveis por região</h2>

          {isLoading && (
            <div className="text-center py-8">
              <p className="text-gray-500">Carregando localizações...</p>
            </div>
          )}

          {error && (
            <div className="text-center py-8">
              <p className="text-red-500">Erro ao carregar localizações: {error}</p>
            </div>
          )}

          {!isLoading && !error && locations.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">Nenhuma localização encontrada</p>
            </div>
          )}

          {!isLoading && !error && locations.length > 0 && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {currentNeighborhoods.map((item) => (
                  <a
                    key={`${item.cityId}-${item.neighborhoodId}`}
                    href={`/imoveis?city=${item.cityId}&neighborhood=${item.neighborhoodId}`}
                    className="text-sm text-gray-700 hover:text-primary transition-colors"
                    style={{ '--tw-text-opacity': 1, '--tw-text-primary': primaryColor }}
                  >
                    {item.neighborhoodName} - {item.cityName}
                  </a>
                ))}
              </div>

              {totalPages > 1 && (
                <div className="flex justify-center mt-8 gap-4">
                  <button
                    className="px-6 py-2 text-white rounded text-sm font-medium transition-opacity"
                    style={{ backgroundColor: primaryColor, opacity: currentPage === 0 ? 0.5 : 1 }}
                    onClick={handlePrevPage}
                    disabled={currentPage === 0}
                  >
                    Anterior
                  </button>
                  <span className="flex items-center text-sm text-gray-500">
                    Página {currentPage + 1} de {totalPages}
                  </span>
                  <button
                    className="px-6 py-2 text-white rounded text-sm font-medium transition-opacity"
                    style={{ backgroundColor: primaryColor, opacity: currentPage === totalPages - 1 ? 0.5 : 1 }}
                    onClick={handleNextPage}
                    disabled={currentPage === totalPages - 1}
                  >
                    Próximo
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </section>
  );
}
