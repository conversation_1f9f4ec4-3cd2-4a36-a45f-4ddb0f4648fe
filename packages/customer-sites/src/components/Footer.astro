---
// Import the formatToPhone function from brazilian-values
import { formatToPhone } from 'brazilian-values';
import Container from './Container.astro';

// Access the team profile data from Astro.locals
const { teamProfile, websiteInfo } = Astro.locals;

// Format WhatsApp number for link (remove non-digits)
const whatsappLink = teamProfile?.whatsapp_number
  ? `https://wa.me/${teamProfile.whatsapp_number.replace(/\D/g, '')}`
  : null;

// Format the WhatsApp number for display
const formattedWhatsappNumber = teamProfile?.whatsapp_number
  ? formatToPhone(teamProfile.whatsapp_number.replace(/\D/g, ''))
  : null;

// Get the logo image URL if available from the website object
const logoImageUrl = websiteInfo?.website?.logo_image_url;

// Format the phone number for display
const formattedPhoneNumber = teamProfile?.phone_number
  ? formatToPhone(teamProfile.phone_number.replace(/\D/g, ''))
  : null;
---

<!-- Footer -->
<footer class="bg-gray-50 text-gray-500 py-12">
    <Container>


            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <div class="mb-4">
                        {logoImageUrl ? (
                            <img
                                src={logoImageUrl}
                                alt={teamProfile?.name || websiteInfo?.title || "Logo"}
                                class="h-16 max-w-[200px] object-contain mb-2"
                            />
                        ) : (
                            <h3 class="text-xl">{teamProfile?.name || websiteInfo?.title}</h3>
                        )}
                    </div>
                    {websiteInfo?.description && <p class="text-gray-400 mb-4">{websiteInfo?.description}</p>}
                </div>
                <div>
                    <h3 class="text-lg lg:text-xl mb-2 lg:mb-3">Acesso rápido</h3>
                    <ul class="space-y-2">
                        <li><a href="/" class="text-gray-500 hover:underline hover:text-gray-800 transition-colors">Página inicial</a></li>
                        <li><a href="/imoveis" class="text-gray-500 hover:underline hover:text-gray-800 transition-colors">Imóveis</a></li>
                        <li><a href="/sobre" class="text-gray-500 hover:underline hover:text-gray-800 transition-colors">Sobre Nós</a></li>
                        <!-- <li><a href="/contato" class="text-gray-500 hover:underline hover:text-gray-800 transition-colors">Contato</a></li> -->
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg lg:text-xl mb-2 lg:mb-3">Contato</h3>
                    {teamProfile?.address && (
                        <p class="text-gray-400 mb-2 flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                            </svg>
                            {teamProfile?.address}
                        </p>
                    )}
                    {teamProfile?.phone_number && (
                        <p class="text-gray-400 mb-2 flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                            </svg>
                            {formattedPhoneNumber}
                        </p>
                    )}
                    {teamProfile?.whatsapp_number && (
                        <p class="text-gray-400 mb-2 flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-400" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413Z" />
                            </svg>
                            <a href={whatsappLink} class="text-green-400 hover:text-green-300 transition-colors">{formattedWhatsappNumber}</a>
                        </p>
                    )}
                    <!-- <p class="text-gray-400 mb-2">Email: <EMAIL></p> -->
                </div>
            </div>
            <div class="border-t border-gray-200 mt-8 pt-8 text-center text-gray-500">
                <p>&copy; {new Date().getFullYear()} {websiteInfo?.title}. Todos os direitos reservados.</p>
            </div>
    </Container>
</footer>
