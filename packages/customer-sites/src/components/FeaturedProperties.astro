---
// Get website info from Astro.locals (set by middleware)
const { websiteInfo } = Astro.locals;

console.log('FeaturedProperties: astro params websiteAddress:', Astro.params.websiteAddress);

// Debug information
console.log('FeaturedProperties: websiteInfo:', websiteInfo);
console.log('FeaturedProperties: teamId:', websiteInfo?.teamId);

// Fetch team properties from API
let properties = [];

try {
  // Try to get the team ID from different sources
  const teamId = websiteInfo?.teamId || websiteInfo?.website?.team_account_id;

  if (teamId) {
    console.log(`Fetching properties for team ID: ${teamId}`);

    // Get the current URL to construct an absolute URL
    const currentUrl = new URL(Astro.request.url);
    const baseUrl = `${currentUrl.protocol}//${currentUrl.host}`;
    const apiUrl = `${baseUrl}/api/get-team-properties-by-team-id?teamId=${teamId}&limit=8`;

    // For development, we can also use a direct API call to the database
    // This is useful when the API endpoint doesn't exist yet or is being developed
    console.log(`API URL: ${apiUrl}`);

    try {
      const propertiesResponse = await fetch(apiUrl);
      console.log(`API response status: ${propertiesResponse.status}`);

      if (propertiesResponse.ok) {
        const data = await propertiesResponse.json();
        console.log('API response data:', data);

        if (data.properties && data.properties.length > 0) {
          properties = data.properties;
          console.log(`Found ${properties.length} properties for team`);
        } else {
          console.log('No properties found for team');
        }
      } else if (propertiesResponse.status === 404) {
        console.log('API endpoint not found (404). Using fallback properties.');
        // Keep using the fallback properties
      } else {
        const errorText = await propertiesResponse.text();
        console.error(`Error response from properties API: ${propertiesResponse.status}`);
        console.error(`Error details: ${errorText}`);
      }
    } catch (fetchError) {
      console.error('Fetch error:', fetchError);
    }
  } else {
    console.log('No team ID available from any source. Using fallback properties.');
  }
} catch (error) {
  console.error('Error fetching team properties:', error);
}
---

{properties.length > 0 && (
  <section class="py-8 lg:py-16 px-4 max-w-7xl mx-auto">
    <h2 class="text-2xl lg:text-3xl font-bold text-center mb-8 lg:mb-12">Imóveis mais recentes</h2>

    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {properties.map((property) => (
      <a href={`/imoveis/${property.id}`} class="block">
        <div class="group relative rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow duration-300">
          <div class="relative pb-[66.66%] overflow-hidden"> <!-- 2:3 aspect ratio -->
            {property.image && property.image.src && !property.image.src.includes('unsplash.com/photo-1560448204-e02f11c3d0e2') ? (
              <img
                src={`https://wsrv.nl/?url=${property.image.src}&w=640&output=webp&q=85`}
                width="640"
                height="427"
                alt={`${property.title} em ${property.location}`}
                class="absolute inset-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                loading="lazy"
              />
            ) : (
              <div class="absolute inset-0 w-full h-full bg-gray-100 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
            )}
          </div>
          <div class="absolute top-3 left-3 bg-white py-1 px-3 rounded-md shadow-sm">
            <p class="text-sm font-medium">{property.title}</p>
            <p class="text-xs">{property.location}</p>
            {property.price && <p class="text-xs font-semibold text-primary mt-1">{property.price}</p>}
          </div>
        </div>
      </a>
    ))}
    </div>

    <div class="flex justify-center mt-10">
      <a href="/imoveis/" class="text-white px-6 py-2 rounded-md hover:bg-primary/90 transition-colors" style={`background-color: ${Astro.locals.websiteTheme?.primary_color};`}>
        Ver mais imóveis
      </a>
    </div>
  </section>
)}
