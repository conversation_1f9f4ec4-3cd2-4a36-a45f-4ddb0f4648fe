import React, { useState, useEffect, memo, useRef } from 'react';

// Property type mapping from codes to display names
const PROPERTY_TYPE_VALUE_LABEL_MAP = {
  apartment: "Apartamento",
  house: "Casa",
  condominium_house: "Casa de Condomínio",
  penthouse: "Cobertura",
  flat: "Flat",
  studio: "Kitnet / Conjugado",
  lot: "Lote / Terreno",
  townhouse: "Sobrado",
  residential_building: "Edifício Residencial",
  rural_property: "Fazenda / Sítios / Chácaras",
  medical_office: "Consultório",
  warehouse: "Galpão / Depósito / Armazém",
  commercial_property: "Imóvel Comercial",
  commercial_lot: "Lote / Terreno Comercial",
  store: "Ponto Comercial / Loja / Box",
  office: "Sala/Conjunto",
  commercial_building: "Prédio/casa comercial",
};

function PropertyTypeSelectComponent({ primaryColor, teamId }) {
  console.log('PropertyTypeSelect: Component initialized with teamId:', teamId, 'primaryColor:', primaryColor);
  
  // Add client-side detection log
  if (typeof window !== 'undefined') {
    console.log('PropertyTypeSelect: Running on CLIENT SIDE');
  } else {
    console.log('PropertyTypeSelect: Running on SERVER SIDE');
  }
  
  const [propertyTypes, setPropertyTypes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedPropertyType, setSelectedPropertyType] = useState(null);
  const [useNativeSelect, setUseNativeSelect] = useState(false);
  const selectRef = useRef(null);

  // Handler for when a property type is selected
  const handlePropertyTypeChange = (value) => {
    console.log('PropertyTypeSelect: Type selected:', value);
    setSelectedPropertyType(value);

    // Set the global variable for the search button to use
    if (typeof window !== 'undefined') {
      window.__selectedPropertyType = value;
      console.log('Set global property type variable:', window.__selectedPropertyType);
    }
  };

  // Check if we're in a browser environment where sessionStorage is available
  const isBrowser = typeof window !== 'undefined' && typeof sessionStorage !== 'undefined';

  // Safely access sessionStorage
  const safeSessionStorage = {
    getItem: (key) => {
      try {
        return isBrowser ? sessionStorage.getItem(key) : null;
      } catch (e) {
        console.warn('Error accessing sessionStorage:', e);
        return null;
      }
    },
    setItem: (key, value) => {
      try {
        if (isBrowser) sessionStorage.setItem(key, value);
      } catch (e) {
        console.warn('Error writing to sessionStorage:', e);
      }
    }
  };

  useEffect(() => {
    console.log('PropertyTypeSelect: useEffect triggered with teamId:', teamId, 'typeof window:', typeof window);
    
    // Function to fetch properties and extract unique types
    const fetchPropertyTypes = async () => {
      console.log('PropertyTypeSelect: fetchPropertyTypes called with teamId:', teamId);
      
      // Skip if no teamId is provided
      if (!teamId) {
        console.log('PropertyTypeSelect: No teamId provided, skipping fetch');
        return;
      }

      // Check if we have cached property types for this team
      const sessionStorageKey = `propertyTypes_${teamId}`;
      let cachedTypes;

      // Use our safe wrapper to access sessionStorage
      cachedTypes = safeSessionStorage.getItem(sessionStorageKey);

      if (cachedTypes) {
        try {
          const parsedTypes = JSON.parse(cachedTypes);

          // Only use cached types if they're not empty
          if (parsedTypes && parsedTypes.length > 0) {
            setPropertyTypes(parsedTypes);
            return;
          }
        } catch (err) {
          console.error('Error parsing cached property types:', err);
          // Continue with fetching if parsing fails
        }
      }

      setIsLoading(true);
      setError(null);

      try {
        // Get the current URL to construct an absolute URL
        const currentUrl = new URL(window.location.href);
        const baseUrl = `${currentUrl.protocol}//${currentUrl.host}`;
        const apiUrl = `${baseUrl}/api/get-team-property-types?teamId=${teamId}`;

        console.log(`Fetching property types from: ${apiUrl}`);

        const response = await fetch(apiUrl);
        console.log(`API response status:`, response.status, response.statusText);

        // Check if the response is ok
        if (!response.ok) {
          const errorText = await response.text();
          console.error(`API error response:`, errorText);
          throw new Error(`Failed to fetch property types: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (!data.propertyTypes || !Array.isArray(data.propertyTypes)) {
          console.warn('No property types found or invalid response format');
          setPropertyTypes([]);
          setIsLoading(false);
          return;
        }

        // Filter out any null or undefined types (shouldn't happen with our API, but just in case)
        const validTypes = data.propertyTypes.filter(type => type);

        // Update state with the valid types
        setPropertyTypes(validTypes);

        // Store in sessionStorage to avoid redundant API calls
        safeSessionStorage.setItem(sessionStorageKey, JSON.stringify(validTypes));
      } catch (err) {
        console.error('Error fetching property types:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    // Call the fetch function
    fetchPropertyTypes();
  }, [teamId]);

  // Initialize Preline select after component mounts and data changes
  useEffect(() => {
    if (typeof window === 'undefined' || !selectRef.current || useNativeSelect) return;

    let retryCount = 0;
    const maxRetries = 20; // Try for 2 seconds
    let cleanupFunction = null;

    // Wait for Preline to be available
    const initializeSelect = () => {
      if (window.HSSelect) {
        try {
          const selectElement = selectRef.current;
          if (selectElement) {
            // Destroy existing instance if it exists
            const existingInstance = window.HSSelect.getInstance(selectElement);
            if (existingInstance) {
              existingInstance.destroy();
            }
            
            // Force a small delay to ensure DOM is updated
            setTimeout(() => {
              // Initialize the select
              window.HSSelect.autoInit();
              
              // Add event listener for selection changes
              const handleChange = (e) => {
                const selectedValue = e.detail.payload;
                if (selectedValue && selectedValue !== 'loading' && selectedValue !== 'error' && selectedValue !== 'none') {
                  handlePropertyTypeChange(selectedValue);
                }
              };
              
              selectElement.addEventListener('change.hs.select', handleChange);
              
              cleanupFunction = () => {
                selectElement.removeEventListener('change.hs.select', handleChange);
                const instance = window.HSSelect.getInstance(selectElement);
                if (instance) {
                  instance.destroy();
                }
              };
            }, 10);
          }
        } catch (error) {
          console.warn('Error initializing Preline select:', error);
          setUseNativeSelect(true);
        }
      } else {
        retryCount++;
        if (retryCount < maxRetries) {
          // Retry after a short delay if Preline isn't loaded yet
          setTimeout(initializeSelect, 100);
        } else {
          console.warn('Preline UI not available, falling back to native select');
          setUseNativeSelect(true);
        }
      }
    };

    // Start initialization
    initializeSelect();
    
    // Cleanup function
    return () => {
      if (cleanupFunction) {
        cleanupFunction();
      }
    };
  }, [propertyTypes, isLoading, error, useNativeSelect]);

  // Handle native select change
  const handleNativeSelectChange = (e) => {
    const value = e.target.value;
    if (value && value !== 'loading' && value !== 'error' && value !== 'none') {
      handlePropertyTypeChange(value);
    }
  };

  // Render select options based on state
  const renderSelectOptions = () => {
    if (isLoading) {
      return <option value="loading" disabled>Carregando...</option>;
    }

    if (error) {
      return <option value="error" disabled>Erro ao carregar tipos</option>;
    }

    if (propertyTypes && propertyTypes.length > 0) {
      console.log(propertyTypes)
      return propertyTypes.map(type => (
        <option key={type} value={type}>
          {PROPERTY_TYPE_VALUE_LABEL_MAP[type] || type}
        </option>
      ));
    }

    // Always show at least one selectable option
    return <option value="none">Nenhum imóvel cadastrado</option>;
  };

  return (
    <div className="relative">
      <div className="absolute inset-y-0 left-3 flex items-center z-10 pointer-events-none">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
          <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
        </svg>
      </div>
      
      {useNativeSelect ? (
         <select
           ref={selectRef}
           onChange={handleNativeSelectChange}
           className="w-full pl-10 pr-4 py-6 bg-white border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:border-transparent shadow-xl md:shadow-none"
           style={{ "--tw-ring-color": primaryColor }}
           disabled={isLoading}
         >
           <option value="">Tipo de imóvel</option>
           {renderSelectOptions()}
         </select>
       ) : (
         <select 
           ref={selectRef}
           data-hs-select={JSON.stringify({
             placeholder: "Tipo de imóvel",
             toggleTag: '<button type="button" aria-expanded="false"></button>',
             toggleClasses: "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative w-full pl-10 pr-4 py-4 flex gap-x-2 text-nowrap cursor-pointer bg-white border border-gray-300 rounded-md text-start text-sm focus:outline-none focus:ring-1 focus:border-transparent shadow-xl md:shadow-none",
             dropdownClasses: "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
             optionClasses: "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-none focus:bg-gray-100 hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
             optionTemplate: '<div class="flex justify-between items-center w-full"><span data-title></span><span class="hidden hs-selected:block"><svg class="shrink-0 size-3.5 text-blue-600 dark:text-blue-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"/></svg></span></div>',
             extraMarkup: '<div class="absolute top-1/2 end-3 -translate-y-1/2"><svg class="shrink-0 size-3.5 text-gray-500 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m7 15 5 5 5-5"/><path d="m7 9 5-5 5 5"/></svg></div>'
           })}
           className="hidden"
           style={{ "--tw-ring-color": primaryColor }}
         >
           <option value="">Tipo de imóvel</option>
           {renderSelectOptions()}
         </select>
       )}
    </div>
  );
}

// Export a memoized version of the component to prevent unnecessary re-renders
export const PropertyTypeSelect = memo(PropertyTypeSelectComponent);