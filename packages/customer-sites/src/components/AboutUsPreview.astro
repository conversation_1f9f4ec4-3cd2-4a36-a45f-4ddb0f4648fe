---
import { formatToPhone } from 'brazilian-values';
import Color from 'colorjs.io';

// Access the team profile data and website info from props or Astro.locals
const { teamProfile = Astro.locals.teamProfile, websiteInfo = Astro.locals.websiteInfo, websiteTheme = Astro.locals.websiteTheme } = Astro.props;

// Get the institutional image URL if available from the website object
const institutionalImageUrl = websiteInfo?.website?.institutional_image_url;

// Calculate a lighter version of the primary color for the background
let bgColor = '#f8f9fa'; // Default light gray background
if (websiteTheme?.primary_color) {
  try {
    // Create a Color object from the primary color
    const primaryColor = new Color(websiteTheme.primary_color);

    // Mix with white to lighten it (90% white, 10% primary color)
    const lightColor = Color.mix(primaryColor, 'white', 0.9, {space: 'lch'});

    // Convert to sRGB and get the hex string
    bgColor = lightColor.to('srgb').toString({format: 'hex'});
  } catch (error) {
    console.error('Error calculating background color:', error);
    // Keep the default background color if there's an error
  }
}

// Calculate a darker version of the primary color for headings
let headingColor = '#333333'; // Default dark gray for headings
if (websiteTheme?.primary_color) {
  try {
    // Create a Color object from the primary color
    const primaryColor = new Color(websiteTheme.primary_color);

    // Mix with black to darken it (30% black, 70% primary color)
    const darkColor = Color.mix(primaryColor, 'black', 0.3, {space: 'lch'});

    // Convert to sRGB and get the hex string
    headingColor = darkColor.to('srgb').toString({format: 'hex'});
  } catch (error) {
    console.error('Error calculating heading color:', error);
    // Keep the default heading color if there's an error
  }
}

// Format the phone number if available
const formattedPhoneNumber = teamProfile?.phone_number ? formatToPhone(teamProfile.phone_number) : '';

// Truncate the about_us text to 400 characters if it's longer
const aboutUsText = teamProfile?.about_us || '';
const isTruncated = aboutUsText.length > 400;
const truncatedText = isTruncated ? aboutUsText.substring(0, 400) + '...' : aboutUsText;
---

<section class="py-8 lg:py-16" style={`background-color: ${bgColor};`}>
  <div class="container mx-auto px-4">
    <div class="flex flex-col md:flex-row items-start gap-8">
      <!-- Image Section -->
      <div class="w-full md:w-1/3 lg:w-1/4">
        {institutionalImageUrl ? (
          <img
            src={institutionalImageUrl}
            alt={teamProfile?.name || websiteInfo?.title || "Imagem institucional"}
            class="w-full h-auto rounded-lg shadow-sm object-cover mx-auto"
            style="aspect-ratio: 4/3;"
          />
        ) : (
          <div
            class="w-full rounded-lg shadow-sm bg-gray-200 flex items-center justify-center mx-auto"
            style="aspect-ratio: 4/3;"
          >
            <p class="text-gray-500 text-center p-4">Imagem institucional não disponível</p>
          </div>
        )}
      </div>

      <!-- About Us Text Section -->
      <div class="w-full md:w-2/3 lg:w-3/4">
        <h2 class="text-2xl font-bold mb-3" style={`color: ${headingColor};`}>Sobre Nós</h2>
        <div class="w-20 h-1 mb-4" style={`background-color: ${websiteTheme?.primary_color || '#6b0f2b'};`}></div>

        {aboutUsText ? (
          <div>
            <p class="text-gray-700 mb-4">{truncatedText}</p>
            {isTruncated && (
              <a
                href="/sobre"
                class="inline-block px-4 py-2 rounded-md text-white transition-colors hover:opacity-90"
                style={`background-color: ${websiteTheme?.primary_color || '#6b0f2b'};`}
              >
                Continue lendo
              </a>
            )}
          </div>
        ) : (
          <p class="text-gray-500 italic">Informações sobre a empresa não disponíveis.</p>
        )}

        {teamProfile?.phone_number && (
          <div class="mt-4">
            <p class="text-gray-700">
              <span class="font-medium">Contato:</span> {formattedPhoneNumber}
            </p>
          </div>
        )}
      </div>
    </div>
  </div>
</section>
