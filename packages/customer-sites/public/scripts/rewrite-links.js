// This script rewrites internal links to include the website address prefix
// when running in local development mode

document.addEventListener('DOMContentLoaded', () => {
  // Extract the website address from the URL path
  const pathParts = window.location.pathname.split('/');
  let pathPrefix = '';
  let websiteAddress = window.websiteAddress || '';

  // First try to use the first path segment as the website address prefix
  if (pathParts.length > 1 && pathParts[1]) {
    pathPrefix = `/${pathParts[1]}`;
  }
  // If no path prefix found but we have websiteAddress from the window object
  else if (websiteAddress) {
    pathPrefix = `/${websiteAddress}`;
  }

  if (!pathPrefix) {
    console.log('No website address prefix found for link rewriting');
    return;
  }

  console.log(`Rewriting links with prefix: ${pathPrefix}`);


  // Function to rewrite links
  function rewriteLinks() {
    // Get all links on the page
    const links = document.querySelectorAll('a[href]');

    links.forEach(link => {
      const href = link.getAttribute('href');

      // Only process internal links that start with / but don't already include our prefix
      if (href && href.startsWith('/') && !href.startsWith(pathPrefix) && !href.startsWith('//')) {
        // Skip links that are already prefixed or are protocol-relative URLs
        const newHref = `${pathPrefix}${href}`;
        link.setAttribute('href', newHref);
      }
    });
  }

  // Rewrite links on initial load
  rewriteLinks();

  // Set up a MutationObserver to handle dynamically added links
  const observer = new MutationObserver((mutations) => {
    let shouldRewrite = false;

    mutations.forEach(mutation => {
      if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
        shouldRewrite = true;
      }
    });

    if (shouldRewrite) {
      rewriteLinks();
    }
  });

  // Start observing the document with the configured parameters
  observer.observe(document.body, { childList: true, subtree: true });
});
