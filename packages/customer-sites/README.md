# Customer Sites

This is an Astro application that serves as the customer-facing website platform for Imoblr. It allows real estate teams to have their own branded websites with property listings and team information.

## NX Monorepo Integration

This package is part of the Imoblr Platform monorepo and is managed using NX. Dependencies are installed at the root level of the monorepo.

## Development

To run the application locally:

```bash
# From the root of the monorepo
bun run dev:customer-sites

# Or using nx directly
nx dev customer-sites
```

## Building

To build the application:

```bash
# From the root of the monorepo
bun run build:customer-sites

# Or using nx directly
nx build customer-sites
```

## Technology Stack

- [Astro](https://astro.build) - Web framework
- [Tailwind CSS](https://tailwindcss.com) - CSS framework
- [React](https://reactjs.org) - UI library for interactive components
- [Cloudflare Pages](https://pages.cloudflare.com) - Hosting platform
- [Supabase](https://supabase.io) - Backend services

## Architecture

The customer sites application works in conjunction with the proxy package to route requests to the appropriate customer site based on the domain or subdomain.
