import * as fs from 'fs';
import * as path from 'path';

// Get the directory of the current file
const currentDir = path.dirname(new URL(import.meta.url).pathname);

const iconsDir = path.join(currentDir, '../src/icons');
const typeFile = path.join(currentDir, '../src/icons/index.d.ts');

// Get all SVG files in the icons directory
const svgFiles = fs
  .readdirSync(iconsDir)
  .filter((file: string) => file.endsWith('.svg'))
  .map((file: string) => file.replace('.svg', ''));

// Generate type declarations
const typeDeclarations = `import { React } from 'react';
import { SVGProps } from 'react';

export type Icon = React.FunctionComponent<SVGProps<SVGSVGElement>>;

${svgFiles
  .map(icon => `export const ${icon}: Icon;`)
  .join('\n')}
`;

// Write to file
fs.writeFileSync(typeFile, typeDeclarations);

console.log('Type declarations generated successfully!');
