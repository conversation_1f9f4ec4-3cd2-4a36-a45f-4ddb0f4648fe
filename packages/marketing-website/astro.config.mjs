// @ts-check
import { defineConfig } from "astro/config";
import tailwindcss from '@tailwindcss/vite';

import preact from '@astrojs/preact';

// https://astro.build/config
export default defineConfig({
  integrations: [preact()],
  image: {
    domains: ["wsrv.nl", "images.unsplash.com"],
    remotePatterns: [{ protocol: "https" }]
  },

  experimental: {
    fonts: [
      {
        provider: 'local',
        name: 'Inter',
        cssVariable: '--font-inter',
        variants: [
          {
            weight: 400,
            style: 'normal',
            src: ['./src/assets/fonts/Inter-VariableFont_opsz,wght.ttf']
          },
          {
            weight: 500,
            style: 'normal',
            src: ['./src/assets/fonts/Inter-VariableFont_opsz,wght.ttf']
          },
          {
            weight: 600,
            style: 'normal',
            src: ['./src/assets/fonts/Inter-VariableFont_opsz,wght.ttf']
          },
          {
            weight: 700,
            style: 'normal',
            src: ['./src/assets/fonts/Inter-VariableFont_opsz,wght.ttf']
          }
        ]
      },
      {
        provider: "local",
        name: "Title",
        cssVariable: "--font-title",
        variants: [
          {
            weight: 400,
            style: 'normal',
            src: ['./src/assets/fonts/Inter-VariableFont_opsz,wght.ttf']
          },
          {
            weight: 500,
            style: 'normal',
            src: ['./src/assets/fonts/Inter-VariableFont_opsz,wght.ttf']
          },
          {
            weight: 600,
            style: 'normal',
            src: ['./src/assets/fonts/Inter-VariableFont_opsz,wght.ttf']
          },
          {
            weight: 700,
            style: 'normal',
            src: ['./src/assets/fonts/Inter-VariableFont_opsz,wght.ttf']
          }
        ]
      }
  ]
},

  vite: {
    plugins: [
      tailwindcss(),
    ]
  },
  integrations: [preact({ compat: true, devtools: true })],
});