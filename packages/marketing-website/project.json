{"name": "marketing-website", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/marketing-website", "projectType": "application", "targets": {"build": {"executor": "nx:run-commands", "options": {"cwd": "packages/marketing-website", "command": "astro build"}}, "dev": {"executor": "nx:run-commands", "options": {"cwd": "packages/marketing-website", "command": "astro dev --host 0.0.0.0"}}, "preview": {"executor": "nx:run-commands", "options": {"cwd": "packages/marketing-website", "command": "astro preview"}}, "lint": {"executor": "nx:run-commands", "options": {"cwd": "packages/marketing-website", "command": "biome lint --unsafe"}}}, "tags": []}