import React, { useState } from 'preact/compat';
const MailIcon = () => (
    <svg
        className="w-5 h-5 text-gray-500 dark:text-gray-400"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
    >
        <rect width="20" height="16" x="2" y="4" rx="2"/>
        <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"/>
    </svg>
);

const WhatsappIcon = () => (
    <svg
        className="w-5 h-5 text-gray-500 dark:text-gray-400"
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
    >
        <path d="M3 21l1.65-3.8a9 9 0 1 1 3.4 2.9L3 21"/>
        <path d="M9 10a.5.5 0 0 0 1 0V9a.5.5 0 0 0-1 0v1Zm0 0a5 5 0 0 0 5 5"/>
    </svg>
);

const SignUpForm: React.FC = () => {
    const [firstName, setFirstName] = useState('');
    const [lastName, setLastName] = useState('');
    const [email, setEmail] = useState('');
    const [whatsapp, setWhatsapp] = useState('');

    return (
        <form>
            <div className="lg:max-w-lg lg:mx-auto lg:me-0 ms-auto">
                {/* Card */}
                <div
                    className="p-4 lg:p-8 flex flex-col bg-white border border-slate-200 rounded-2xl shadow-xl shadow-slate-500/10 dark:bg-neutral-900"
                >
                    <div className="text-center mb-4">
                        <h3
                            className="block mb-2 md:mb-4 font-semibold text-primary-950 text-lg md:text-xl lg:text-2xl dark:text-neutral-200"
                        >
                            <span
                                className="bg-clip-text font-medium bg-linear-to-t from-primary-600/30 to-primary-950 to-50% text-transparent dark:from-secondary-400 dark:to-primary-400"
                            >Comece seu
                            </span><br className="inline md:hidden" />
                            <span
                                className="bg-clip-text font-black bg-linear-to-tr from-primary-600 from-10% to-primary-500/20 to-90% text-transparent dark:from-primary-400 dark:to-primary-400"
                            >teste grátis</span
                            >
                        </h3>
                        <p
                            className="text-xs text-slate-500 dark:text-neutral-400 max-w-3/4 mx-auto"
                        >
                            Teste grátis todas as funcionalidades da nossa plataforma
                            por 7 dias. Não precisa de cartão!
                        </p>
                    </div>

                    <div className="mt-5">
                        {/* Grid */}
                        <div className="grid grid-cols-1 gap-4">
                            <div className="flex flex-row items-center gap-4">
                                <div className="space-y-2 flex-1">
                                    <label
                                        htmlFor="firstName"
                                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                                    >Nome</label
                                    >
                                    <input
                                        type="text"
                                        id="firstName"
                                        placeholder="Ex: João"
                                        className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                        value={firstName}
                                        onInput={(e) => setFirstName((e.target as HTMLInputElement).value)}
                                    />
                                </div>

                                <div className="space-y-2 flex-1">
                                    <label
                                        htmlFor="lastName"
                                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                                    >Sobrenome</label
                                    >
                                    <input
                                        type="text"
                                        id="lastName"
                                        placeholder="Ex: da Silva"
                                        className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                        value={lastName}
                                        onInput={(e) => setLastName((e.target as HTMLInputElement).value)}
                                    />
                                </div>
                            </div>

                            <div className="space-y-2 flex-1">
                                <label
                                    htmlFor="email"
                                    className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                                >Email XXX</label
                                >
                                <div className="relative">
                                    <div
                                        className="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none"
                                    >
                                        <MailIcon />
                                    </div>
                                    <input
                                        type="email"
                                        id="email"
                                        placeholder="Ex: <EMAIL>"
                                        className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                        value={email}
                                        onInput={(e) => setEmail((e.target as HTMLInputElement).value)}
                                    />
                                </div>
                            </div>

                            {/* Input Group */}
                            <div className="relative col-span-full">
                                <div className="space-y-2 flex-1">
                                    <label
                                        htmlFor="whatsapp"
                                        className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
                                    >Whatsapp</label
                                    >
                                    <div className="relative">
                                        <div
                                            className="absolute inset-y-0 start-0 flex items-center ps-3.5 pointer-events-none"
                                        >
                                            <WhatsappIcon />
                                        </div>
                                        <input
                                            type="tel"
                                            id="whatsapp"
                                            placeholder="(11) 9 9543-2569"
                                            className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full ps-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                            value={whatsapp}
                                            onInput={(e) => setWhatsapp((e.target as HTMLInputElement).value)}
                                        />
                                    </div>
                                </div>
                            </div>
                            {/* End Input Group */}
                        </div>
                        {/* End Grid */}

                        {/* Checkbox */}
                        <div className="flex items-center justify-center">
                            <p className="text-xs text-center my-4 text-gray-500 max-w-3/4">
                                Ao clicar em "Iniciar teste grátis", você concorda com
                                os termos de uso e políticas de privacidade.
                            </p>
                        </div>
                        {/* End Checkbox */}

                        <a
                            className="w-full text-center py-2 md:py-3 px-4 inline-flex items-center justify-center gap-x-2 text-sm font-medium rounded-full border border-transparent bg-gray-950 text-white hover:opacity-80 focus:outline-hidden focus:bg-gray-700 disabled:opacity-50 disabled:pointer-events-none"
                            href="/teste-gratis"
                        >
                            Iniciar teste grátis
                            <svg
                                className="shrink-0 size-4"
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            >
                                <path d="m9 18 6-6-6-6"></path>
                            </svg>
                        </a>
                    </div>
                </div>
                {/* End Card */}
            </div>
        </form>
    );
};

export default SignUpForm;