---
import Layout from "../../layouts/Layout.astro";
import SignUpForm from "./_components/SignUpForm";


import MainNavbar from "../../components/MainNavbar.astro";
import MainFooter from "../../components/MainFooter.astro";
import mockLogo1 from "../../assets/logos/mock-logo-1.png";
import mockLogo5 from "../../assets/logos/mock-logo-5.png";
import mockLogo6 from "../../assets/logos/mock-logo-6.png";
import mockLogo7 from "../../assets/logos/mock-logo-7.png";
import mockLogo8 from "../../assets/logos/mock-logo-8.png";
import mockLogo9 from "../../assets/logos/mock-logo-9.png";
---

<Layout pageTitle="Faça um teste grátis">
    <MainNavbar />
    <!-- Hero -->
    <div
        class="relative pt-16 bg-linear-to-bl from-violet-100 via-transparent dark:from-violet-950 dark:via-transparent"
    >
        <div class="max-w-[85rem] px-4 py-10 sm:px-6 lg:px-8 lg:py-14 mx-auto">
            <!-- Grid -->
            <div class="grid items-start md:grid-cols-2 gap-8 lg:gap-12">
                <div>
                    <div
                        class="p-[1px] w-fit mb-2 md:mb-4 backdrop-blur-lg rounded-full bg-linear-to-tr from-primary-300 to-secondary-300 opacity-80"
                    >
                        <div
                            class="px-3 py-0.5 md:py-1 rounded-full bg-linear-to-t from-primary-50 to-white"
                        >
                            <p
                                class="inline-block text-xs md:text-sm font-medium bg-clip-text bg-linear-to-tr from-primary-500 to-secondary-500 text-transparent dark:from-secondary-400 dark:to-primary-400"
                            >
                                Teste a melhor plataforma imobiliária grátis!
                            </p>
                        </div>
                    </div>

                    <!-- Title -->
                    <div class="mt-4 md:mb-12 max-w-2xl">
                        <h1
                            class="block leading-10 md:leading-16 font-semibold text-primary-950 text-3xl md:text-4xl lg:text-5xl dark:text-neutral-200"
                        >
                            <span
                                class="bg-clip-text font-medium bg-linear-to-t from-primary-600/30 to-primary-950 to-50% text-transparent dark:from-secondary-400 dark:to-primary-400"
                                >Comece seu
                            </span><br class="inline md:hidden" />
                            <span
                                class="bg-clip-text font-black bg-linear-to-tr from-primary-600 from-10% to-primary-500/20 to-90% text-transparent dark:from-primary-400 dark:to-primary-400"
                                >teste grátis</span
                            >
                            <br />
                            <span
                                class="bg-clip-text font-medium bg-linear-to-t from-primary-600/30 to-primary-950 to-50% text-transparent dark:from-secondary-400 dark:to-primary-400"
                                >e transforme sua imobiliária</span
                            >
                        </h1>
                        <p
                            class="text-sm my-2 md:my-6 md:text-base lg:text-lg text-secondary-950/60 dark:text-neutral-400"
                        >
                            Acesso total à todas as funcionalidades e
                            ferramentas da melhor plataforma imobiliária. 100%
                            grátis e sem compromisso!
                        </p>
                    </div>
                    <!-- End Title -->

                    <!-- Blockquote -->
                    <blockquote class="hidden md:block relative max-w-sm">
                        <svg
                            class="absolute top-0 start-0 transform -translate-x-6 -translate-y-8 size-16 text-gray-200 dark:text-neutral-800"
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            aria-hidden="true"
                        >
                            <path
                                d="M7.39762 10.3C7.39762 11.0733 7.14888 11.7 6.6514 12.18C6.15392 12.6333 5.52552 12.86 4.76621 12.86C3.84979 12.86 3.09047 12.5533 2.48825 11.94C1.91222 11.3266 1.62421 10.4467 1.62421 9.29999C1.62421 8.07332 1.96459 6.87332 2.64535 5.69999C3.35231 4.49999 4.33418 3.55332 5.59098 2.85999L6.4943 4.25999C5.81354 4.73999 5.26369 5.27332 4.84476 5.85999C4.45201 6.44666 4.19017 7.12666 4.05926 7.89999C4.29491 7.79332 4.56983 7.73999 4.88403 7.73999C5.61716 7.73999 6.21938 7.97999 6.69067 8.45999C7.16197 8.93999 7.39762 9.55333 7.39762 10.3ZM14.6242 10.3C14.6242 11.0733 14.3755 11.7 13.878 12.18C13.3805 12.6333 12.7521 12.86 11.9928 12.86C11.0764 12.86 10.3171 12.5533 9.71484 11.94C9.13881 11.3266 8.85079 10.4467 8.85079 9.29999C8.85079 8.07332 9.19117 6.87332 9.87194 5.69999C10.5789 4.49999 11.5608 3.55332 12.8176 2.85999L13.7209 4.25999C13.0401 4.73999 12.4903 5.27332 12.0713 5.85999C11.6786 6.44666 11.4168 7.12666 11.2858 7.89999C11.5215 7.79332 11.7964 7.73999 12.1106 7.73999C12.8437 7.73999 13.446 7.97999 13.9173 8.45999C14.3886 8.93999 14.6242 9.55333 14.6242 10.3Z"
                                fill="currentColor"></path>
                        </svg>

                        <div class="relative z-10">
                            <p
                                class="text-xl italic text-gray-800 dark:text-white"
                            >
                                Muito fácil de usar e simplifica muito o meu
                                trabalho
                            </p>
                        </div>

                        <footer class="mt-3">
                            <div class="flex items-center gap-x-4">
                                <div class="shrink-0">
                                    <img
                                        class="size-8 rounded-full"
                                        src="https://images.unsplash.com/photo-1492562080023-ab3db95bfbce?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=facearea&facepad=2&w=320&h=320&q=80"
                                        alt="Avatar"
                                    />
                                </div>
                                <div class="grow">
                                    <div
                                        class="font-semibold text-gray-800 dark:text-neutral-200"
                                    >
                                        João Pedro Pereira Cardoso
                                    </div>
                                    <div
                                        class="text-xs text-gray-500 dark:text-neutral-500"
                                    >
                                        Diretor de Operações | Plus+Imobi
                                    </div>
                                </div>
                            </div>
                        </footer>
                    </blockquote>
                    <!-- End Blockquote -->
                </div>
                <!-- End Col -->

                <div>
                    <SignUpForm client:load />
                </div>
                <!-- End Col -->
            </div>
            <!-- End Grid -->

            <!-- Clients -->
            <div
                class="mt-6 md:mt-12 py-3 flex items-center text-sm text-gray-800 gap-x-1.5 after:flex-1 after:border-t after:border-gray-200 after:ms-6 dark:text-white dark:after:border-neutral-700"
            >
                <span
                    class="font-semibold bg-clip-text bg-linear-to-l from-violet-600 to-violet-500 text-transparent dark:from-violet-400 dark:to-violet-400"
                    >Centenas de clientes</span
                >
                confiam na Imoblr
            </div>

            <!-- Clients -->
            <div
                class="flex flex-wrap items-center justify-center gap-x-6 sm:gap-x-12 lg:gap-x-24 grayscale-100"
            >
                <img
                    class="py-3 lg:py-5 w-16 h-auto md:w-20 lg:w-24 text-gray-500 dark:text-neutral-500"
                    src={mockLogo1.src}
                    alt="mock-logo-1"
                />
                <img
                    class="py-3 lg:py-5 w-16 h-auto md:w-20 lg:w-24 text-gray-500 dark:text-neutral-500"
                    src={mockLogo5.src}
                    alt="mock-logo-5"
                />
                <img
                    class="py-3 lg:py-5 w-16 h-auto md:w-20 lg:w-24 text-gray-500 dark:text-neutral-500"
                    src={mockLogo6.src}
                    alt="mock-logo-6"
                />
                <img
                    class="py-3 lg:py-5 w-16 h-auto md:w-20 lg:w-24 text-gray-500 dark:text-neutral-500"
                    src={mockLogo7.src}
                    alt="mock-logo-7"
                />
                <img
                    class="py-3 lg:py-5 w-16 h-auto md:w-20 lg:w-24 text-gray-500 dark:text-neutral-500"
                    src={mockLogo8.src}
                    alt="mock-logo-8"
                />
                <img
                    class="py-3 lg:py-5 w-16 h-auto md:w-20 lg:w-24 text-gray-500 dark:text-neutral-500"
                    src={mockLogo9.src}
                    alt="mock-logo-9"
                />
            </div>
        </div>
        <!-- End Clients -->
    </div>
    <!-- End Clients Section -->
</Layout>
<!-- End Hero -->
<MainFooter />
