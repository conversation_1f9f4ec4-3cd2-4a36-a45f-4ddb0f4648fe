import React, { useState, useEffect } from 'preact/compat';
import { cn } from '@/utils/cn';

interface FeatureDetail {
  included?: string[];
  excluded?: string[];
}

interface Plan {
  name: string;
  monthlyPrice: number;
  annualPrice: number;
  description: string;
  features: FeatureDetail;
  cta: string;
  highlight: boolean;
}

// Pricing data
const plans: Plan[] = [
  {
    name: "Li<PERSON>",
    monthlyPrice: 48,
    annualPrice: 40,
    description:
      "Plano individual. Ideal para quem está iniciando seu negócio.",
    features: {
      included: [
        "Apenas 1 colaborador",
        "Até 50 imóveis",
        "Website personalizável",
        "Gestão de leads",
        "Gestão de agenda (Em breve)",
        "16 créditos IA (Em breve)",
        "Gestão financeira (Em breve)",
        "Gestão de filiais (Em breve)",
      ],
      excluded: [
        "Use seu próprio domínio",
        "Certificado SSL",
        "Consultoria de design",
        "Consultoria de marketing",
      ],
    },
    cta: "Faça um teste grátis",
    highlight: false,
  },
  {
    name: "Pro",
    monthlyPrice: 64,
    annualPrice: 54,
    description:
      "Para negócios intermediários que precisam de mais funcionalidades.",
    features: {
      included: [
        "Gestão de colaboradores",
        "150 imóveis por colaborador ativo",
        "Até 3 websites personalizáveis",
        "Use seu próprio domínio",
        "Certificado SSL",
        "Gestão de leads",
        "Gestão de agenda (Em breve)",
        "32 créditos IA (Em breve)",
        "Gestão financeira (Em breve)",
        "Gestão de filiais (Em breve)",
      ],
      excluded: ["Consultoria de design", "Consultoria de marketing"],
    },
    cta: "Faça um teste grátis",
    highlight: true,
  },
  {
    name: "Max",
    monthlyPrice: 80,
    annualPrice: 66,
    description:
      "Funcionalidades avançadas e uso ampliado para negócios em expansão.",
    features: {
      included: [
        "Gestão de colaboradores",
        "300 imóveis por colaborador ativo",
        "Até 5 websites personalizáveis",
        "Use seu próprio domínio",
        "Certificado SSL",
        "Gestão de leads",
        "Gestão de agenda (Em breve)",
        "64 créditos IA (Em breve)",
        "Gestão financeira (Em breve)",
        "Gestão de filiais (Em breve)",
        "Consultoria de design",
        "Consultoria de marketing",
      ],
    },
    cta: "Faça um teste grátis",
    highlight: false,
  },
];

const MainPricingTable: React.FC = () => {
  // State for billing toggle
  const [isAnnual, setIsAnnual] = useState(true);

  // Initialize from localStorage and set up listener for checkbox
  useEffect(() => {
    const savedBilling = localStorage.getItem("billingPeriod");
    const initialIsAnnual = savedBilling === "annual";
    setIsAnnual(initialIsAnnual);

    const checkbox = document.getElementById("pricing-switch") as HTMLInputElement;
    if (checkbox) {
      checkbox.checked = initialIsAnnual;
    }
  }, []);

  // Handle billing toggle
  const handleBillingToggle = () => {
    setIsAnnual((prevIsAnnual: boolean) => {
      const newIsAnnual = !prevIsAnnual;
      localStorage.setItem("billingPeriod", newIsAnnual ? "annual" : "monthly");
      return newIsAnnual;
    });
  };

  return (
    <>
      {/* Billing Toggle */}

      <div
        className="flex justify-center items-center gap-x-3 mb-4 mt-8 lg:mb-8 lg:mt-12"
      >
        <label
          htmlFor="pricing-switch"
          className={cn(
            'text-xs md:text-sm font-medium text-gray-800 dark:text-neutral-200 text-right',
            { 'opacity-50': isAnnual },
          )}
        >
          Pagamento mensal
        </label>
        <label
          htmlFor="pricing-switch"
          className="relative inline-block w-11 h-6 cursor-pointer"
        >
          <input
            type="checkbox"
            id="pricing-switch"
            className="peer sr-only"
            checked={isAnnual}
            onChange={handleBillingToggle} // Added onChange handler
          />
          <span
            className="absolute inset-0 bg-gray-200 rounded-full transition-colors duration-200 ease-in-out peer-checked:bg-gray-800 dark:bg-neutral-700 dark:peer-checked:bg-gray-500 peer-disabled:opacity-50 peer-disabled:pointer-events-none"
          ></span>
          <span
            className="absolute top-1/2 start-0.5 -translate-y-1/2 size-5 bg-white rounded-full shadow-xs transition-transform duration-200 ease-in-out peer-checked:translate-x-full dark:bg-neutral-400 dark:peer-checked:bg-white"
          ></span>
        </label>
        <label
          htmlFor="pricing-switch"
          className={cn(
            'relative text-xs md:text-sm font-medium text-left text-gray-800 dark:text-neutral-200',
            { 'opacity-50': !isAnnual },
          )}
        >
          Pagamento anual
          <span
            className={cn('absolute -top-10 start-auto -end-4', {
              'opacity-100': isAnnual,
              'opacity-50': !isAnnual,
            })}
          >
            <span className="flex items-center">
              <svg
                className="w-9 h-5 me-1 mt-2"
                width="45"
                height="25"
                viewBox="0 0 45 25"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M43.2951 3.47877C43.8357 3.59191 44.3656 3.24541 44.4788 2.70484C44.5919 2.16427 44.2454 1.63433 43.7049 1.52119L43.2951 3.47877ZM4.63031 24.4936C4.90293 24.9739 5.51329 25.1423 5.99361 24.8697L13.8208 20.4272C14.3011 20.1546 14.4695 19.5443 14.1969 19.0639C13.9242 18.5836 13.3139 18.4152 12.8336 18.6879L5.87608 22.6367L1.92723 15.6792C1.65462 15.1989 1.04426 15.0305 0.563943 15.3031C-0.0847477 15.5757 -0.0847477 16.1861 0.187863 16.6664L4.63031 24.4936ZM43.7049 1.52119C32.7389 -0.77401 23.9595 0.99522 17.3905 5.28788C10.8356 9.57127 6.58742 16.2977 4.53601 23.7341L6.46399 24.2659C8.41258 17.2023 12.4144 10.9287 18.4845 6.96211C24.5405 3.00476 32.7611 1.27399 43.2951 3.47877L43.7049 1.52119Z"
                  fill="currentColor"
                  className="fill-primary-600/60 dark:fill-neutral-700"></path>
              </svg>
              <span
                className="mt-2 inline-block whitespace-nowrap border border-primary-100 text-[10px] leading-5 font-semibold uppercase bg-linear-to-t from-primary-50 to-primary-100 text-primary-800 rounded-full py-0 px-1.5"
              >
                Economize até 20%
              </span>
            </span>
          </span>
        </label>
      </div>

      {/* Pricing Grid */}
      <div
        className="mt-12 grid sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8 xl:gap-12 lg:items-start"
      >
        {plans.map((plan: Plan, index: number) => (
          <div
            key={index} // Added key for list items
            className={cn(
              'flex flex-col items-center border text-center rounded-4xl p-8 bg-white',
              {
                'border-primary-200 shadow-xl': plan.highlight,
                'border-primary-950/15 dark:border-neutral-800': !plan.highlight,
              },
            )}
          >
            {plan.highlight && (
              <div className="w-full flex justify-center relative">
                <p className="mb-3 absolute -top-12">
                  <span className="inline-flex items-center border border-primary-600/30 gap-1.5 py-1.5 px-3 rounded-full text-xs uppercase font-semibold bg-linear-to-t from-primary-100 to-primary-50 text-primary-900 text-shadow-xs text-shadow-white shadow-xs">
                    Melhor custo-benefício
                  </span>
                </p>
              </div>
            )}
            <h4 className="font-medium text-xl text-gray-800 dark:text-neutral-200">
              {plan.name}
            </h4>
            <div className="mt-5 relative">
              <span className="font-bold text-5xl text-gray-800 dark:text-neutral-200">
                <span className="font-bold text-2xl -ml-6">R$</span>
                {isAnnual ? plan.annualPrice : plan.monthlyPrice}
                {isAnnual && (
                  <span className="absolute text-gray-600/60 font-extralight ml-2 text-2xl">
                    {plan.monthlyPrice}
                    <span className="absolute left-0 top-4 h-[2px] w-full bg-red-500/50 rotate-[170deg]" />
                  </span>
                )}
              </span>
            </div>
            <span className="font-regular font-medium text-slate-500 text-sm -me-2">
              {plan.name === "Lite" ? "total por mês" : "por colaborador /mês"}
            </span>
            <span className="font-regular text-primary-300 text-sm -me-2">
              {isAnnual
                ? `(desconto de R$${plan.monthlyPrice - plan.annualPrice})`
                : "\u00A0"}
            </span>
            <p className="mt-2 text-xs max-w-3/4 text-slate-400 dark:text-neutral-500">
              {plan.description}
            </p>
            <a
              className={cn(
                'mt-5 py-3 px-4 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-full border w-full',
                {
                  'bg-gray-950 text-white hover:opacity-80 focus:outline-hidden focus:bg-gray-700 disabled:opacity-50 disabled:pointer-events-none':
                    plan.highlight,
                  'border-primary-950/15 bg-gray-50 text-primary-950 shadow-2xs hover:bg-gray-100 focus:bg-gray-50 dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800':
                    !plan.highlight,
                },
              )}
              href="/teste-gratis"
            >
              {plan.cta}
            </a>
            <ul className="mt-12 space-y-2.5 text-sm w-full">
              {plan.features.included?.map((feature: string, featureIndex: number) => (
                <li key={`inc-${featureIndex}`} className="flex gap-x-2">
                  <svg
                    className="shrink-0 mt-0.5 size-4 text-gray-950 dark:text-gray-500"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <polyline points="20 6 9 17 4 12" />
                  </svg>
                  <span className="text-gray-800 dark:text-neutral-400 text-start">
                    {feature}
                  </span>
                </li>
              ))}
              {plan.features.excluded?.map((feature: string, featureIndex: number) => (
                <li key={`exc-${featureIndex}`} className="flex gap-x-2">
                  <svg
                    className="shrink-0 mt-0.5 size-4 text-slate-400 dark:text-slate-800"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M18 6L6 18M6 6l12 12" />
                  </svg>
                  <span className="text-slate-400 dark:text-neutral-800">
                    {feature}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        ))
        }

        {/* Enterprise Card */}
        <div
          className="flex lg:gap-6 xl:gap-12 lg:mt-8 xl:mt-16 flex-col lg:justify-center lg:items-center lg:flex-row border border-gray-200 text-center rounded-4xl p-8 dark:border-neutral-800 bg-gray-950 w-full lg:col-span-3"
        >
          <div className="lg:text-start max-w-[24rem]">
            <h4
              className="font-medium lg:text-start text-xl text-white dark:text-neutral-200"
            >
              Enterprise
            </h4>
            <span
              className="mt-5 font-bold lg:text-start text-2xl text-white dark:text-neutral-200"
            >
              Sob consulta
            </span>
            <p
              className="mt-2 text-sm lg:text-start text-slate-300 dark:text-neutral-500"
            >
              Precisa de mais recursos? Oferecemos planos corporativos com recursos
              ilimitados para grandes imobiliárias com muitos funcionários e filiais.
            </p>
            <a
              className="hidden lg:inline-flex mt-5 py-3 px-4 justify-center items-center gap-x-2 text-sm font-medium rounded-xl border border-gray-200 bg-gray-50 text-primary-950 shadow-2xs hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
              href="#"
            >
              Fale com um especialista
            </a>
          </div>

          <div className="flex-1">
            <ul
              className="mt-7 lg:mt-0 grid grid-cols-1 lg:grid-cols-2 gap-x-4 gap-y-2.5 text-sm"
            >
              {
                [
                  "Suporte dedicado e prioritário",
                  "Gestão de colaboradores",
                  "Imóveis ilimitados",
                  "Gestão avançada de filiais",
                  "Relatórios avançados para filiais",
                  "White label (incluindo apps iOS, Android e Web personalizados)",
                ].map((feature: string, featureIndex: number) => (
                  <li key={`ent-${featureIndex}`} className="flex gap-x-2 text-start">
                    <svg
                      className="shrink-0 mt-0.5 size-4 text-primary-50 dark:text-primary-500"
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="20 6 9 17 4 12" />
                    </svg>
                    <span className="text-slate-50 dark:text-neutral-400">{feature}</span>
                  </li>
                ))
              }
            </ul>
          </div>

          <a
            className="mt-5 py-3 px-4 lg:hidden inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-xl border border-gray-200 bg-gray-50 text-primary-950 shadow-2xs hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-50 dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
            href="#"
          >
            Fale com um especialista
          </a>
        </div>
      </div>
    </>
  );
};

export default MainPricingTable;
