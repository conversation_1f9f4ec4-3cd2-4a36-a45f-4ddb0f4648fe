---
// State for billing toggle
let isAnnual = false;

// Helper function to render SVG icons or text
const renderPlanValue = (value) => {
  if (typeof value === "boolean") {
    return value
      ? `<svg class="shrink-0 size-5 text-primary-500 dark:text-primary-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>`
      : `<svg class="shrink-0 size-5 text-gray-400 dark:text-neutral-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"></path></svg>`;
  }
  return `<span class="text-sm text-gray-800 dark:text-neutral-200">${value}</span>`;
};

// Main data structure for the pricing table
const pricingData = [
  {
    sectionTitle: "Imóveis",
    features: [
      {
        featureTitle: "Quantidade de imóveis",
        plans: {
          lite: "Até 80",
          pro: "160 (por usuário)",
          max: "320 (por usuário)",
          enterprise: "Ilimitado",
        },
      },
      {
        featureTitle: "Fotos por imóvel",
        plans: {
          lite: "40",
          pro: "80",
          max: "Ilimitado",
          enterprise: "Ilimitado",
        },
      },
      {
        featureTitle: "Vídeos (Youtube)",
        plans: { lite: true, pro: true, max: true, enterprise: true },
      },
      {
        featureTitle: "Vídeos (hospedado)",
        plans: { lite: false, pro: "1", max: "3", enterprise: "Ilimitado" },
      },
    ],
  },
  {
    sectionTitle: "Website personalizável",
    features: [
      {
        featureTitle: "Websites",
        plans: { lite: "1", pro: "3", max: "5", enterprise: "Ilimitado" },
      },
      {
        featureTitle: "Subdomínio",
        plans: { lite: true, pro: true, max: true, enterprise: true },
      },
      {
        featureTitle: "Domínio personalizado",
        plans: { lite: false, pro: true, max: true, enterprise: true },
      },
      {
        featureTitle: "Certificado SSL",
        plans: { lite: false, pro: true, max: true, enterprise: true },
      },
    ],
  },
  {
    sectionTitle: "ARIA (Inteligência Artificial)",
    features: [
      {
        featureTitle: "Créditos inclusos (por mês)",
        plans: { lite: "20", pro: "40", max: "100", enterprise: "Ilimitado" },
      },
      {
        featureTitle: "Criação de conteúdo",
        plans: { lite: true, pro: true, max: true, enterprise: true },
      },
      {
        featureTitle: "Pesquisa avançada de imóveis",
        plans: { lite: true, pro: true, max: true, enterprise: true },
      },
      {
        featureTitle: "Edição de mídia",
        plans: { lite: true, pro: true, max: true, enterprise: true },
      },
    ],
  },
  {
    sectionTitle: "Leads",
    features: [
      {
        featureTitle: "Gestão de leads",
        plans: { lite: true, pro: true, max: true, enterprise: true },
      },
      {
        featureTitle: "Máximo de ativos",
        plans: {
          lite: "1000",
          pro: "5000",
          max: "Ilimitado",
          enterprise: "Ilimitado",
        },
      },
      {
        featureTitle: "Captação automática",
        plans: { lite: true, pro: true, max: true, enterprise: true },
      },
      {
        featureTitle: "Distribuição automática",
        plans: { lite: false, pro: true, max: true, enterprise: true },
      },
    ],
  },
  {
    sectionTitle: "Agenda",
    features: [
      {
        featureTitle: "Gestão de agenda",
        plans: { lite: true, pro: true, max: true, enterprise: true },
      },
      {
        featureTitle: "Captação automática",
        plans: { lite: true, pro: true, max: true, enterprise: true },
      },
    ],
  },
  {
    sectionTitle: "Suporte",
    features: [
      {
        featureTitle: "Via chat",
        plans: { lite: true, pro: true, max: true, enterprise: true },
      },
      {
        featureTitle: "Via email",
        plans: { lite: true, pro: true, max: true, enterprise: true },
      },
      {
        featureTitle: "Via telefone",
        plans: { lite: false, pro: false, max: true, enterprise: true },
      },
      {
        featureTitle: "Suporte prioritário",
        plans: { lite: false, pro: false, max: false, enterprise: true },
      },
    ],
  },
];

// Plan details for the header and mobile view
const planDetails = {
  lite: {
    name: "Lite",
    description: `R$${isAnnual ? 40 : 40} /mês`,
    discountDescription: `(com desconto de R$${isAnnual ? 8 : 8} no pagamento anual)`,
    mobileName: "Lite",
  },
  pro: {
    name: "Pro",
    description: `R$${isAnnual ? 54 : 54} por usuário /mês`,
    discountDescription: `(com desconto de R$${isAnnual ? 10 : 10} no pagamento anual)`,
    mobileName: "Pro",
  },
  max: {
    name: "Max",
    description: `R$${isAnnual ? 66 : 66} por usuário /mês`,
    discountDescription: `(com desconto de R$${isAnnual ? 14 : 14} no pagamento anual)`,
    mobileName: "Max",
  },
  enterprise: {
    name: "Enterprise",
    description: `Sob consulta`,
    discountDescription: " ",
    mobileName: "Enterprise",
  },
};

const planOrder = ["lite", "pro", "max", "enterprise"];

const getButtonClass = (planKey) => {
  let classes =
    "w-full py-3 px-4 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-lg border disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden";
  if (planKey === "pro") {
    classes +=
      " border-transparent bg-primary-900 text-white hover:bg-primary-800 focus:bg-primary-800";
  } else {
    classes +=
      " border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:bg-gray-50 dark:bg-transparent dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-800 dark:focus:bg-neutral-800";
  }
  return classes;
};

// Handle billing toggle
function handleBillingToggle() {
  isAnnual = !isAnnual;
  updatePlanDetails();

  // Save preference to localStorage
  if (typeof window !== "undefined") {
    localStorage.setItem("billingPeriod", isAnnual ? "annual" : "monthly");
  }
}

// Update plan details when billing changes
function updatePlanDetails() {
  planDetails.lite.description = `R$${isAnnual ? 40 : 40} /mês`;
  planDetails.lite.discountDescription = `(com desconto de R$${isAnnual ? 8 : 8} no pagamento anual)`;

  planDetails.pro.description = `R$${isAnnual ? 54 : 54} por usuário /mês`;
  planDetails.pro.discountDescription = `(com desconto de R$${isAnnual ? 10 : 10} no pagamento anual)`;

  planDetails.max.description = `R$${isAnnual ? 66 : 66} por usuário /mês`;
  planDetails.max.discountDescription = `(com desconto de R$${isAnnual ? 14 : 14} no pagamento anual)`;
}

// Initialize from localStorage
if (typeof window !== "undefined") {
  const savedBilling = localStorage.getItem("billingPeriod");
  isAnnual = savedBilling === "annual";
  updatePlanDetails();
}
---

<div class="relative" id="pricing-comparison">
  <div class="max-w-[85rem] md:px-6 py-10 lg:px-8 md:py-14 lg:py-20 mx-auto">
    <div class="max-w-2xl mx-auto text-center mb-10 lg:mb-14">
      <h2
        class="text-2xl font-bold md:text-3xl md:leading-tight dark:text-white"
      >
        Compare nossos planos
      </h2>
    </div>

    <!-- Header -->
    <div
      class="hidden lg:block sticky top-[66px] start-0 py-2 bg-white dark:bg-neutral-900 border-b border-gray-200 dark:border-neutral-800 z-10"
    >
      <div class="grid grid-cols-6 gap-6">
        <div class="col-span-2">
          <span
            class="font-semibold text-lg text-gray-800 dark:text-neutral-200"
          >
            Funcionalidades
          </span>
        </div>
        {
          planOrder.map((planKey) => (
            <div class="col-span-1">
              <span class="font-semibold text-lg text-gray-800 dark:text-neutral-200">
                {planDetails[planKey].name}
              </span>
              <p class="mt-2 text-sm text-gray-500 dark:text-neutral-500">
                {planDetails[planKey].description}
              </p>
              {/* {isAnnual && ( */}
                <p class="mt-2 text-xs font-medium text-gray-400 dark:text-neutral-500">
                  {planDetails[planKey].discountDescription}
                </p>
              {/* )} */}
            </div>
          ))
        }
      </div>
    </div>
    <!-- End Header -->

    <!-- Sections and Features -->
    <div class="space-y-4 lg:space-y-0">
      {
        pricingData.map((section) => (
          <>
            <ul class="grid lg:grid-cols-6 lg:gap-6">
              <li class="lg:col-span-2 px-6 md:px-0 lg:py-3">
                <span class="text-lg font-semibold text-gray-800 dark:text-neutral-200">
                  {section.sectionTitle}
                </span>
              </li>
              {planOrder.map((planKey) => (
                <li class="hidden lg:block lg:col-span-1 py-1.5 lg:py-3 px-4 lg:px-0 lg:text-center" />
              ))}
            </ul>

            {section.features.map((feature) => (
              <ul class="grid lg:grid-cols-6 lg:gap-6">
                <li class="lg:col-span-2 px-6 md:px-0 bg-slate-100 md:bg-transparent pb-1.5 lg:py-3">
                  <h4 class="font-semibold lg:font-normal text-gray-800 dark:text-neutral-200">
                    {feature.featureTitle}
                  </h4>
                </li>
                {planOrder.map((planKey) => (
                  <li class="col-span-1 py-1.5 px-6 md:px-0 lg:py-3 border-b border-gray-200 dark:border-neutral-800">
                    <div class="grid grid-cols-2 md:grid-cols-6 lg:block">
                      <span class="lg:hidden md:col-span-2 text-sm text-gray-800 dark:text-neutral-200">
                        {planDetails[planKey].mobileName}
                      </span>
                      <span
                        set:html={renderPlanValue(feature.plans[planKey])}
                      />
                    </div>
                  </li>
                ))}
              </ul>
            ))}
          </>
        ))
      }
    </div>
    <!-- End Sections and Features -->

    <!-- Footer Buttons -->
    <div class="hidden lg:block mt-6">
      <div class="grid grid-cols-6 gap-6">
        <div class="col-span-2"></div>
        {
          planOrder.map((planKey) => (
            <div class="col-span-1">
              <a class={getButtonClass(planKey)} href={planKey === "enterprise" ? "/contato" : "/teste-gratis"}>
                {planKey === "enterprise" ? "Fale conosco" : "Teste grátis"}
              </a>
            </div>
          ))
        }
      </div>
    </div>
    <!-- End Footer Buttons -->
  </div>
</div>

<script>
  // Initialize the component
  document.addEventListener("DOMContentLoaded", () => {
    // Restore the saved billing preference
    const savedBilling = localStorage.getItem("billingPeriod");
    const isAnnual = savedBilling === "annual";

    // Update the UI to reflect the saved state
    if (isAnnual) {
      const checkbox = document.getElementById("pricing-switch");
      if (checkbox) checkbox.checked = true;
    }

    // Update plan details when the billing toggle changes
    const toggle = document.getElementById("pricing-switch");
    if (toggle) {
      toggle.addEventListener("change", () => {
        const isAnnual = toggle.checked;

        // Update prices in the DOM
        const priceElements = document.querySelectorAll("[data-price]");
        priceElements.forEach((el) => {
          const monthlyPrice = el.getAttribute("data-monthly");
          const annualPrice = el.getAttribute("data-annual");
          el.textContent = isAnnual ? annualPrice : monthlyPrice;
        });

        // Toggle discount visibility
        const discounts = document.querySelectorAll("[data-discount]");
        discounts.forEach((el) => {
          el.style.display = isAnnual ? "block" : "none";
        });

        // Save preference
        localStorage.setItem("billingPeriod", isAnnual ? "annual" : "monthly");
      });
    }
  });
</script>
