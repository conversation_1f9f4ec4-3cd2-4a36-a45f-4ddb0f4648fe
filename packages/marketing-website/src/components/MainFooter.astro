---
import MentoringIcon from "@platform/assets/icons/mentoring.svg";
import Imoblr<PERSON><PERSON> from "@platform/assets/logos/imoblr-violet-new.svg";
---

<div class="w-full flex flex-col items-center justify-center px-6 mb-4 md:mb-8">
    <div
        class="mt-auto w-full max-w-[80rem] py-16 md:py-24 px-6 lg:px-8 mx-auto border border-primary-700 bg-gradient-to-tr from-primary-600 to-primary-700 rounded-4xl shadow-[0_4px_20px_var(--color-primary-100),inset_0_1px_4px_var(--color-primary-300)]"
    >
        <h3 class="font-semibold text-2xl md:text-3xl text-white text-center">
            Pronto para começar?
        </h3>
        <p class="text-white/90 mt-1 text-center text-sm md:text-base">
            Crie seu site com 1 clique, gerencie seus imóveis, contatos e leads.
            <br class="hidden md:block" />
            Com a Imoblr fica fácil gerenciar seus processos e vender mais!
        </p>
        <div
            class="flex flex-col md:flex-row items-center justify-center gap-1 mt-8"
        >
            <a
                class="w-fit order-2 md:order-1 py-3 justify-center px-6 inline-flex items-center gap-x-2 text-sm font-medium rounded-full border border-transparent text-white hover:bg-white/20 disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                href="#"
            >
                <MentoringIcon />
                Agende uma demonstração
            </a>
            <a
                class="w-fit order-1 md:order-2 py-3 px-6 inline-flex items-center gap-x-2 text-sm shadow-2xl font-medium rounded-full border border-transparent bg-white text-primary-500 hover:opacity-80 focus:outline-hidden disabled:opacity-50 disabled:pointer-events-none"
                href="/teste-gratis"
            >
                Faça um teste grátis
                <svg
                    class="shrink-0 size-4"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"><path d="m9 18 6-6-6-6"></path></svg
                >
            </a>
        </div>
    </div>
</div>
<footer class="mt-auto w-full max-w-[85rem] py-10 px-6 lg:px-8 mx-auto">
    <!-- Grid -->
    <div
        class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-x-2 gap-y-8 md:gap-6 mb-10"
    >
        <div class="col-span-full hidden lg:col-span-1 lg:block">
            <a
                class="flex-none font-semibold text-xl text-black focus:outline-hidden focus:opacity-80 dark:text-white"
                href="/"
                aria-label="Brand"><ImoblrLogo class="w-auto h-10" /></a
            >
            <p
                class="mt-3 text-xs sm:text-sm text-gray-600 dark:text-neutral-400"
            >
                © 2025 Imoblr
                <br />
                Todos os direitos reservados.
            </p>
        </div>
        <!-- End Col -->

        <div>
            <h4
                class="text-xs font-semibold text-gray-900 uppercase dark:text-neutral-100"
            >
                Plataforma
            </h4>

            <div class="mt-3 grid space-y-3 text-sm">
                <p>
                    <a
                        class="inline-flex gap-x-2 text-gray-600 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                        href="/planos">Planos</a
                    >
                </p>
                <p>
                    <a
                        class="inline-flex gap-x-2 text-gray-600 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                        href="#">Funcionalidades</a
                    >
                </p>
                <a
                    class="inline align-center opacity-60 pointer-events-none gap-x-2 text-gray-600 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                    href="#"
                    >Atualizações<span
                        class="inline text-xs tracking-tight text-primary-600 dark:text-primary-500"
                    >
                        - Em breve</span
                    ></a
                >
                <a
                    class="inline-flex gap-x-2 text-gray-600 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                    href="#">Termos e condições</a
                >
            </div>
        </div>
        <!-- End Col -->

        <div>
            <h4
                class="text-xs font-semibold text-gray-900 uppercase dark:text-neutral-100"
            >
                A empresa
            </h4>

            <div class="mt-3 grid space-y-3 text-sm">
                <p>
                    <a
                        class="inline-flex gap-x-2 text-gray-600 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                        href="#">Quem somos</a
                    >
                </p>
                <p>
                    <a
                        class="inline-flex gap-x-2 text-gray-600 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                        href="#">Fale conosco</a
                    >
                </p>
                <p>
                    <a
                        class="inline align-center opacity-60 pointer-events-none gap-x-2 text-gray-600 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                        href="#"
                        >Carreira<span
                            class="inline text-xs tracking-tight text-primary-600 dark:text-primary-500"
                        >
                            - Em breve</span
                        ></a
                    >
                </p>
            </div>
        </div>
        <!-- End Col -->

        <div>
            <h4
                class="text-xs font-semibold text-gray-900 uppercase dark:text-neutral-100"
            >
                Conteúdo
            </h4>

            <div class="mt-3 grid space-y-3 text-sm">
                <p>
                    <a
                        class="inline align-center opacity-60 pointer-events-none gap-x-2 text-gray-600 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                        href="#"
                        >Blog<span
                            class="inline text-xs tracking-tight text-primary-600 dark:text-primary-500"
                        >
                            - Em breve</span
                        ></a
                    >
                </p>
                <p>
                    <a
                        class="inline align-center opacity-60 pointer-events-none gap-x-2 text-gray-600 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                        href="#"
                        >Academy<span
                            class="inline text-xs tracking-tight text-primary-600 dark:text-primary-500"
                        >
                            - Em breve</span
                        ></a
                    >
                </p>
                <p>
                    <a
                        class="inline align-center opacity-60 pointer-events-none gap-x-2 text-gray-600 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                        href="#"
                        >Sugestões<span
                            class="inline text-xs tracking-tight text-primary-600 dark:text-primary-500"
                        >
                            - Em breve</span
                        ></a
                    >
                </p>
            </div>
        </div>
        <!-- End Col -->

        <div>
            <h4
                class="text-xs font-semibold text-gray-900 uppercase dark:text-neutral-100"
            >
                Sistema
            </h4>

            <div class="mt-3 grid space-y-3 text-sm">
                <p>
                    <a
                        class="inline align-center opacity-60 pointer-events-none gap-x-2 text-gray-600 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                        href="#"
                        >Api<span
                            class="inline text-xs tracking-tight text-primary-600 dark:text-primary-500"
                        >
                            - Em breve</span
                        ></a
                    >
                </p>
                <p>
                    <a
                        class="inline-flex gap-x-2 text-gray-600 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                        href="https://status.imoblr.com.br">Status</a
                    >
                </p>
                <p>
                    <a
                        class="inline-flex gap-x-2 text-gray-600 hover:text-gray-800 focus:outline-hidden focus:text-gray-800 dark:text-neutral-400 dark:hover:text-neutral-200 dark:focus:text-neutral-200"
                        href="https://github.com/imoblr">GitHub</a
                    >
                </p>
            </div>
        </div>
        <!-- End Col -->
    </div>
    <!-- End Grid -->

    <div class="pt-5 mt-5 border-t border-gray-200 dark:border-neutral-700">
        <div class="sm:flex sm:justify-between sm:items-center">
            <div class="flex flex-wrap items-center gap-3">
                <!-- Language Dropdown -->
                <div
                    class="hs-dropdown [--placement:top-left] relative inline-flex"
                >
                    <button
                        id="hs-footer-language-dropdown"
                        type="button"
                        class="hs-dropdown-toggle py-2 px-3 inline-flex items-center gap-x-2 text-sm rounded-lg border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 focus:outline-hidden focus:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
                        aria-haspopup="menu"
                        aria-expanded="false"
                        aria-label="Dropdown"
                    >
                        <svg
                            class="shrink-0 size-3 rounded-full"
                            xmlns="http://www.w3.org/2000/svg"
                            id="flag-icon-css-br1"
                            viewBox="0 0 512 512"
                        >
                            <g fill="none" fill-rule="evenodd">
                                <!-- Green background -->
                                <rect
                                    width="512"
                                    height="512"
                                    rx="256"
                                    fill="#009b3a"></rect>
                                <!-- Yellow diamond -->
                                <polygon
                                    points="256,64 480,256 256,448 32,256"
                                    fill="#ffdf00"></polygon>
                                <!-- Blue globe -->
                                <circle cx="256" cy="256" r="96" fill="#3e4095"
                                ></circle>
                                <!-- White band -->
                                <ellipse
                                    cx="256"
                                    cy="256"
                                    rx="90"
                                    ry="18"
                                    fill="none"
                                    stroke="#fff"
                                    stroke-width="12"
                                    transform="rotate(-15 256 256)"></ellipse>
                                <!-- White stars (simplified: one big star) -->
                                <polygon
                                    points="256,200 266,236 304,236 272,256 282,292 256,272 230,292 240,256 208,236 246,236"
                                    fill="#fff"></polygon>
                            </g>
                        </svg>

                        Português (BR)
                        <svg
                            class="hs-dropdown-open:rotate-180 shrink-0 size-4 text-gray-500 dark:text-neutral-500"
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            ><path d="m18 15-6-6-6 6"></path></svg
                        >
                    </button>

                    <div
                        class="hs-dropdown-menu w-40 transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden z-10 bg-white shadow-md rounded-lg p-2 dark:bg-neutral-800 dark:border dark:border-neutral-700 dark:divide-neutral-700"
                        role="menu"
                        aria-orientation="vertical"
                        aria-labelledby="hs-footer-language-dropdown"
                    >
                        <a
                            class="flex items-center gap-x-2 pointer-events-none opacity-50 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700 dark:focus:text-neutral-300"
                            href="#"
                        >
                            <svg
                                class="shrink-0 size-3.5 rounded-full"
                                xmlns="http://www.w3.org/2000/svg"
                                id="flag-icon-css-us"
                                viewBox="0 0 512 512"
                            >
                                <g fill-rule="evenodd">
                                    <g stroke-width="1pt">
                                        <path
                                            fill="#bd3d44"
                                            d="M0 0h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0z"
                                            transform="scale(3.9385)"></path>
                                        <path
                                            fill="#fff"
                                            d="M0 10h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0zm0 20h247v10H0z"
                                            transform="scale(3.9385)"></path>
                                    </g>
                                    <path
                                        fill="#192f5d"
                                        d="M0 0h98.8v70H0z"
                                        transform="scale(3.9385)"></path>
                                    <path
                                        fill="#fff"
                                        d="M8.2 3l1 2.8H12L9.7 7.5l.9 2.7-2.4-1.7L6 10.2l.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8H45l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7L74 8.5l-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9L92 7.5l1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm-74.1 7l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7H65zm16.4 0l1 2.8H86l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm-74 7l.8 2.8h3l-2.4 1.7.9 2.7-2.4-1.7L6 24.2l.9-2.7-2.4-1.7h3zm16.4 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8H45l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9L92 21.5l1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm-74.1 7l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7H65zm16.4 0l1 2.8H86l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm-74 7l.8 2.8h3l-2.4 1.7.9 2.7-2.4-1.7L6 38.2l.9-2.7-2.4-1.7h3zm16.4 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8H45l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9L92 35.5l1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm-74.1 7l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7H65zm16.4 0l1 2.8H86l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm-74 7l.8 2.8h3l-2.4 1.7.9 2.7-2.4-1.7L6 52.2l.9-2.7-2.4-1.7h3zm16.4 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8H45l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9L92 49.5l1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm-74.1 7l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7H65zm16.4 0l1 2.8H86l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm-74 7l.8 2.8h3l-2.4 1.7.9 2.7-2.4-1.7L6 66.2l.9-2.7-2.4-1.7h3zm16.4 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8H45l-2.4 1.7 1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9zm16.4 0l1 2.8h2.8l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h3zm16.5 0l.9 2.8h2.9l-2.3 1.7.9 2.7-2.4-1.7-2.3 1.7.9-2.7-2.4-1.7h2.9zm16.5 0l.9 2.8h2.9L92 63.5l1 2.7-2.4-1.7-2.4 1.7 1-2.7-2.4-1.7h2.9z"
                                        transform="scale(3.9385)"></path>
                                </g>
                            </svg>
                            English (US)
                        </a>
                        <a
                            class="flex items-center gap-x-2 pointer-events-none opacity-50 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700 dark:focus:text-neutral-300"
                            href="#"
                        >
                            <svg
                                class="shrink-0 size-3 rounded-full"
                                xmlns="http://www.w3.org/2000/svg"
                                id="flag-icon-css-de"
                                viewBox="0 0 512 512"
                            >
                                <path fill="#ffce00" d="M0 341.3h512V512H0z"
                                ></path>
                                <path d="M0 0h512v170.7H0z"></path>
                                <path fill="#d00" d="M0 170.7h512v170.6H0z"
                                ></path>
                            </svg>
                            Deutsch
                        </a>
                        <a
                            class="flex items-center gap-x-2 pointer-events-none opacity-50 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700 dark:focus:text-neutral-300"
                            href="#"
                        >
                            <svg
                                class="shrink-0 size-3 rounded-full"
                                xmlns="http://www.w3.org/2000/svg"
                                id="flag-icon-css-dk"
                                viewBox="0 0 512 512"
                            >
                                <path fill="#c8102e" d="M0 0h512.1v512H0z"
                                ></path>
                                <path fill="#fff" d="M144 0h73.1v512H144z"
                                ></path>
                                <path fill="#fff" d="M0 219.4h512.1v73.2H0z"
                                ></path>
                            </svg>
                            Dansk
                        </a>
                        <a
                            class="flex items-center gap-x-2 pointer-events-none opacity-50 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700 dark:focus:text-neutral-300"
                            href="#"
                        >
                            <svg
                                class="shrink-0 size-3 rounded-full"
                                xmlns="http://www.w3.org/2000/svg"
                                id="flag-icon-css-it"
                                viewBox="0 0 512 512"
                            >
                                <g fill-rule="evenodd" stroke-width="1pt">
                                    <path fill="#fff" d="M0 0h512v512H0z"
                                    ></path>
                                    <path fill="#009246" d="M0 0h170.7v512H0z"
                                    ></path>
                                    <path
                                        fill="#ce2b37"
                                        d="M341.3 0H512v512H341.3z"></path>
                                </g>
                            </svg>
                            Italiano
                        </a>
                        <a
                            class="flex items-center gap-x-2 pointer-events-none opacity-50 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:text-neutral-400 dark:hover:bg-neutral-700 dark:hover:text-neutral-300 dark:focus:bg-neutral-700 dark:focus:text-neutral-300"
                            href="#"
                        >
                            <svg
                                class="shrink-0 size-3 rounded-full"
                                xmlns="http://www.w3.org/2000/svg"
                                xmlns:xlink="http://www.w3.org/1999/xlink"
                                id="flag-icon-css-cn"
                                viewBox="0 0 512 512"
                            >
                                <defs>
                                    <path
                                        id="a"
                                        fill="#ffde00"
                                        d="M1-.3L-.7.8 0-1 .6.8-1-.3z"></path>
                                </defs>
                                <path fill="#de2910" d="M0 0h512v512H0z"></path>
                                <use
                                    width="30"
                                    height="20"
                                    transform="matrix(76.8 0 0 76.8 128 128)"
                                    xlink:href="#a"></use>
                                <use
                                    width="30"
                                    height="20"
                                    transform="rotate(-121 142.6 -47) scale(25.5827)"
                                    xlink:href="#a"></use>
                                <use
                                    width="30"
                                    height="20"
                                    transform="rotate(-98.1 198 -82) scale(25.6)"
                                    xlink:href="#a"></use>
                                <use
                                    width="30"
                                    height="20"
                                    transform="rotate(-74 272.4 -114) scale(25.6137)"
                                    xlink:href="#a"></use>
                                <use
                                    width="30"
                                    height="20"
                                    transform="matrix(16 -19.968 19.968 16 256 230.4)"
                                    xlink:href="#a"></use>
                            </svg>
                            中文 (繁體)
                        </a>
                    </div>
                </div>
                <!-- End Language Dropdown -->
            </div>

            <div class="flex flex-wrap justify-between items-center gap-3">
                <div class="mt-3 sm:hidden">
                    <a
                        class="flex-none font-semibold text-xl text-black focus:outline-hidden focus:opacity-80 dark:text-white"
                        href="/"
                        aria-label="Brand"><ImoblrLogo class="w-auto h-8" /></a
                    >
                    <p
                        class="mt-1 text-xs sm:text-sm text-gray-600 dark:text-neutral-400"
                    >
                        © 2025 Imoblr - Todos os direitos reservados.
                    </p>
                </div>
            </div>
            <!-- End Col -->
        </div>
    </div>
</footer>
<!-- ========== END FOOTER ========== -->
