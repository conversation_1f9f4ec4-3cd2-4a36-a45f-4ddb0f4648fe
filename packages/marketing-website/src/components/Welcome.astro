---
import astroLogo from "../assets/astro.svg";
import background from "../assets/background.svg";
import MentoringIcon from "@platform/assets/icons/mentoring.svg";
---

<!-- Hero -->
<div class="relative overflow-hidden bg-white">
	<!-- Gradients -->
	<div
		aria-hidden="true"
		class="flex absolute -top-80 start-1/2 transform -translate-x-1/2"
	>
		<div
			class="bg-linear-to-r opacity-15 from-secondary-500/50 to-primary-500/50 blur-3xl w-[100vw] h-175 rotate-[0deg] transform -translate-x-40 dark:from-primary-900/50 dark:to-purple-900"
		>
		</div>
		<div
			class="bg-linear-to-tl opacity-15 from-primary-500/50 via-secondary-500/50 to-primary-500/50 blur-3xl w-[1440px] h-200 rounded-fulls origin-top-left -rotate-12 -translate-x-60 dark:from-indigo-900/70 dark:via-indigo-900/70 dark:to-secondary-900/70"
		>
		</div>
	</div>
	<!-- End Gradients -->

	<div
		class="relative z-10 pt-12 md:pt-32 pb-0 md:pb-8 overflow-hidden before:absolute before:-top-36 before:opacity-15 md:before:opacity-15 md:before:-top-24 before:start-1/2 before:bg-[url('../assets/hero-bg.svg')] dark:before:bg-[url('../assets/hero-bg.svg')] before:bg-no-repeat before:bg-center before:size-full before:-z-10 before:transform before:-translate-x-1/2"
	>
		<div
			class="max-w-[85rem] mx-auto px-4 sm:px-6 lg:px-8 py-10 lg:py-16 flex items-center justify-center"
		>
			<div
				class="max-w-5xl text-center mx-auto flex flex-col items-center justify-center"
			>
				<div
					class="p-[1px] backdrop-blur-lg rounded-full bg-linear-to-tr from-primary-300 to-secondary-300 opacity-80"
				>
					<div
						class="px-3 py-0.5 md:py-1 rounded-full bg-linear-to-t from-primary-50 to-white"
					>
						<p
							class="inline-block text-xs md:text-sm font-medium bg-clip-text bg-linear-to-tr from-primary-500 to-secondary-500 text-transparent dark:from-secondary-400 dark:to-primary-400"
						>
							Solução completa para imobiliárias e corretores
						</p>
					</div>
				</div>

				<!-- Title -->
				<div class="mt-5">
					<h1
						class="block leading-10 md:leading-16 font-semibold text-primary-950 text-4xl md:text-5xl lg:text-6xl dark:text-neutral-200"
					>
						<span
							class="bg-clip-text font-medium bg-linear-to-t from-primary-600/30 to-primary-950 to-50% text-transparent dark:from-secondary-400 dark:to-primary-400"
							>A melhor</span
						><br class="inline md:hidden" />
						<span
							class="bg-clip-text font-black bg-linear-to-tr from-primary-600 from-10% to-primary-500/20 to-90% text-transparent dark:from-primary-400 dark:to-primary-400"
							>plataforma completa</span
						>
						<br />
						<span
							class="bg-clip-text font-medium bg-linear-to-t from-primary-600/30 to-primary-950 to-50% text-transparent dark:from-secondary-400 dark:to-primary-400"
							>para corretores</span
						>
						<span
							class="bg-clip-text font-medium bg-linear-to-t from-primary-600/30 to-primary-950 to-50% text-transparent dark:from-secondary-400 dark:to-primary-400"
							>e imobiliárias.</span
						>
					</h1>
				</div>
				<!-- End Title -->

				<div class="mt-8 max-w-3xl">
					<p
						class="text-sm md:text-base lg:text-lg text-secondary-950/60 dark:text-neutral-400"
					>
						Crie seu site com 1 clique, gerencie seus imóveis,
						contatos, leads e muito mais!
						<br class="hidden md:block" />
						A tecnologia mais moderna, fácil de usar e com o melhor preço
						do mercado.
					</p>
				</div>

				<!-- Buttons -->
				<div
					class="mt-8 md:mt-16 gap-3 flex justify-center flex-col md:flex-row items-center"
				>
					<a
						class="w-fit py-3 md:py-4 justify-center px-6 md:px-8 inline-flex items-center gap-x-2 text-base font-medium rounded-full border border-transparent bg-gray-950 text-white hover:opacity-80 focus:outline-hidden focus:bg-gray-700 disabled:opacity-50 disabled:pointer-events-none"
						href="/teste-gratis"
					>
						Faça um teste grátis
						<svg
							class="shrink-0 size-4"
							xmlns="http://www.w3.org/2000/svg"
							width="24"
							height="24"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							stroke-width="2"
							stroke-linecap="round"
							stroke-linejoin="round"
							><path d="m9 18 6-6-6-6"></path></svg
						>
					</a>
					<a
						class="w-fit py-3 md:py-4 justify-center px-6 md:px-8 inline-flex items-center gap-x-2 text-base font-medium rounded-full border border-transparent text-gray-800 hover:bg-slate-100 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:hover:bg-neutral-800 dark:focus:bg-neutral-800"
						href="#"
					>
						<MentoringIcon />
						Agende uma demonstração
					</a>
				</div>
				<!-- End Buttons -->
			</div>
		</div>
	</div>
</div>

<!-- End Hero -->
