---
import HouseIcon from "@platform/assets/icons/bulk/house-03.svg";
import WebDesignIcon from "@platform/assets/icons/bulk/web-design-01.svg";
import UserMultipleIcon from "@platform/assets/icons/bulk/user-multiple-02.svg";
import ChatBotIcon from "@platform/assets/icons/bulk/chat-bot.svg";
import SaveMoneyIcon from "@platform/assets/icons/bulk/save-money.svg";
import CalendarIcon from "@platform/assets/icons/bulk/calendar.svg";
import ImoblrLogo from "@platform/assets/logos/imoblr-violet-new.svg";
---

<!-- ========== HEADER ========== -->
<header
    class="flex fixed backdrop-blur-md flex-wrap md:justify-start md:flex-nowrap z-50 w-full bg-white/80 border-b border-gray-200 dark:bg-neutral-800/80 dark:border-neutral-700"
>
    <nav
        class="relative max-w-[85rem] w-full mx-auto md:flex md:items-center md:justify-between md:gap-3 py-3 px-4 sm:px-6 lg:px-8"
    >
        <div class="flex justify-between items-center gap-x-1">
            <a
                class="flex-none font-semibold text-xl text-black focus:outline-hidden focus:opacity-80 dark:text-white"
                href="/"
                aria-label="Brand"><ImoblrLogo class="w-auto h-10" /></a
            >

            <!-- Collapse Button -->
            <button
                type="button"
                class="hs-collapse-toggle md:hidden relative size-9 flex justify-center items-center font-medium text-sm rounded-lg border border-gray-200 text-gray-800 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:text-white dark:border-neutral-700 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                id="hs-header-base-collapse"
                aria-expanded="false"
                aria-controls="hs-header-base"
                aria-label="Toggle navigation"
                data-hs-collapse="#hs-header-base"
            >
                <svg
                    class="hs-collapse-open:hidden size-4"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    ><line x1="3" x2="21" y1="6" y2="6"></line><line
                        x1="3"
                        x2="21"
                        y1="12"
                        y2="12"></line><line x1="3" x2="21" y1="18" y2="18"
                    ></line></svg
                >
                <svg
                    class="hs-collapse-open:block shrink-0 hidden size-4"
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    ><path d="M18 6 6 18"></path><path d="m6 6 12 12"
                    ></path></svg
                >
                <span class="sr-only">Toggle navigation</span>
            </button>
            <!-- End Collapse Button -->
        </div>

        <!-- Collapse -->
        <div
            id="hs-header-base"
            class="hs-collapse hidden overflow-hidden transition-all duration-300 basis-full grow md:block"
            aria-labelledby="hs-header-base-collapse"
        >
            <div
                class="overflow-hidden overflow-y-auto max-h-[75vh] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500"
            >
                <div
                    class="py-2 md:py-0 flex flex-col md:flex-row md:items-center gap-0.5 md:gap-1"
                >
                    <div class="grow">
                        <div
                            class="flex flex-col md:flex-row md:justify-end md:items-center gap-0.5 md:gap-1"
                        >
                            <a
                                class="p-2 flex items-center text-sm bg-gray-100 text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-700 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                                href="/"
                                aria-current="page"
                            >
                                <svg
                                    class="shrink-0 size-4 me-3 md:me-2 block md:hidden"
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="24"
                                    height="24"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    ><path
                                        d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"
                                    ></path><path
                                        d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
                                    ></path></svg
                                >
                                Home
                            </a>

                            <!-- Mega Menu -->
                            <div
                                class="hs-dropdown [--strategy:static] md:[--strategy:absolute] [--adaptive:none] [--is-collapse:true] md:[--is-collapse:false]"
                            >
                                <button
                                    id="hs-header-base-mega-menu-fullwidth"
                                    type="button"
                                    class="hs-dropdown-toggle w-full p-2 flex items-center text-sm text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                                    aria-haspopup="menu"
                                    aria-expanded="false"
                                    aria-label="Mega Menu"
                                >
                                    <svg
                                        class="shrink-0 size-4 me-3 md:me-2 block md:hidden"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        ><path
                                            d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"
                                        ></path><circle cx="12" cy="12" r="3"
                                        ></circle></svg
                                    >
                                    Funcionalidades
                                    <svg
                                        class="hs-dropdown-open:-rotate-180 md:hs-dropdown-open:rotate-0 duration-300 shrink-0 size-4 ms-auto md:ms-1"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="24"
                                        height="24"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        ><path d="m6 9 6 6 6-6"></path></svg
                                    >
                                </button>

                                <div
                                    class="hs-dropdown-menu transition-[opacity,margin] duration-[0.1ms] md:duration-[150ms] hs-dropdown-open:opacity-100 opacity-0 relative w-full min-w-60 hidden z-10 top-full start-0 before:absolute before:-top-5 before:start-0 before:w-full before:h-5"
                                    role="menu"
                                    aria-orientation="vertical"
                                    aria-labelledby="hs-header-base-mega-menu-fullwidth"
                                >
                                    <div
                                        class="md:mx-6 lg:mx-8 md:bg-white md:rounded-lg md:shadow-md dark:md:bg-neutral-800"
                                    >
                                        <!-- Grid -->
                                        <div
                                            class="py-1 md:p-2 md:grid md:grid-cols-2 lg:grid-cols-3 gap-4"
                                        >
                                            <div class="flex flex-col">
                                                <!-- Link -->
                                                <a
                                                    class="p-3 flex gap-x-4 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 rounded-lg dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                                                    href="#"
                                                >
                                                    <HouseIcon
                                                        class="size-6 text-primary-950 dark:text-white"
                                                    />
                                                    <div class="flex-1">
                                                        <p
                                                            class="font-medium text-sm text-gray-800 dark:text-neutral-200"
                                                        >
                                                            Gerencie seus
                                                            imóveis
                                                        </p>
                                                        <p
                                                            class="text-sm text-gray-500 dark:text-neutral-500"
                                                        >
                                                            Tenha todos os seus
                                                            imóveis com fotos,
                                                            vídeos e outros
                                                            documentos em um só
                                                            lugar.
                                                        </p>
                                                    </div>
                                                </a>
                                                <!-- End Link -->

                                                <!-- Link -->
                                                <a
                                                    class="p-3 flex gap-x-4 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 rounded-lg dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                                                    href="#"
                                                >
                                                    <WebDesignIcon
                                                        class="size-6 text-primary-950 dark:text-white"
                                                    />
                                                    <div class="flex-1">
                                                        <p
                                                            class="font-medium text-sm text-gray-800 dark:text-neutral-200"
                                                        >
                                                            Seu site em 1 clique
                                                        </p>
                                                        <p
                                                            class="text-sm text-gray-500 dark:text-neutral-500"
                                                        >
                                                            Crie seu site de
                                                            forma rápida e
                                                            fácil. Otimizado
                                                            para mobile e sites
                                                            de busca e
                                                            personalizado com a
                                                            sua identidade!
                                                        </p>
                                                    </div>
                                                </a>
                                                <!-- End Link -->

                                                <!-- Link -->
                                                <a
                                                    class="p-3 flex gap-x-4 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 rounded-lg dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                                                    href="#"
                                                >
                                                    <UserMultipleIcon
                                                        class="size-6 text-primary-950 dark:text-white"
                                                    />
                                                    <div class="flex-1">
                                                        <p
                                                            class="font-medium text-sm text-gray-800 dark:text-neutral-200"
                                                        >
                                                            CRM: Gerencie leads
                                                            e contatos
                                                        </p>
                                                        <p
                                                            class="text-sm text-gray-500 dark:text-neutral-500"
                                                        >
                                                            Organize seus
                                                            contatos e leads em
                                                            um só lugar. Saiba
                                                            qual o status de
                                                            cada lead para não
                                                            perder negócios!
                                                        </p>
                                                    </div>
                                                </a>
                                                <!-- End Link -->
                                            </div>
                                            <!-- End Col -->

                                            <div class="flex flex-col">
                                                <!-- Link -->
                                                <a
                                                    class="p-3 flex gap-x-4 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 rounded-lg dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                                                    href="#"
                                                >
                                                    <ChatBotIcon
                                                        class="size-6 text-primary-950 dark:text-white"
                                                    />
                                                    <div class="flex-1">
                                                        <p
                                                            class="font-medium text-sm text-gray-800 dark:text-neutral-200"
                                                        >
                                                            AI: Automatize seus
                                                            processos
                                                        </p>
                                                        <p
                                                            class="text-sm text-gray-500 dark:text-neutral-500"
                                                        >
                                                            Nossa tecnologia de
                                                            IA faz pesquisas
                                                            avançadas sobre cada
                                                            imóvel para criar
                                                            descrições de alta
                                                            conversão.
                                                        </p>
                                                    </div>
                                                </a>
                                                <!-- End Link -->

                                                <!-- Link -->
                                                <a
                                                    class="p-3 flex gap-x-4 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 rounded-lg dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                                                    href="#"
                                                >
                                                    <SaveMoneyIcon
                                                        class="size-6 text-primary-950 dark:text-white"
                                                    />
                                                    <div class="flex-1">
                                                        <p
                                                            class="font-medium text-sm text-gray-800 dark:text-neutral-200"
                                                        >
                                                            Gestão financeira
                                                        </p>
                                                        <p
                                                            class="text-sm text-gray-500 dark:text-neutral-500"
                                                        >
                                                            Você tem controle e
                                                            visão total de todas
                                                            as despesas e
                                                            receitas do seu
                                                            negócio imobiliário.
                                                        </p>
                                                    </div>
                                                </a>
                                                <!-- End Link -->

                                                <!-- Link -->
                                                <a
                                                    class="p-3 flex gap-x-4 hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 rounded-lg dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                                                    href="#"
                                                >
                                                    <CalendarIcon
                                                        class="size-6 text-primary-950 dark:text-white"
                                                    />
                                                    <div class="flex-1">
                                                        <p
                                                            class="font-medium text-sm text-gray-800 dark:text-neutral-200"
                                                        >
                                                            Gestão de agenda
                                                        </p>
                                                        <p
                                                            class="text-sm text-gray-500 dark:text-neutral-500"
                                                        >
                                                            Gerencie visitas,
                                                            vistorias, etc. Tudo
                                                            em apenas um lugar e
                                                            conectado com seus
                                                            leads e imóveis!
                                                        </p>
                                                    </div>
                                                </a>
                                                <!-- End Link -->
                                            </div>
                                            <!-- End Col -->

                                            <div
                                                class="mt-2 md:mt-0 flex flex-col"
                                            >
                                                <span
                                                    class="ms-2.5 mb-2 font-semibold text-xs uppercase text-gray-800 dark:text-neutral-200"
                                                    >Novas funcionalidades</span
                                                >

                                                <!-- Link -->
                                                <a
                                                    class="p-3 flex gap-x-5 items-center rounded-xl hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                                                    href="#"
                                                >
                                                    <img
                                                        class="size-32 rounded-lg"
                                                        src="https://images.unsplash.com/photo-1648737967328-690548aec14f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=320&h=320&q=80"
                                                        alt="Avatar"
                                                    />
                                                    <div class="grow">
                                                        <p
                                                            class="text-sm text-gray-800 dark:text-neutral-400"
                                                        >
                                                            Gerencie suas
                                                            locações e
                                                            pagamentos de forma
                                                            simples e eficiente.
                                                            Totalmente integrado
                                                            com a gestão
                                                            financeira.
                                                        </p>
                                                        <p
                                                            class="mt-3 inline-flex items-center gap-x-1 text-sm text-primary-600 decoration-2 group-hover:underline group-focus:underline font-medium dark:text-primary-400"
                                                        >
                                                            Em breve
                                                            <svg
                                                                class="shrink-0 size-4"
                                                                xmlns="http://www.w3.org/2000/svg"
                                                                width="24"
                                                                height="24"
                                                                viewBox="0 0 24 24"
                                                                fill="none"
                                                                stroke="currentColor"
                                                                stroke-width="2"
                                                                stroke-linecap="round"
                                                                stroke-linejoin="round"
                                                                ><path
                                                                    d="m9 18 6-6-6-6"
                                                                ></path></svg
                                                            >
                                                        </p>
                                                    </div>
                                                </a>
                                                <!-- End Link -->
                                            </div>
                                            <!-- End Col -->
                                        </div>
                                        <!-- End Grid -->
                                    </div>
                                </div>
                            </div>
                            <!-- End Mega Menu -->

                            <a
                                class="p-2 flex items-center text-sm text-gray-800 hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                                href="/planos"
                            >
                                Planos
                            </a>
                        </div>
                    </div>

                    <div class="my-2 md:my-0 md:mx-4">
                        <div
                            class="w-full h-px md:w-px md:h-4 bg-gray-100 md:bg-gray-300 dark:bg-neutral-700"
                        >
                        </div>
                    </div>

                    <!-- Button Group -->
                    <div class="flex flex-wrap items-center gap-x-1.5">
                        <a
                            class="py-2.5 px-5 inline-flex items-center font-medium text-sm rounded-full border border-gray-200 bg-white text-gray-800 shadow-2xs hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 focus:outline-hidden focus:bg-gray-100 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700"
                            href="https://app.imoblr.com.br/entrar"
                        >
                            Entrar
                        </a>
                        <a
                            class="py-2 px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-full border border-transparent bg-gray-950 text-white hover:opacity-80 focus:outline-hidden focus:bg-gray-700 disabled:opacity-50 disabled:pointer-events-none"
                            href="/teste-gratis"
                        >
                            Faça um teste grátis
                            <svg
                                class="shrink-0 size-4"
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                ><path d="m9 18 6-6-6-6"></path></svg
                            >
                        </a>
                    </div>
                    <!-- End Button Group -->
                </div>
            </div>
        </div>
        <!-- End Collapse -->
    </nav>
</header>
<!-- ========== END HEADER ========== -->
