@import "tailwindcss";
@import "preline/variants.css";

@plugin 'flowbite/plugin';

@source "../../node_modules/flowbite-svelte/dist";
@source "../../node_modules/flowbite-svelte-icons/dist";
@source "../../node_modules/preline/dist/*.js";

/* Defaults hover styles on all devices */
@custom-variant hover (&:hover);
@custom-variant dark (&:where([data-theme=dark], [data-theme=dark] *, .dark, .dark *));

@layer base {
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}

@theme inline {
    --font-sans: var(--font-inter);
    --font-title: var(--font-title);
}

@theme {
  --color-primary-50: oklch(96.57% 0.022 309.56);
  --color-primary-100: oklch(89.72% 0.065 309.58);
  --color-primary-200: oklch(76.3% 0.156 307.65);
  --color-primary-300: oklch(65.42% 0.23 305.52);
  --color-primary-400: oklch(56.44% 0.281 300.72);
  --color-primary-500: oklch(48.97% 0.263 297.92);
  --color-primary-600: oklch(42.18% 0.227 297.91);
  --color-primary-700: oklch(35.28% 0.188 298.4);
  --color-primary-800: oklch(27.13% 0.143 300.04);
  --color-primary-900: oklch(19.22% 0.098 302.92);
  --color-primary-950: oklch(14.8% 0.076 306.2);

  /* --color-primary-50: oklch(97.94% 0.008 301.36);
  --color-primary-100: oklch(91.52% 0.038 305.66);
  --color-primary-200: oklch(78.79% 0.1 304.39);
  --color-primary-300: oklch(66.14% 0.16 302.32);
  --color-primary-400: oklch(54.71% 0.214 298.9);
  --color-primary-500: oklch(45.15% 0.211 295.86);
  --color-primary-600: oklch(40.18% 0.185 296.55);
  --color-primary-700: oklch(34.85% 0.156 297.2);
  --color-primary-800: oklch(30.17% 0.132 297.47);
  --color-primary-900: oklch(24.45% 0.1 298.77);
  --color-primary-950: oklch(20.86% 0.081 298.94); */

  /* --color-primary-50: oklch(98.19% 0.015 145.46);
  --color-primary-100: oklch(95.14% 0.041 146.34);
  --color-primary-200: oklch(88.8% 0.099 145.1);
  --color-primary-300: oklch(83.48% 0.149 144.57);
  --color-primary-400: oklch(78.25% 0.197 143.67);
  --color-primary-500: oklch(73.68% 0.225 143.07);
  --color-primary-600: oklch(62.92% 0.191 143.06);
  --color-primary-700: oklch(51.11% 0.152 143.19);
  --color-primary-800: oklch(38.57% 0.111 143.24);
  --color-primary-900: oklch(24.94% 0.064 143.86);
  --color-primary-950: oklch(17.19% 0.039 143.67); */
  
  --color-secondary-50: oklch(98.47% 0.008 236.56);
  --color-secondary-100: oklch(94.82% 0.03 232.41);
  --color-secondary-200: oklch(87.66% 0.072 234.18);
  --color-secondary-300: oklch(81.21% 0.111 235.27);
  --color-secondary-400: oklch(75.23% 0.144 238.59);
  --color-secondary-500: oklch(70.37% 0.168 243.48);
  --color-secondary-600: oklch(61.56% 0.155 245.16);
  --color-secondary-700: oklch(50.88% 0.126 244.25);
  --color-secondary-800: oklch(39.35% 0.096 243.47);
  --color-secondary-900: oklch(27.18% 0.062 240.08);
  --color-secondary-950: oklch(20.62% 0.045 236.53);

  /* --color-secondary-50: oklch(99.00% 0.014 162.49);
  --color-secondary-100: oklch(95.29% 0.043 162.45);
  --color-secondary-200: oklch(90.41% 0.087 161.27);
  --color-secondary-300: oklch(85.59% 0.131 159.45);
  --color-secondary-400: oklch(81.85% 0.166 156.86);
  --color-secondary-500: oklch(77.91% 0.193 153.16);
  --color-secondary-600: oklch(65.58% 0.16 153.71);
  --color-secondary-700: oklch(54.11% 0.129 154.24);
  --color-secondary-800: oklch(40.72% 0.094 154.9);
  --color-secondary-900: oklch(26.12% 0.055 156.57);
  --color-secondary-950: oklch(17.96% 0.034 159.51); */

  --color-tertiary-50: oklch(97.28% 0.005 95.1);
  --color-tertiary-100: oklch(93.74% 0.01 87.47);
  --color-tertiary-200: oklch(87.45% 0.022 85.95);
  --color-tertiary-300: oklch(81.01% 0.032 86.47);
  --color-tertiary-400: oklch(74.31% 0.044 83.82);
  --color-tertiary-500: oklch(67.98% 0.054 84.39);
  --color-tertiary-600: oklch(58.38% 0.054 83.64);
  --color-tertiary-700: oklch(47.65% 0.044 85.07);
  --color-tertiary-800: oklch(36.3% 0.031 86.76);
  --color-tertiary-900: oklch(23.78% 0.018 82.11);
  --color-tertiary-950: oklch(17.82% 0.01 88.81);
}

@layer base {
  /* disable chrome cancel button */
  input[type="search"]::-webkit-search-cancel-button {
    display: none;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
    @apply font-title;
    letter-spacing: -0.03em;
}

html,
body {
  @apply text-slate-900;
  margin: 0;
  width: 100%;
  height: 100%;
}