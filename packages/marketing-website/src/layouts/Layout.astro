---
import "../styles/global.css";
import { Font } from "astro:assets";

interface Props {
	pageTitle?: string;
	pageDescription?: string;
}

const { pageTitle, pageDescription } = Astro.props;
---

<!doctype html>
<html lang="en" data-theme="light">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>
			{
				pageTitle ||
					"Imoblr: A melhor Plataforma Completa para corretores e imobiliárias"
			}
			{pageTitle && " | Imoblr"}
		</title>
		<meta
			name="description"
			content={pageDescription ||
				"Um único sistema que permite a corretores e imobiliárias gerenciar seus imóveis, clientes e marketing de forma completa e otimizada."}
		/>
		<Font cssVariable="--font-inter" preload />
		<Font cssVariable="--font-title" preload />
	</head>
	<body>
		<slot />
	</body><script>
		document.addEventListener("astro:page-load", async () => {
			const preline = await import("preline/dist/preline.js");
			preline.HSStaticMethods.autoInit();
		});
	</script>
	<script>
		import "preline/dist/preline.js";
	</script>
</html>
