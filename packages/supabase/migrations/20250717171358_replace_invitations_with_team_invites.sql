-- Migration: Replace invitations table with team_invites table
-- Date: 2025-07-17
-- Description: Drop existing basejump.invitations table and create new basejump.team_invites table
--              with updated schema to support email-based invitations

-- First, drop all triggers and functions that depend on the invitations table
DROP TRIGGER IF EXISTS basejump_trigger_set_invitation_details ON basejump.invitations;
DROP TRIGGER IF EXISTS basejump_set_invitations_timestamp ON basejump.invitations;
DROP FUNCTION IF EXISTS basejump.trigger_set_invitation_details();

-- Drop the existing invitations table
DROP TABLE IF EXISTS basejump.invitations CASCADE;

-- Create the new team_invites table
CREATE TABLE IF NOT EXISTS basejump.team_invites (
    -- the id of the invitation
    id                 uuid unique                                                   not null default extensions.uuid_generate_v4(),
    -- what role should invitation accepters be given in this team
    team_role          basejump.account_role                                        not null,
    -- the team account the invitation is for
    team_id            uuid references basejump.team_accounts (id) on delete cascade not null,
    -- unique token used to accept the invitation
    token              text unique                                                   not null default basejump.generate_token(30),
    -- who created the invitation
    invited_by_user_id uuid references auth.users                                    not null,
    -- team account name. filled in by a trigger
    team_name          text,
    -- email address to which the invitation will be sent
    email              text                                                          not null,
    -- when the invitation was last updated
    updated_at         timestamp with time zone,
    -- when the invitation was created
    created_at         timestamp with time zone,
    primary key (id)
);

-- Open up access to team_invites
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE basejump.team_invites TO authenticated, service_role;

-- manage timestamps
CREATE TRIGGER basejump_set_team_invites_timestamp
    BEFORE INSERT OR UPDATE
    ON basejump.team_invites
    FOR EACH ROW
EXECUTE FUNCTION basejump.trigger_set_timestamps();

/**
  * This function fills in team account info and inviting user email
  * so that the recipient can get more info about the invitation prior to
  * accepting. It allows us to avoid complex permissions on team accounts
 */
CREATE OR REPLACE FUNCTION basejump.trigger_set_team_invite_details()
    RETURNS TRIGGER AS
$$
BEGIN
    NEW.invited_by_user_id = auth.uid();
    NEW.team_name = (select name from basejump.team_accounts where id = NEW.team_id);
    RETURN NEW;
END
$$ LANGUAGE plpgsql;

CREATE TRIGGER basejump_trigger_set_team_invite_details
    BEFORE INSERT
    ON basejump.team_invites
    FOR EACH ROW
EXECUTE FUNCTION basejump.trigger_set_team_invite_details();

-- enable RLS on team_invites
alter table basejump.team_invites
    enable row level security;

/**
  * -------------------------
  * Section - RLS Policies
  * -------------------------
  * This is where we define access to tables in the basejump schema
 */

create policy "Team invites viewable by team owners" on basejump.team_invites
    for select
    to authenticated
    using (
            created_at > (now() - interval '24 hours')
        and
            basejump.has_role_on_team(team_id, 'owner') = true
    );

create policy "Team invites can be created by team owners" on basejump.team_invites
    for insert
    to authenticated
    with check (
    -- team accounts should be enabled
            basejump.is_set('enable_team_accounts') = true
        -- the inserting user should be an owner of the team
        and basejump.has_role_on_team(team_id, 'owner') = true
    );

create policy "Team invites can be deleted by team owners" on basejump.team_invites
    for delete
    to authenticated
    using (
            basejump.has_role_on_team(team_id, 'owner') = true
    );

-- Add unique constraint to prevent duplicate invitations to the same email for the same team
ALTER TABLE basejump.team_invites 
ADD CONSTRAINT unique_team_email_invite 
UNIQUE (team_id, email);

-- Add index for better performance on token lookups
CREATE INDEX idx_team_invites_token ON basejump.team_invites(token);
CREATE INDEX idx_team_invites_team_id ON basejump.team_invites(team_id);
CREATE INDEX idx_team_invites_email ON basejump.team_invites(email);
