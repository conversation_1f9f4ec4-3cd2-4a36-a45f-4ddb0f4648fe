-- Migration to add neighborhood foreign key to streets table

-- 1. Add neighborhood_id column to streets table
ALTER TABLE public.streets
ADD COLUMN neighborhood_id INTEGER REFERENCES public.neighborhoods(id) ON DELETE CASCADE;

-- 2. Create index for better performance
CREATE INDEX idx_streets_neighborhood_id ON public.streets(neighborhood_id);

-- 3. Update the existing streets with neighborhood data based on city_id
-- This attempts to match streets to neighborhoods in the same city
UPDATE public.streets s
SET neighborhood_id = (
  SELECT n.id
  FROM public.neighborhoods n
  WHERE n.city_id = s.city_id
  LIMIT 1
)
WHERE s.neighborhood_id IS NULL;

-- 4. Modify the streets_city_name_unique constraint to include neighborhood_id
ALTER TABLE public.streets DROP CONSTRAINT IF EXISTS streets_city_name_unique;
ALTER TABLE public.streets
ADD CONSTRAINT streets_city_neighborhood_name_unique 
UNIQUE (city_id, neighborhood_id, name);

-- 5. Update the addresses table constraints
-- First, modify the foreign key to streets to include the neighborhood check
ALTER TABLE public.addresses DROP CONSTRAINT IF EXISTS addresses_street_id_fkey;
ALTER TABLE public.addresses
ADD CONSTRAINT addresses_street_id_fkey 
FOREIGN KEY (street_id) REFERENCES public.streets(id) ON DELETE CASCADE;

-- 6. Create a function to ensure street's neighborhood matches address's neighborhood
CREATE OR REPLACE FUNCTION check_street_neighborhood()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if the street's neighborhood matches the address's neighborhood
  IF NEW.neighborhood_id != (
    SELECT neighborhood_id 
    FROM public.streets 
    WHERE id = NEW.street_id
  ) THEN
    RAISE EXCEPTION 'Street neighborhood must match address neighborhood';
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger for addresses
CREATE TRIGGER ensure_street_neighborhood_match
BEFORE INSERT OR UPDATE ON public.addresses
FOR EACH ROW
EXECUTE FUNCTION check_street_neighborhood();

-- 7. Update functions that manage addresses and streets

-- Update the create_property function to ensure neighborhood consistency
CREATE OR REPLACE FUNCTION public.create_or_get_street(
  p_city_id INTEGER,
  p_neighborhood_id INTEGER,
  p_street_name TEXT
) RETURNS INTEGER AS $$
DECLARE
  v_street_id INTEGER;
BEGIN
  -- Try to find an existing street with the same name in the city and neighborhood
  SELECT id INTO v_street_id
  FROM public.streets
  WHERE city_id = p_city_id
  AND neighborhood_id = p_neighborhood_id
  AND name = p_street_name;
  
  -- If not found, create a new street
  IF v_street_id IS NULL THEN
    INSERT INTO public.streets (city_id, neighborhood_id, name)
    VALUES (p_city_id, p_neighborhood_id, p_street_name)
    RETURNING id INTO v_street_id;
  END IF;
  
  RETURN v_street_id;
END;
$$ LANGUAGE plpgsql;

-- 8. Make neighborhood_id NOT NULL after populating it
ALTER TABLE public.streets
ALTER COLUMN neighborhood_id SET NOT NULL;
