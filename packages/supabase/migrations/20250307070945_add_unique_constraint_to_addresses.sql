-- Add unique constraint to addresses table to prevent duplicate addresses

-- First, create a temporary table to track duplicates to remove
DO $$
DECLARE
  duplicate_record RECORD;
BEGIN
    -- Create temporary table to store duplicate addresses to remove
    CREATE TEMP TABLE duplicate_addresses AS
    SELECT 
      MIN(id) as keep_id,
      city_id,
      street_id,
      neighborhood_id,
      street_number,
      postcode
    FROM 
      addresses
    GROUP BY 
      city_id, street_id, neighborhood_id, street_number, postcode
    HAVING 
      COUNT(*) > 1;
    
    -- Create temp table of addresses to remove
    CREATE TEMP TABLE addresses_to_remove AS
    SELECT a.id
    FROM addresses a
    JOIN duplicate_addresses d ON 
      a.city_id = d.city_id AND
      a.street_id = d.street_id AND
      a.neighborhood_id = d.neighborhood_id AND
      a.street_number = d.street_number AND
      a.postcode = d.postcode AND
      a.id != d.keep_id;
    
    -- Update any properties that refer to addresses that will be removed
    FOR duplicate_record IN SELECT * FROM addresses_to_remove LOOP
      UPDATE properties
      SET address_id = d.keep_id
      FROM duplicate_addresses d
      JOIN addresses a ON a.id = duplicate_record.id
      WHERE properties.address_id = duplicate_record.id
      AND a.city_id = d.city_id
      AND a.street_id = d.street_id
      AND a.neighborhood_id = d.neighborhood_id
      AND a.street_number = d.street_number
      AND a.postcode = d.postcode;
    END LOOP;
    
    -- Delete duplicate addresses
    DELETE FROM addresses
    WHERE id IN (SELECT id FROM addresses_to_remove);

END $$;

-- Now add the unique constraint
ALTER TABLE addresses
ADD CONSTRAINT addresses_unique_location
UNIQUE (city_id, street_id, neighborhood_id, street_number, postcode);

-- Add comment explaining the constraint
COMMENT ON CONSTRAINT addresses_unique_location ON addresses IS 
'Ensures that addresses with the same city, street, neighborhood, street number, and postcode are unique';
