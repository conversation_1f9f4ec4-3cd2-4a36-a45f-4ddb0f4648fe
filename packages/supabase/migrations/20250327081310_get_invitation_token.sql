-- Create a function to get an invitation token by ID
CREATE OR REPLACE FUNCTION public.get_invitation_token(invitation_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, basejump
AS $$
DECLARE
    invite_record basejump.invitations;
    current_user_role text;
    team_id uuid;
BEGIN
    -- Find the invitation by ID
    SELECT * INTO invite_record
    FROM basejump.invitations
    WHERE id = invitation_id;
    
    -- Check if invitation exists
    IF invite_record IS NULL THEN
        RETURN json_build_object('error', 'Invitation not found');
    END IF;
    
    -- Get the team ID from the invitation
    team_id := invite_record.account_id;
    
    -- Check if the current user is an owner of the team
    SELECT account_role INTO current_user_role
    FROM basejump.team_members
    WHERE team_id = invite_record.account_id
    AND user_id = auth.uid();
    
    -- Only allow team owners to access invitation tokens
    IF current_user_role IS NULL OR current_user_role <> 'owner' THEN
        RETURN json_build_object('error', 'Only team owners can access invitation tokens');
    END IF;
    
    -- Check if invitation is expired (for 24-hour invitations)
    IF invite_record.invitation_type = '24_hour' AND invite_record.created_at < NOW() - INTERVAL '24 hours' THEN
        RETURN json_build_object('error', 'Invitation has expired');
    END IF;
    
    -- Return the token
    RETURN json_build_object(
        'token', invite_record.token,
        'account_role', invite_record.account_role,
        'invitation_type', invite_record.invitation_type,
        'created_at', invite_record.created_at
    );
END;
$$;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION public.get_invitation_token(uuid) TO authenticated;