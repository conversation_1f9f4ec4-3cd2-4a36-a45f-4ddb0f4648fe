-- Move complement from addresses table to properties table

-- First, add address_complement column to properties table
ALTER TABLE properties
ADD COLUMN address_complement TEXT;

-- Copy data from addresses.complement to properties.address_complement
UPDATE properties
SET address_complement = a.complement
FROM addresses a
WHERE properties.address_id = a.id
AND a.complement IS NOT NULL;

-- Remove the complement column from addresses table
-- First remove it from the unique constraint
ALTER TABLE addresses
DROP CONSTRAINT addresses_unique_location;

-- Then recreate the constraint without the complement column
ALTER TABLE addresses
ADD CONSTRAINT addresses_unique_location
UNIQUE (city_id, street_id, neighborhood_id, street_number, postcode);

-- Finally drop the column
ALTER TABLE addresses
DROP COLUMN complement;

-- Add comment explaining the change
COMMENT ON COLUMN properties.address_complement IS 
'Address complement (apartment, unit, etc.) specific to the property';
