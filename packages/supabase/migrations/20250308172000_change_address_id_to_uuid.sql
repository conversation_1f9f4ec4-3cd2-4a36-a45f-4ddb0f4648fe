-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. First, remove the foreign key constraint from properties table
ALTER TABLE public.properties DROP CONSTRAINT IF EXISTS properties_address_id_fkey;

-- 2. Add a new UUID column to addresses table
ALTER TABLE public.addresses ADD COLUMN uuid_id UUID DEFAULT uuid_generate_v4();

-- 3. Make sure the new UUID column has unique values
UPDATE public.addresses SET uuid_id = uuid_generate_v4() WHERE uuid_id IS NULL;

-- 4. Add a unique constraint to the new UUID column
ALTER TABLE public.addresses ADD CONSTRAINT addresses_uuid_id_key UNIQUE (uuid_id);

-- 5. Create a temporary lookup table to store the mapping between old IDs and new UUIDs
CREATE TEMPORARY TABLE address_id_mapping AS 
SELECT id AS old_id, uuid_id AS new_id FROM public.addresses;

-- 6. Update the properties table to reference the new UUID keys
ALTER TABLE public.properties ADD COLUMN address_uuid UUID;
UPDATE public.properties p 
SET address_uuid = m.new_id 
FROM address_id_mapping m 
WHERE p.address_id = m.old_id;

-- 7. Now drop the old primary key and rename the UUID column to be the new primary key
ALTER TABLE public.addresses DROP CONSTRAINT addresses_pkey;
ALTER TABLE public.addresses DROP COLUMN id;
ALTER TABLE public.addresses RENAME COLUMN uuid_id TO id;
ALTER TABLE public.addresses ADD PRIMARY KEY (id);

-- 8. Clean up the properties table by renaming and adding the foreign key constraint
ALTER TABLE public.properties DROP COLUMN address_id;
ALTER TABLE public.properties RENAME COLUMN address_uuid TO address_id;
ALTER TABLE public.properties ALTER COLUMN address_id TYPE UUID;
ALTER TABLE public.properties ADD CONSTRAINT properties_address_id_fkey 
  FOREIGN KEY (address_id) REFERENCES public.addresses(id) ON DELETE SET NULL;

-- 9. Update functions and triggers that might rely on the old ID type
-- This is a placeholder for any custom functions that might need updating
-- If you have any functions using the address ID, they would need to be updated here

-- 10. Optional: create an index to improve lookup performance 
CREATE INDEX IF NOT EXISTS idx_properties_address_id ON public.properties(address_id);
