-- Create amenities records for existing properties
DO $$
DECLARE
    property_record RECORD;
    property_amenities_record_id UUID;
    building_amenities_record_id UUID;
BEGIN
    -- Loop through all existing properties
    FOR property_record IN SELECT id FROM public.properties WHERE property_amenities_id IS NULL OR building_amenities_id IS NULL
    LOOP
        -- Create a new property_amenities record if needed
        IF NOT EXISTS (SELECT 1 FROM public.property_amenities WHERE property_id = property_record.id) THEN
            INSERT INTO public.property_amenities (property_id)
            VALUES (property_record.id)
            RETURNING id INTO property_amenities_record_id;
        ELSE
            SELECT id INTO property_amenities_record_id FROM public.property_amenities WHERE property_id = property_record.id;
        END IF;
        
        -- Create a new building_amenities record if needed
        IF NOT EXISTS (SELECT 1 FROM public.building_amenities WHERE property_id = property_record.id) THEN
            INSERT INTO public.building_amenities (property_id)
            VALUES (property_record.id)
            RETURNING id INTO building_amenities_record_id;
        ELSE
            SELECT id INTO building_amenities_record_id FROM public.building_amenities WHERE property_id = property_record.id;
        END IF;
        
        -- Update the property record with the amenities IDs
        UPDATE public.properties
        SET 
            property_amenities_id = property_amenities_record_id,
            building_amenities_id = building_amenities_record_id
        WHERE id = property_record.id;
    END LOOP;
END;
$$;
