-- Enable PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis;

-- Create property types enum in public schema
CREATE TYPE public.property_type AS ENUM (
    -- Residential Types
    'apartment',
    'house',
    'condominium_house',
    'penthouse',
    'flat',
    'studio',
    'lot',
    'townhouse',
    'residential_building',
    'rural_property',
    -- Commercial Types
    'medical_office',
    'warehouse',
    'commercial_property',
    'commercial_lot',
    'store',
    'office',
    'commercial_building'
);

-- Create properties table in public schema
CREATE TABLE public.properties (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    team_account_id UUID NOT NULL REFERENCES basejump.team_accounts(id) ON DELETE CASCADE,
    -- Property details
    purpose TEXT NOT NULL CHECK (purpose IN ('residential', 'commercial', 'mixed')),
    type public.property_type NOT NULL, -- Using the enum type directly
    floors INT,
    floor INT,
    rooms INT,
    pets_allowed BOOLEAN DEFAULT FALSE,
    available_for_rent BOOLEAN DEFAULT TRUE,
    available_for_sale BOOLEAN DEFAULT TRUE,
    available_for_bnb BOOLEAN DEFAULT TRUE,
    sale_price NUMERIC,
    rent_price NUMERIC,
    bnb_price NUMERIC,
    furnished BOOLEAN DEFAULT FALSE,
    condominium_monthly_tax NUMERIC,
    iptu_monthly_tax NUMERIC,
    insurance_monthly_tax NUMERIC,
    other_monthly_tax NUMERIC,
    -- Address
    address_street TEXT NOT NULL,
    address_number TEXT NOT NULL,
    address_complement TEXT,
    address_neighborhood TEXT NOT NULL,
    address_city TEXT NOT NULL,
    address_state TEXT NOT NULL,
    address_country TEXT NOT NULL,
    address_postcode TEXT NOT NULL,
    -- Geolocation (for geospatial queries)
    coordinates GEOGRAPHY(POINT, 4326), -- Latitude and longitude stored as a PostGIS point
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add comment to the table
COMMENT ON TABLE public.properties IS 'Stores property listings information';

-- Create indexes for common queries
CREATE INDEX idx_properties_team_account_id ON public.properties (team_account_id);
CREATE INDEX idx_properties_address_city ON public.properties (address_city);
CREATE INDEX idx_properties_address_neighborhood ON public.properties (address_neighborhood);
CREATE INDEX idx_properties_price ON public.properties (sale_price, rent_price, bnb_price);
CREATE INDEX idx_properties_type ON public.properties (type);
CREATE INDEX idx_properties_coordinates ON public.properties USING GIST (coordinates); -- Geospatial index

-- Enable Row Level Security
ALTER TABLE public.properties ENABLE ROW LEVEL SECURITY;

-- Create the trigger for updating timestamps
CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON public.properties
    FOR EACH ROW
    EXECUTE FUNCTION basejump.trigger_set_timestamps();

-- Grant access to authenticated users and anon for reading
GRANT SELECT ON public.properties TO anon;
GRANT SELECT ON public.properties TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.properties TO authenticated;

-- RLS Policies

-- Allow public read access to all properties
CREATE POLICY "Allow public read access to properties"
    ON public.properties
    FOR SELECT
    TO public
    USING (true);

-- Allow team members to create properties for their team
CREATE POLICY "Team members can create properties"
    ON public.properties
    FOR INSERT
    TO authenticated
    WITH CHECK (
        EXISTS (
            SELECT 1
            FROM basejump.team_users
            WHERE team_users.team_id = properties.team_account_id
            AND team_users.user_id = auth.uid()
        )
    );

-- Allow team members to update their team's properties
CREATE POLICY "Team members can update their team's properties"
    ON public.properties
    FOR UPDATE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1
            FROM basejump.team_users
            WHERE team_users.team_id = properties.team_account_id
            AND team_users.user_id = auth.uid()
        )
    );

-- Allow team members to delete their team's properties
CREATE POLICY "Team members can delete their team's properties"
    ON public.properties
    FOR DELETE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1
            FROM basejump.team_users
            WHERE team_users.team_id = properties.team_account_id
            AND team_users.user_id = auth.uid()
        )
    );
