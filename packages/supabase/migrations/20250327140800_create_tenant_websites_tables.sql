-- Create website_themes table
CREATE TABLE IF NOT EXISTS public.website_themes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  primary_color TEXT NOT NULL,
  secondary_color TEXT NOT NULL,
  font_family TEXT,
  dark_mode_settings JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create websites table
CREATE TABLE IF NOT EXISTS public.websites (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  team_account_id uuid NOT NULL REFERENCES basejump.team_accounts(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  subdomain TEXT NOT NULL UNIQUE,
  description TEXT,
  slug TEXT,
  published BOOLEAN NOT NULL DEFAULT FALSE,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
  theme_id uuid REFERENCES public.website_themes(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Enable RLS for both tables
ALTER TABLE public.website_themes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.websites ENABLE ROW LEVEL SECURITY;

-- Create policies for website_themes
CREATE POLICY "Team members can manage their website themes"
ON public.website_themes
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.websites
    WHERE websites.theme_id = website_themes.id
    AND basejump.has_role_on_team(websites.team_account_id) = true
  )
);

CREATE POLICY "Public can view themes for published websites"
ON public.website_themes
FOR SELECT
TO anon, authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.websites
    WHERE websites.theme_id = website_themes.id
    AND websites.published = true
  )
);

-- Create policies for websites
CREATE POLICY "Team members can manage their websites"
ON public.websites
FOR ALL
TO authenticated
USING (
  basejump.has_role_on_team(team_account_id) = true
);

CREATE POLICY "Public can view published websites"
ON public.websites
FOR SELECT
TO anon, authenticated
USING (published = true);

-- Create indexes for better performance
CREATE INDEX idx_websites_team_account_id ON public.websites(team_account_id);
CREATE INDEX idx_websites_subdomain ON public.websites(subdomain);
CREATE INDEX idx_websites_published ON public.websites(published);