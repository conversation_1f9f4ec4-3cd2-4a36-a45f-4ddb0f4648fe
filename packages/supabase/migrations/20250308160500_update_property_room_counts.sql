-- Rename existing columns
ALTER TABLE public.properties RENAME COLUMN floors TO floors_count;
ALTER TABLE public.properties RENAME COLUMN rooms TO rooms_count;

-- Add new columns
ALTER TABLE public.properties ADD COLUMN parking_spots_count INT;
ALTER TABLE public.properties ADD COLUMN bathrooms_count INT;
ALTER TABLE public.properties ADD COLUMN suites_count INT;

-- Add comments to explain the columns
COMMENT ON COLUMN public.properties.floors_count IS 'Total number of floors in the property';
COMMENT ON COLUMN public.properties.rooms_count IS 'Total number of rooms in the property';
COMMENT ON COLUMN public.properties.parking_spots_count IS 'Number of parking spots available';
COMMENT ON COLUMN public.properties.bathrooms_count IS 'Number of bathrooms in the property';
COMMENT ON COLUMN public.properties.suites_count IS 'Number of suites in the property';
