-- Add team_account_id and website_id columns to website_themes table
ALTER TABLE public.website_themes
ADD COLUMN IF NOT EXISTS team_account_id UUID REFERENCES basejump.team_accounts(id),
ADD COLUMN IF NOT EXISTS website_id UUID REFERENCES public.websites(id);

-- Update existing themes to set team_account_id based on associated websites
UPDATE public.website_themes
SET team_account_id = websites.team_account_id
FROM public.websites
WHERE websites.theme_id = website_themes.id
AND website_themes.team_account_id IS NULL;

-- Update existing themes to set website_id based on associated websites
UPDATE public.website_themes
SET website_id = websites.id
FROM public.websites
WHERE websites.theme_id = website_themes.id
AND website_themes.website_id IS NULL;

-- Drop existing RLS policies for website_themes
DROP POLICY IF EXISTS "Team members can manage their website themes" ON public.website_themes;
DROP POLICY IF EXISTS "Public can view themes for published websites" ON public.website_themes;

-- Create new RLS policies for website_themes
-- Allow team members to manage themes for their team
CREATE POLICY "Team members can manage their website themes"
ON public.website_themes
FOR ALL
TO authenticated
USING (basejump.has_role_on_team(team_account_id));

-- Allow public access to themes for published websites
CREATE POLICY "Public can view themes for published websites"
ON public.website_themes
FOR SELECT
TO anon, authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.websites
    WHERE websites.id = website_themes.website_id
    AND websites.published = true
  )
);
