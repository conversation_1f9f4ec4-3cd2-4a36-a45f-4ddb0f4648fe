-- Migration: Remove all invitation-related RPC functions
-- Date: 2025-07-17
-- Description: Drop all PostgreSQL RPC functions related to invitations
--              These functions will be replaced with API endpoints

-- Drop all invitation-related RPC functions
DROP FUNCTION IF EXISTS public.accept_invitation(text);
DROP FUNCTION IF EXISTS public.lookup_invitation(text);
DROP FUNCTION IF EXISTS public.create_invitation(uuid, basejump.account_role, basejump.invitation_type);
DROP FUNCTION IF EXISTS public.delete_invitation(uuid);
DROP FUNCTION IF EXISTS public.get_invitation_token(uuid);
DROP FUNCTION IF EXISTS public.get_team_invitations(uuid, integer, integer);

-- Also drop the invitation_type enum since we're no longer using it
DROP TYPE IF EXISTS basejump.invitation_type;
