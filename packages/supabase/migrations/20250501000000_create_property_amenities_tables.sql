-- Create property_amenities table
CREATE TABLE public.property_amenities (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
    -- Property amenities (boolean columns)
    barbecue_grill BOOLEAN DEFAULT FALSE,
    gourmet_space BOOLEAN DEFAULT FALSE,
    garden BOOLEAN DEFAULT FALSE,
    pool BOOLEAN DEFAULT FALSE,
    backyard BOOLEAN DEFAULT FALSE,
    water_heating BOOLEAN DEFAULT FALSE,
    heating BOOLEAN DEFAULT FALSE,
    air_conditioning BOOLEAN DEFAULT FALSE,
    internet BOOLEAN DEFAULT FALSE,
    garage BOOLEAN DEFAULT FALSE,
    fireplace BOOLEAN DEFAULT FALSE,
    laundry BOOLEAN DEFAULT FALSE,
    sauna BOOLEAN DEFAULT FALSE,
    spa BOOLEAN DEFAULT FALSE,
    security_system BOOLEAN DEFAULT FALSE,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Ensure only one amenities record per property
    CONSTRAINT unique_property_amenities UNIQUE (property_id)
);

-- Add comment to the table
COMMENT ON TABLE public.property_amenities IS 'Stores property-specific amenities information';

-- Create index for property_id lookups
CREATE INDEX idx_property_amenities_property_id ON public.property_amenities (property_id);

-- Enable Row Level Security
ALTER TABLE public.property_amenities ENABLE ROW LEVEL SECURITY;

-- Create the trigger for updating timestamps
CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON public.property_amenities
    FOR EACH ROW
    EXECUTE FUNCTION basejump.trigger_set_timestamps();

-- Create building_amenities table
CREATE TABLE public.building_amenities (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
    -- Building amenities (boolean columns)
    shared_barbecue_grill BOOLEAN DEFAULT FALSE,
    shared_gourmet_space BOOLEAN DEFAULT FALSE,
    bicycle_storage BOOLEAN DEFAULT FALSE,
    intercom BOOLEAN DEFAULT FALSE,
    gym BOOLEAN DEFAULT FALSE,
    green_area BOOLEAN DEFAULT FALSE,
    playground BOOLEAN DEFAULT FALSE,
    shared_pool BOOLEAN DEFAULT FALSE,
    tennis_court BOOLEAN DEFAULT FALSE,
    sports_area BOOLEAN DEFAULT FALSE,
    party_room BOOLEAN DEFAULT FALSE,
    game_room BOOLEAN DEFAULT FALSE,
    storage BOOLEAN DEFAULT FALSE,
    shared_laundry BOOLEAN DEFAULT FALSE,
    elevator BOOLEAN DEFAULT FALSE,
    shared_garage BOOLEAN DEFAULT FALSE,
    shared_water_heating BOOLEAN DEFAULT FALSE,
    power_generator BOOLEAN DEFAULT FALSE,
    reception BOOLEAN DEFAULT FALSE,
    shared_sauna BOOLEAN DEFAULT FALSE,
    shared_spa BOOLEAN DEFAULT FALSE,
    shared_security_system BOOLEAN DEFAULT FALSE,
    gated_community BOOLEAN DEFAULT FALSE,
    private_security BOOLEAN DEFAULT FALSE,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Ensure only one amenities record per property
    CONSTRAINT unique_building_amenities UNIQUE (property_id)
);

-- Add comment to the table
COMMENT ON TABLE public.building_amenities IS 'Stores building/condominium amenities information';

-- Create index for property_id lookups
CREATE INDEX idx_building_amenities_property_id ON public.building_amenities (property_id);

-- Enable Row Level Security
ALTER TABLE public.building_amenities ENABLE ROW LEVEL SECURITY;

-- Create the trigger for updating timestamps
CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON public.building_amenities
    FOR EACH ROW
    EXECUTE FUNCTION basejump.trigger_set_timestamps();

-- Add foreign key columns to properties table
ALTER TABLE public.properties
ADD COLUMN property_amenities_id UUID REFERENCES public.property_amenities(id) ON DELETE SET NULL,
ADD COLUMN building_amenities_id UUID REFERENCES public.building_amenities(id) ON DELETE SET NULL;

-- Create indexes for the new foreign key columns
CREATE INDEX idx_properties_property_amenities_id ON public.properties (property_amenities_id);
CREATE INDEX idx_properties_building_amenities_id ON public.properties (building_amenities_id);

-- Grant access to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON public.property_amenities TO authenticated;
GRANT SELECT ON public.property_amenities TO anon;

GRANT SELECT, INSERT, UPDATE, DELETE ON public.building_amenities TO authenticated;
GRANT SELECT ON public.building_amenities TO anon;

-- RLS Policies for property_amenities

-- Allow public read access to all property amenities
CREATE POLICY "Allow public read access to property amenities"
    ON public.property_amenities
    FOR SELECT
    TO public
    USING (true);

-- Allow authenticated users to insert property amenities for their team
CREATE POLICY "Allow authenticated users to insert property amenities"
    ON public.property_amenities
    FOR INSERT
    TO authenticated
    WITH CHECK (
        property_id IN (
            SELECT id FROM public.properties
            WHERE basejump.has_role_on_team(team_account_id) = true
        )
    );

-- Allow authenticated users to update property amenities for their team
CREATE POLICY "Allow authenticated users to update property amenities"
    ON public.property_amenities
    FOR UPDATE
    TO authenticated
    USING (
        property_id IN (
            SELECT id FROM public.properties
            WHERE basejump.has_role_on_team(team_account_id) = true
        )
    );

-- Allow authenticated users to delete property amenities for their team
CREATE POLICY "Allow authenticated users to delete property amenities"
    ON public.property_amenities
    FOR DELETE
    TO authenticated
    USING (
        property_id IN (
            SELECT id FROM public.properties
            WHERE basejump.has_role_on_team(team_account_id) = true
        )
    );

-- RLS Policies for building_amenities

-- Allow public read access to all building amenities
CREATE POLICY "Allow public read access to building amenities"
    ON public.building_amenities
    FOR SELECT
    TO public
    USING (true);

-- Allow authenticated users to insert building amenities for their team
CREATE POLICY "Allow authenticated users to insert building amenities"
    ON public.building_amenities
    FOR INSERT
    TO authenticated
    WITH CHECK (
        property_id IN (
            SELECT id FROM public.properties
            WHERE basejump.has_role_on_team(team_account_id) = true
        )
    );

-- Allow authenticated users to update building amenities for their team
CREATE POLICY "Allow authenticated users to update building amenities"
    ON public.building_amenities
    FOR UPDATE
    TO authenticated
    USING (
        property_id IN (
            SELECT id FROM public.properties
            WHERE basejump.has_role_on_team(team_account_id) = true
        )
    );

-- Allow authenticated users to delete building amenities for their team
CREATE POLICY "Allow authenticated users to delete building amenities"
    ON public.building_amenities
    FOR DELETE
    TO authenticated
    USING (
        property_id IN (
            SELECT id FROM public.properties
            WHERE basejump.has_role_on_team(team_account_id) = true
        )
    );
