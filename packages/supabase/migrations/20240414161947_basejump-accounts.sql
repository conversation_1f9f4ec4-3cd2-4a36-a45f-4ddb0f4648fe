/**
      ____                 _
     |  _ \               (_)
     | |_) | __ _ ___  ___ _ _   _ _ __ ___  _ __
     |  _ < / _` / __|/ _ \ | | | | '_ ` _ \| '_ \
     | |_) | (_| \__ \  __/ | |_| | | | | | | |_) |
     |____/ \__,_|___/\___| |\__,_|_| |_| |_| .__/
                         _/ |               | |
                        |__/                |_|

     Basejump is a starter kit for building SaaS products on top of Supabase.
     Learn more at https://usebasejump.com
 */

/**
  * -------------------------------------------------------
  * Section - Accounts
  * -------------------------------------------------------
 */

/**
 * Account roles allow you to provide permission levels to users
 * when they're acting on an account.  By default, we provide
 * "owner" and "member".  The only distinction is that owners can
 * also manage billing and invite/remove account members.
 */
DO
$$
    BEGIN
        -- check it account_role already exists on basejump schema
        IF NOT EXISTS(SELECT 1
                      FROM pg_type t
                               JOIN pg_namespace n ON n.oid = t.typnamespace
                      WHERE t.typname = 'account_role'
                        AND n.nspname = 'basejump') THEN
            CREATE TYPE basejump.account_role AS ENUM ('owner', 'member');
        end if;
    end;
$$;

/**
 * User accounts represent individual users in the system.
 * This table stores additional user-specific information
 * that's not stored in auth.users.
 */
CREATE TABLE IF NOT EXISTS basejump.user_accounts
(
    id                    uuid unique                NOT NULL DEFAULT extensions.uuid_generate_v4(),
    -- reference to auth.users
    user_id              uuid unique                NOT NULL references auth.users on delete cascade,
    first_name           text,
    last_name            text,
    email                text,
    -- when the user account was created
    created_at          timestamp with time zone    NOT NULL DEFAULT timezone('utc' :: text, now()),
    -- when the user account was last updated
    updated_at          timestamp with time zone    NOT NULL DEFAULT timezone('utc' :: text, now()),
    primary key (id)
);

-- enable RLS
alter table basejump.user_accounts
    enable row level security;

-- Grant access to the authenticated users
grant all on basejump.user_accounts to authenticated;

/**
 * Users can only view their own user account
 */
create policy "Users can view their own user account."
    on basejump.user_accounts for select
    to authenticated
    using (
        user_id = auth.uid()
    );

/**
 * Users can only update their own user account
 */
create policy "Users can update their own user account."
    on basejump.user_accounts for update
    to authenticated
    using (
        user_id = auth.uid()
    );

/**
 * Users can only delete their own user account
 */
create policy "Users can delete their own user account."
    on basejump.user_accounts for delete
    to authenticated
    using (
        user_id = auth.uid()
    );

/**
 * Users can only insert their own user account
 */
create policy "Users can insert their own user account."
    on basejump.user_accounts for insert
    to authenticated
    with check (
        user_id = auth.uid()
    );

/**
 * Function to automatically create a user_account when a new auth.users record is created
 */
CREATE OR REPLACE FUNCTION basejump.handle_new_user()
    RETURNS TRIGGER
    SECURITY DEFINER
    SET search_path = public, basejump
AS
$$
BEGIN
    INSERT INTO basejump.user_accounts (user_id, email)
    VALUES (NEW.id, NEW.email);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Grant execute to the trigger function
GRANT EXECUTE ON FUNCTION basejump.handle_new_user() TO postgres;

-- Create the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT
    ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION basejump.handle_new_user();

-- Create update trigger function
CREATE OR REPLACE FUNCTION basejump.handle_updated_user()
    RETURNS TRIGGER
    SECURITY DEFINER
    SET search_path = public, basejump
AS
$$
BEGIN
    UPDATE basejump.user_accounts 
    SET 
        first_name = NEW.raw_user_meta_data->>'first_name',
        last_name = NEW.raw_user_meta_data->>'last_name',
        email = NEW.email
    WHERE user_id = NEW.id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Grant execute to the new trigger function
GRANT EXECUTE ON FUNCTION basejump.handle_updated_user() TO postgres;

-- Create the trigger
DROP TRIGGER IF EXISTS on_auth_user_updated ON auth.users;
CREATE TRIGGER on_auth_user_updated
    AFTER UPDATE
    ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION basejump.handle_updated_user();

/**
 * Team accounts represent organizations or teams within
 * the system. They have many users, and all billing is connected to
 * a team account.
 */
CREATE TABLE IF NOT EXISTS basejump.team_accounts
(
    id                    uuid unique                NOT NULL DEFAULT extensions.uuid_generate_v4(),
    -- defaults to the user who creates the team
    -- this user cannot be removed from a team without changing
    -- the primary owner first
    primary_owner_user_id uuid                      NOT NULL references auth.users on delete cascade,
    -- the name of the team
    name                 text                       NOT NULL,
    -- the slug is used in urls, and must be unique
    slug                 text unique                NOT NULL,
    -- metadata for the team account
    public_metadata      jsonb                     NOT NULL DEFAULT '{}'::jsonb,
    -- when the team was created
    created_at          timestamp with time zone    NOT NULL DEFAULT timezone('utc' :: text, now()),
    -- when the team was last updated
    updated_at          timestamp with time zone    NOT NULL DEFAULT timezone('utc' :: text, now()),
    -- who created the team account
    created_by          uuid                       NOT NULL DEFAULT auth.uid() references auth.users on delete cascade,
    -- who last updated the team account
    updated_by          uuid                       NOT NULL DEFAULT auth.uid() references auth.users on delete cascade,
    primary key (id)
);

-- enable RLS
alter table basejump.team_accounts
    enable row level security;

-- Grant access to the authenticated users
grant all on basejump.team_accounts to authenticated;

CREATE TRIGGER basejump_set_team_accounts_timestamp
    BEFORE INSERT OR UPDATE
    ON basejump.team_accounts
    FOR EACH ROW
EXECUTE PROCEDURE basejump.trigger_set_timestamps();

CREATE TRIGGER basejump_set_team_accounts_user_tracking
    BEFORE INSERT OR UPDATE
    ON basejump.team_accounts
    FOR EACH ROW
EXECUTE PROCEDURE basejump.trigger_set_user_tracking();

/**
 * Team users is a join table between users and teams
 * It allows us to track what role a user has on a team
 */
CREATE TABLE IF NOT EXISTS basejump.team_users
(
    user_id     uuid references auth.users on delete cascade     not null,
    team_id     uuid references basejump.team_accounts on delete cascade not null,
    team_role   basejump.account_role                           not null,
    created_at  timestamp with time zone,
    updated_at  timestamp with time zone,
    constraint team_users_pkey primary key (user_id, team_id)
);

GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE basejump.team_users TO authenticated, service_role;

-- enable RLS
alter table basejump.team_users
    enable row level security;

-- Grant access to the authenticated users
grant all on basejump.team_users to authenticated;

/**
 * We want to protect some fields on team accounts from being updated
 * Specifically the primary owner user id and account id.
 * primary_owner_user_id should be updated using the dedicated function
 */
CREATE OR REPLACE FUNCTION basejump.protect_team_account_fields()
    RETURNS TRIGGER AS
$$
BEGIN
    IF current_user IN ('authenticated', 'anon') THEN
        -- these are protected fields that users are not allowed to update themselves
        -- platform admins should be VERY careful about updating them as well.
        if NEW.id <> OLD.id
            OR NEW.primary_owner_user_id <> OLD.primary_owner_user_id
        THEN
            RAISE EXCEPTION 'You do not have permission to update this field';
        end if;
    end if;

    RETURN NEW;
END
$$ LANGUAGE plpgsql;

-- trigger to protect team account fields
CREATE TRIGGER basejump_protect_team_account_fields
    BEFORE UPDATE
    ON basejump.team_accounts
    FOR EACH ROW
EXECUTE FUNCTION basejump.protect_team_account_fields();

-- convert any character in the slug that's not a letter, number, or dash to a dash on insert/update for team accounts
CREATE OR REPLACE FUNCTION basejump.slugify_team_account_slug()
    RETURNS TRIGGER AS
$$
BEGIN
    if NEW.slug is not null then
        NEW.slug = lower(regexp_replace(NEW.slug, '[^a-zA-Z0-9-]+', '-', 'g'));
    end if;

    RETURN NEW;
END
$$ LANGUAGE plpgsql;

-- trigger to slugify the team account slug
CREATE TRIGGER basejump_slugify_team_account_slug
    BEFORE INSERT OR UPDATE
    ON basejump.team_accounts
    FOR EACH ROW
EXECUTE FUNCTION basejump.slugify_team_account_slug();

-- enable RLS for team accounts
alter table basejump.team_accounts
    enable row level security;

/**
  * When an team account gets created, we want to insert the current user as the first
  * owner
 */
create or replace function basejump.add_current_user_to_new_team_account()
    returns trigger
    language plpgsql
    security definer
    set search_path = public
as
$$
begin
    if new.primary_owner_user_id = auth.uid() then
        insert into basejump.team_users (team_id, user_id, team_role)
        values (NEW.id, auth.uid(), 'owner');
    end if;
    return NEW;
end;
$$;

-- trigger the function whenever a new team account is created
CREATE TRIGGER basejump_add_current_user_to_new_team_account
    AFTER INSERT
    ON basejump.team_accounts
    FOR EACH ROW
EXECUTE FUNCTION basejump.add_current_user_to_new_team_account();

/**
  * Returns true if the current user has the pass in role on the passed in team
  * If no role is sent, will return true if the user is a member of the team
  */
create or replace function basejump.has_role_on_team(team_id uuid, role basejump.account_role default null)
    returns boolean
    language sql
    security definer
    set search_path = public
as
$$
select exists(
               select 1
               from basejump.team_users wu
               where wu.user_id = auth.uid()
                 and wu.team_id = has_role_on_team.team_id
                 and (
                           wu.team_role = has_role_on_team.role
                       or has_role_on_team.role is null
                   )
           );
$$;

grant execute on function basejump.has_role_on_team(uuid, basejump.account_role) to authenticated;

/**
  * Returns team_account_ids that the current user is a member of. If you pass in a role,
  * it'll only return team_accounts that the user is a member of with that role.
  */
create or replace function basejump.get_team_accounts_with_role(passed_in_role basejump.account_role default null)
    returns setof uuid
    language sql
    security definer
    set search_path = public
as
$$
select team_id
from basejump.team_users wu
where wu.user_id = auth.uid()
  and (
            wu.team_role = passed_in_role
        or passed_in_role is null
    );
$$;

grant execute on function basejump.get_team_accounts_with_role(basejump.account_role) to authenticated;

/**
  * -------------------------
  * Section - RLS Policies
  * -------------------------
  * This is where we define access to tables in the basejump schema
 */

create policy "users can view their own team_users" on basejump.team_users
    for select
    to authenticated
    using (
    user_id = auth.uid()
    );

create policy "users can view their teammates" on basejump.team_users
    for select
    to authenticated
    using (
    basejump.has_role_on_team(team_id) = true
    );

create policy "Team users can be deleted by owners except primary team account owner" on basejump.team_users
    for delete
    to authenticated
    using (
        (basejump.has_role_on_team(team_id, 'owner') = true)
        AND
        user_id != (select primary_owner_user_id
                    from basejump.team_accounts
                    where team_id = team_users.team_id)
    );

create policy "Team accounts are viewable by members" on basejump.team_accounts
    for select
    to authenticated
    using (
    basejump.has_role_on_team(id) = true
    );

-- Primary owner should always have access to the team account
create policy "Team accounts are viewable by primary owner" on basejump.team_accounts
    for select
    to authenticated
    using (
    primary_owner_user_id = auth.uid()
    );

create policy "Team accounts can be created by any user" on basejump.team_accounts
    for insert
    to authenticated
    with check (
            basejump.is_set('enable_team_accounts') = true
    );


/**
  * -------------------------------------------------------
  * Section - Public functions
  * -------------------------------------------------------
  * Each of these functions exists in the public name space because they are accessible
  * via the API.  it is the primary way developers can interact with Basejump accounts
 */



/**
 * Returns the current user's role within a given team_id
*/
create or replace function public.current_user_team_role(team_id uuid)
    returns jsonb
    language plpgsql
as
$$
DECLARE
    response jsonb;
BEGIN

    SELECT jsonb_build_object(
        'team_role', team_user.team_role,
        'is_primary_owner', team.primary_owner_user_id = auth.uid()
    )
    INTO response
    FROM basejump.team_users team_user
    JOIN basejump.team_accounts team ON team.id = team_user.team_id
    WHERE team_user.user_id = auth.uid()
    AND team_user.team_id = current_user_team_role.team_id;

    -- if the user is not a member of the team, throw an error
    if response ->> 'team_role' IS NULL then
        raise exception 'User is not a member of this team';
    end if;

    return response;
END
$$;

grant execute on function public.current_user_team_role(uuid) to authenticated;

/**
* Returns the team_account_id for a given team account slug
*/
create or replace function public.get_team_account_id(slug text)
    returns uuid
    language sql
as
$$
select id
from basejump.team_accounts
where slug = get_team_account_id.slug;
$$;

grant execute on function public.get_team_account_id(text) to authenticated, service_role;

/**
 * Returns the current user's role within a given team_account_id
*/
create or replace function public.current_user_team_account_role(team_account_id uuid)
    returns jsonb
    language plpgsql
as
$$
DECLARE
    response jsonb;
BEGIN
    select jsonb_build_object(
                   'team_role', wu.team_role,
                   'is_primary_owner', t.primary_owner_user_id = auth.uid()
               )
    into response
    from basejump.team_users wu
             join basejump.team_accounts t on t.id = wu.team_id
    where wu.user_id = auth.uid()
      and wu.team_id = current_user_team_account_role.team_account_id;

    -- if the user is not a member of the team account, throw an error
    if response ->> 'team_role' IS NULL then
        raise exception 'Not found';
    end if;

    return response;
END
$$;

grant execute on function public.current_user_team_account_role(uuid) to authenticated;

/**
  * Let's you update a users role within a team account if you are an owner of that team account
 */

create or replace function public.update_team_user_role(team_account_id uuid, user_id uuid,
                                                           new_account_role basejump.account_role,
                                                           make_primary_owner boolean default false)
    returns void
    security definer
    set search_path = public
    language plpgsql
as
$$
declare
    is_team_account_owner         boolean;
    is_team_account_primary_owner boolean;
    is_target_primary_owner   boolean;
begin
    -- check if the user is an owner, and if they are, allow them to update the role
    select basejump.has_role_on_team(update_team_user_role.team_account_id, 'owner') into is_team_account_owner;

    if not is_team_account_owner then
        raise exception 'You must be an owner of the team account to update a users role';
    end if;

    -- check if we are updating the primary owner
    select primary_owner_user_id = auth.uid(), primary_owner_user_id = update_team_user_role.user_id
    into is_team_account_primary_owner, is_target_primary_owner
    from basejump.team_accounts
    where id = update_team_user_role.team_account_id;

    -- update the role
    update basejump.team_users tu
    set team_role = update_team_user_role.new_account_role
    where tu.team_id = update_team_user_role.team_account_id
      and tu.user_id = update_team_user_role.user_id;

    if make_primary_owner = true then
        -- first we see if the current user is the owner, only they can do this
        if is_team_account_primary_owner = false then
            raise exception 'You must be the primary owner of the team account to change the primary owner';
        end if;

        update basejump.team_accounts
        set primary_owner_user_id = update_team_user_role.user_id
        where id = update_team_user_role.team_account_id;
    end if;
end;
$$;

grant execute on function public.update_team_user_role(uuid, uuid, basejump.account_role, boolean) to authenticated;

/**
  Returns the current user's team accounts
 */
create or replace function public.get_team_accounts()
    returns json
    language sql
as
$$
select coalesce(json_agg(
                        json_build_object(
                                'id', wu.team_id,
                                'role', wu.team_role,
                                'is_primary_owner', a.primary_owner_user_id = auth.uid(),
                                'name', a.name,
                                'slug', a.slug,
                                'created_at', a.created_at,
                                'updated_at', a.updated_at
                            )
                    ), '[]'::json)
from basejump.team_users wu
         join basejump.team_accounts a on a.id = wu.team_id
where wu.user_id = auth.uid();
$$;

grant execute on function public.get_team_accounts() to authenticated;

/**
  Returns a specific team account that the current user has access to
 */
create or replace function public.get_team_account(team_account_id uuid)
    returns json
    language plpgsql
as
$$
BEGIN
    -- check if the user is a member of the team account or a service_role user
    if current_user IN ('anon', 'authenticated') and
       (select current_user_team_account_role(get_team_account.team_account_id) ->> 'team_role' IS NULL) then
        raise exception 'You must be a member of a team account to access it';
    end if;


    return (select json_build_object(
                           'id', a.id,
                           'role', wu.team_role,
                           'is_primary_owner', a.primary_owner_user_id = auth.uid(),
                           'name', a.name,
                           'slug', a.slug,
                           'created_at', a.created_at,
                           'updated_at', a.updated_at,
                           'metadata', a.public_metadata,
                           'billing_status', bs.status
                       )
            from basejump.team_accounts a
                     left join basejump.team_users wu on a.id = wu.team_id and wu.user_id = auth.uid()
                     join basejump.config config on true
                     left join (select bs.team_account_id, status
                                from basejump.billing_subscriptions bs
                                where bs.team_account_id = get_team_account.team_account_id
                                order by created desc
                                limit 1) bs on bs.team_account_id = a.id
            where a.id = get_team_account.team_account_id);
END;
$$;

grant execute on function public.get_team_account(uuid) to authenticated, service_role;

/**
  Returns a specific team account that the current user has access to
 */
create or replace function public.get_team_account_by_slug(slug text)
    returns json
    language plpgsql
as
$$
DECLARE
    internal_team_account_id uuid;
BEGIN
    select a.id
    into internal_team_account_id
    from basejump.team_accounts a
    where a.slug IS NOT NULL
      and a.slug = get_team_account_by_slug.slug;

    return public.get_team_account(internal_team_account_id);
END;
$$;

grant execute on function public.get_team_account_by_slug(text) to authenticated;

/**
  Returns the personal account for the current user
 */
create or replace function public.get_personal_account()
    returns json
    language plpgsql
as
$$
BEGIN
    return row_to_json(ua)
    FROM basejump.user_accounts ua
    WHERE ua.user_id = auth.uid()
    LIMIT 1;
END;
$$;

grant execute on function public.get_personal_account() to authenticated;

/**
  * Create a team account
 */
create or replace function public.create_team_account(slug text default null, name text default null)
    returns json
    language plpgsql
as
$$
DECLARE
    new_team_account_id uuid;
BEGIN
    insert into basejump.team_accounts (slug, name, primary_owner_user_id)
    values (create_team_account.slug, create_team_account.name, auth.uid())
    returning id into new_team_account_id;

    return public.get_team_account(new_team_account_id);
EXCEPTION
    WHEN unique_violation THEN
        raise exception 'A team account with that unique ID already exists';
END;
$$;

grant execute on function public.create_team_account(slug text, name text) to authenticated;

/**
  Update a team account with passed in info. None of the info is required except for team account ID.
  If you don't pass in a value for a field, it will not be updated.
  If you set replace_meta to true, the metadata will be replaced with the passed in metadata.
  If you set replace_meta to false, the metadata will be merged with the passed in metadata.
 */
create or replace function public.update_team_account(team_account_id uuid, slug text default null, name text default null,
                                                 public_metadata jsonb default null,
                                                 replace_metadata boolean default false)
    returns json
    language plpgsql
as
$$
BEGIN

    -- check if postgres role is service_role
    if current_user IN ('anon', 'authenticated') and
       not (select current_user_team_account_role(update_team_account.team_account_id) ->> 'team_role' = 'owner') then
        raise exception 'Only team account owners can update a team account';
    end if;

    update basejump.team_accounts team_accounts
    set slug            = coalesce(update_team_account.slug, team_accounts.slug),
        name            = coalesce(update_team_account.name, team_accounts.name),
        public_metadata = case
                              when update_team_account.public_metadata is null then team_accounts.public_metadata -- do nothing
                              when team_accounts.public_metadata IS NULL then update_team_account.public_metadata -- set metadata
                              when update_team_account.replace_metadata
                                  then update_team_account.public_metadata -- replace metadata
                              else team_accounts.public_metadata || update_team_account.public_metadata end -- merge metadata
    where team_accounts.id = update_team_account.team_account_id;

    return public.get_team_account(team_account_id);
END;
$$;

grant execute on function public.update_team_account(uuid, text, text, jsonb, boolean) to authenticated, service_role;

/**
  Returns a list of current team account members. Only team account owners can access this function.
  It's a security definer because it requries us to lookup personal_accounts for existing members so we can
  get their names.
 */
create or replace function public.get_team_account_members(team_account_id uuid, results_limit integer default 50,
                                                      results_offset integer default 0)
    returns json
    language plpgsql
    security definer
    set search_path = basejump
as
$$
BEGIN

    -- only team account owners can access this function
    if (select public.current_user_team_account_role(get_team_account_members.team_account_id) ->> 'team_role' <> 'owner') then
        raise exception 'Only team account owners can access this function';
    end if;

    return (select json_agg(
                           json_build_object(
                                   'user_id', wu.user_id,
                                   'account_role', wu.team_role,
                                   'first_name', p.first_name,
                                   'last_name', p.last_name,
                                   'email', p.email,
                                   'is_primary_owner', a.primary_owner_user_id = wu.user_id
                               )
                       )
            from basejump.team_users wu
             join basejump.team_accounts a on a.id = wu.team_id
             join basejump.user_accounts p on p.user_id = wu.user_id
             join auth.users u on u.id = wu.user_id
            where wu.team_id = get_team_account_members.team_account_id
            limit coalesce(get_team_account_members.results_limit, 50) offset coalesce(get_team_account_members.results_offset, 0));
END;
$$;

grant execute on function public.get_team_account_members(uuid, integer, integer) to authenticated;

/**
  Allows an owner of the team account to remove any member other than the primary owner
 */

create or replace function public.remove_team_account_member(team_account_id uuid, user_id uuid)
    returns void
    language plpgsql
as
$$
BEGIN
    -- only team account owners can access this function
    if basejump.has_role_on_team(remove_team_account_member.team_account_id, 'owner') <> true then
        raise exception 'Only team account owners can access this function';
    end if;

    delete
    from basejump.team_users wu
    where wu.team_id = remove_team_account_member.team_account_id
      and wu.user_id = remove_team_account_member.user_id;
END;
$$;

grant execute on function public.remove_team_account_member(uuid, uuid) to authenticated;