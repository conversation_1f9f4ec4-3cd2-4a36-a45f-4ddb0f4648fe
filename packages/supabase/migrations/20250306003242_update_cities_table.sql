-- Create an enum type for Brazilian states
CREATE TYPE public.brazilian_state AS ENUM (
  'AC', 'AL', 'AP', 'AM', 'BA', 'CE', 'DF', 'ES', 'GO', 'MA', 
  'MS', 'MT', 'MG', 'PA', 'PB', 'PE', 'PI', 'PR', 'RJ', 'RN', 
  'RS', 'RO', 'RR', 'SC', 'SE', 'SP', 'TO'
);

-- Add state column to cities table as nullable first
ALTER TABLE public.cities 
  ADD COLUMN state public.brazilian_state NULL;

-- Update existing cities with default state 'SP' (São Paulo)
-- You can adjust this if most of your cities are in a different state
UPDATE public.cities SET state = 'SP' WHERE state IS NULL;

-- Now make the state column NOT NULL
ALTER TABLE public.cities 
  ALTER COLUMN state SET NOT NULL;

-- Drop the old unique constraint/index
DROP INDEX IF EXISTS idx_cities_name_lower;
ALTER TABLE public.cities 
  DROP CONSTRAINT IF EXISTS cities_name_unique;

-- Create a functional index for case-insensitive city name and state uniqueness
CREATE UNIQUE INDEX idx_cities_name_state_unique ON public.cities (LOWER(name), state);

-- Create index for improved query performance
CREATE INDEX idx_cities_state ON public.cities (state);

COMMENT ON COLUMN public.cities.state IS 'Brazilian state code';
