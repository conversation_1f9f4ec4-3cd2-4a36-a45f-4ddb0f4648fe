-- Fix the current_user_team_account_role function by adding SECURITY DEFINER
-- This ensures the function runs with the permissions of the function owner
-- rather than the calling user, allowing proper access to the team_accounts table

CREATE OR REPLACE FUNCTION public.current_user_team_account_role(team_account_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    response jsonb;
BEGIN
    select jsonb_build_object(
                   'team_role', wu.team_role,
                   'is_primary_owner', t.primary_owner_user_id = auth.uid()
               )
    into response
    from basejump.team_users wu
             join basejump.team_accounts t on t.id = wu.team_id
    where wu.user_id = auth.uid()
      and wu.team_id = current_user_team_account_role.team_account_id;

    -- if the user is not a member of the team account, throw an error
    if response ->> 'team_role' IS NULL then
        raise exception 'Not found';
    end if;

    return response;
END
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.current_user_team_account_role(uuid) TO authenticated;
