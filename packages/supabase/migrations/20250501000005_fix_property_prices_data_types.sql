-- Fix property prices data types
-- This migration ensures price values are stored as numbers

-- First, check if there are any string values in the price columns
DO $$
DECLARE
    column_type text;
    string_values_count integer;
BEGIN
    -- Check the data type of the sale_price column
    SELECT data_type INTO column_type FROM information_schema.columns
    WHERE table_name = 'properties_prices' AND column_name = 'sale_price';

    RAISE NOTICE 'Current data type of sale_price: %', column_type;

    -- If the columns are already numeric, we need to check if there are any string values
    IF column_type = 'numeric' THEN
        -- Count rows where sale_price is not a valid number
        SELECT COUNT(*) INTO string_values_count FROM properties_prices
        WHERE sale_price IS NOT NULL AND sale_price::text !~ '^[0-9]+(\.[0-9]+)?$';

        IF string_values_count > 0 THEN
            RAISE NOTICE 'Found % rows with non-numeric values in sale_price', string_values_count;

            -- Update any non-numeric values to NULL
            UPDATE properties_prices
            SET sale_price = NULL
            WHERE sale_price IS NOT NULL AND sale_price::text !~ '^[0-9]+(\.[0-9]+)?$';

            -- Do the same for other price columns
            UPDATE properties_prices
            SET rent_price = NULL
            WHERE rent_price IS NOT NULL AND rent_price::text !~ '^[0-9]+(\.[0-9]+)?$';

            UPDATE properties_prices
            SET bnb_price = NULL
            WHERE bnb_price IS NOT NULL AND bnb_price::text !~ '^[0-9]+(\.[0-9]+)?$';

            UPDATE properties_prices
            SET condominium_monthly_tax = NULL
            WHERE condominium_monthly_tax IS NOT NULL AND condominium_monthly_tax::text !~ '^[0-9]+(\.[0-9]+)?$';

            UPDATE properties_prices
            SET iptu_monthly_tax = NULL
            WHERE iptu_monthly_tax IS NOT NULL AND iptu_monthly_tax::text !~ '^[0-9]+(\.[0-9]+)?$';

            UPDATE properties_prices
            SET insurance_monthly_tax = NULL
            WHERE insurance_monthly_tax IS NOT NULL AND insurance_monthly_tax::text !~ '^[0-9]+(\.[0-9]+)?$';

            UPDATE properties_prices
            SET other_monthly_tax = NULL
            WHERE other_monthly_tax IS NOT NULL AND other_monthly_tax::text !~ '^[0-9]+(\.[0-9]+)?$';
        ELSE
            RAISE NOTICE 'All values in price columns are valid numbers';
        END IF;
    END IF;
END
$$;

-- Set default values and ensure columns are numeric
DO $$
BEGIN
    -- Check if the columns already have default values
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'properties_prices'
        AND column_name = 'sale_price'
        AND column_default = '0'
    ) THEN
        -- Set default values for price columns
        ALTER TABLE properties_prices
        ALTER COLUMN sale_price SET DEFAULT 0,
        ALTER COLUMN rent_price SET DEFAULT 0,
        ALTER COLUMN bnb_price SET DEFAULT 0,
        ALTER COLUMN condominium_monthly_tax SET DEFAULT 0,
        ALTER COLUMN iptu_monthly_tax SET DEFAULT 0,
        ALTER COLUMN insurance_monthly_tax SET DEFAULT 0,
        ALTER COLUMN other_monthly_tax SET DEFAULT 0;

        RAISE NOTICE 'Default values set for price columns';
    ELSE
        RAISE NOTICE 'Default values already set for price columns';
    END IF;

    -- Ensure columns are numeric type
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'properties_prices'
        AND column_name = 'sale_price'
        AND data_type != 'numeric'
    ) THEN
        -- Convert columns to numeric type
        ALTER TABLE properties_prices
        ALTER COLUMN sale_price TYPE numeric USING (CASE WHEN sale_price ~ '^[0-9]+(\.[0-9]+)?$' THEN sale_price::numeric ELSE NULL END),
        ALTER COLUMN rent_price TYPE numeric USING (CASE WHEN rent_price ~ '^[0-9]+(\.[0-9]+)?$' THEN rent_price::numeric ELSE NULL END),
        ALTER COLUMN bnb_price TYPE numeric USING (CASE WHEN bnb_price ~ '^[0-9]+(\.[0-9]+)?$' THEN bnb_price::numeric ELSE NULL END),
        ALTER COLUMN condominium_monthly_tax TYPE numeric USING (CASE WHEN condominium_monthly_tax ~ '^[0-9]+(\.[0-9]+)?$' THEN condominium_monthly_tax::numeric ELSE NULL END),
        ALTER COLUMN iptu_monthly_tax TYPE numeric USING (CASE WHEN iptu_monthly_tax ~ '^[0-9]+(\.[0-9]+)?$' THEN iptu_monthly_tax::numeric ELSE NULL END),
        ALTER COLUMN insurance_monthly_tax TYPE numeric USING (CASE WHEN insurance_monthly_tax ~ '^[0-9]+(\.[0-9]+)?$' THEN insurance_monthly_tax::numeric ELSE NULL END),
        ALTER COLUMN other_monthly_tax TYPE numeric USING (CASE WHEN other_monthly_tax ~ '^[0-9]+(\.[0-9]+)?$' THEN other_monthly_tax::numeric ELSE NULL END);

        RAISE NOTICE 'Columns converted to numeric type';
    ELSE
        RAISE NOTICE 'Columns are already numeric type';
    END IF;

    -- Add check constraints to ensure only numeric values are stored
    -- First, check if constraints already exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.constraint_column_usage
        WHERE table_name = 'properties_prices'
        AND column_name = 'sale_price'
        AND constraint_name = 'properties_prices_sale_price_check'
    ) THEN
        -- Add check constraints
        ALTER TABLE properties_prices
        ADD CONSTRAINT properties_prices_sale_price_check CHECK (sale_price IS NULL OR sale_price >= 0),
        ADD CONSTRAINT properties_prices_rent_price_check CHECK (rent_price IS NULL OR rent_price >= 0),
        ADD CONSTRAINT properties_prices_bnb_price_check CHECK (bnb_price IS NULL OR bnb_price >= 0),
        ADD CONSTRAINT properties_prices_condominium_monthly_tax_check CHECK (condominium_monthly_tax IS NULL OR condominium_monthly_tax >= 0),
        ADD CONSTRAINT properties_prices_iptu_monthly_tax_check CHECK (iptu_monthly_tax IS NULL OR iptu_monthly_tax >= 0),
        ADD CONSTRAINT properties_prices_insurance_monthly_tax_check CHECK (insurance_monthly_tax IS NULL OR insurance_monthly_tax >= 0),
        ADD CONSTRAINT properties_prices_other_monthly_tax_check CHECK (other_monthly_tax IS NULL OR other_monthly_tax >= 0);

        RAISE NOTICE 'Check constraints added to ensure only non-negative numeric values are stored';
    ELSE
        RAISE NOTICE 'Check constraints already exist';
    END IF;
END
$$;
