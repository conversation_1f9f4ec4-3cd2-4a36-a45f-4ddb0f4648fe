-- Update the accept_invitation function to delete all invitations when accepted
CREATE OR REPLACE FUNCTION public.accept_invitation(lookup_invitation_token text)
    RETURNS jsonb
    LANGUAGE plpgsql
    SECURITY DEFINER SET search_path = public, basejump
AS
$$
DECLARE
    lookup_team_id       uuid;
    declare new_member_role basejump.account_role;
    lookup_team_slug     text;
BEGIN
    SELECT i.account_id, i.account_role, t.slug
    INTO lookup_team_id, new_member_role, lookup_team_slug
    FROM basejump.invitations i
             JOIN basejump.team_accounts t ON t.id = i.account_id
    WHERE i.token = lookup_invitation_token
      AND i.created_at > now() - interval '24 hours';

    IF lookup_team_id IS NULL THEN
        RAISE EXCEPTION 'Invitation not found';
    END IF;

    IF lookup_team_id IS NOT NULL THEN
        -- we've validated the token is real, so grant the user access
        INSERT INTO basejump.team_users (team_id, user_id, team_role)
        VALUES (lookup_team_id, auth.uid(), new_member_role);
        
        -- Delete the invitation regardless of its type
        DELETE FROM basejump.invitations WHERE token = lookup_invitation_token;
    END IF;
    
    RETURN json_build_object('team_id', lookup_team_id, 'account_role', new_member_role, 'slug',
                             lookup_team_slug);
EXCEPTION
    WHEN unique_violation THEN
        RAISE EXCEPTION 'You are already a member of this team';
END;
$$;

-- Grant execute permission on the updated function
GRANT EXECUTE ON FUNCTION public.accept_invitation(text) TO authenticated;
