-- Create custom_domains table for managing custom domain names for websites

-- Create enum types for verification and SSL status
CREATE TYPE public.domain_verification_status AS ENUM (
  'pending',
  'verified',
  'failed'
);

CREATE TYPE public.domain_ssl_status AS ENUM (
  'pending',
  'active',
  'expired'
);

-- Create custom_domains table
CREATE TABLE IF NOT EXISTS public.custom_domains (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  team_account_id UUID NOT NULL REFERENCES basejump.team_accounts(id) ON DELETE CASCADE,
  website_id UUID NOT NULL REFERENCES public.websites(id) ON DELETE CASCADE,
  domain_name TEXT NOT NULL UNIQUE,
  verification_status public.domain_verification_status NOT NULL DEFAULT 'pending',
  ssl_status public.domain_ssl_status NOT NULL DEFAULT 'pending',
  ssl_expiration_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  is_published BOOLEAN NOT NULL DEFAULT FALSE
);

-- Enable Row Level Security
ALTER TABLE public.custom_domains ENABLE ROW LEVEL SECURITY;

-- Create policies for custom_domains
CREATE POLICY "Team members can manage their custom domains"
ON public.custom_domains
FOR ALL
TO authenticated
USING (
  basejump.has_role_on_team(team_account_id) = true
);

CREATE POLICY "Public can view published custom domains"
ON public.custom_domains
FOR SELECT
TO anon, authenticated
USING (is_published = true);

-- Create indexes for better performance
CREATE INDEX idx_custom_domains_team_account_id ON public.custom_domains(team_account_id);
CREATE INDEX idx_custom_domains_website_id ON public.custom_domains(website_id);
CREATE INDEX idx_custom_domains_domain_name ON public.custom_domains(domain_name);
CREATE INDEX idx_custom_domains_is_published ON public.custom_domains(is_published);

-- Create trigger for updating timestamps
CREATE TRIGGER set_custom_domains_timestamp
  BEFORE UPDATE ON public.custom_domains
  FOR EACH ROW
  EXECUTE FUNCTION basejump.trigger_set_timestamps();

-- Grant permissions
GRANT SELECT ON public.custom_domains TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON public.custom_domains TO authenticated;
