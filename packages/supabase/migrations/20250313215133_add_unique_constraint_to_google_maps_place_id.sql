-- Add unique constraint to google_maps_place_id column in addresses table

-- First, check if we have any duplicate place IDs
DO $$ 
BEGIN
  IF EXISTS (
    SELECT google_maps_place_id, COUNT(*)
    FROM addresses
    WHERE google_maps_place_id IS NOT NULL
    GROUP BY google_maps_place_id
    HAVING COUNT(*) > 1
  ) THEN
    RAISE EXCEPTION 'Cannot add UNIQUE constraint - there are duplicate Google Maps Place IDs';
  END IF;
END $$;

-- Add the unique constraint
-- Note: <PERSON>g<PERSON> handles NULL values in unique constraints - multiple NULL values are allowed
ALTER TABLE addresses
ADD CONSTRAINT addresses_google_maps_place_id_unique
UNIQUE (google_maps_place_id);

-- Add comment explaining the constraint
COMMENT ON CONSTRAINT addresses_google_maps_place_id_unique ON addresses IS 
'Ensures that each Google Maps Place ID is unique across all addresses, preventing duplicate locations';
