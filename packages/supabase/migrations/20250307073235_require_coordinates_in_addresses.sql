-- Make coordinates a required field in the addresses table

-- Check if we have any addresses with null coordinates
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM addresses WHER<PERSON> coordinates IS NULL) THEN
    RAISE EXCEPTION 'Cannot add NOT NULL constraint - there are addresses with NULL coordinates';
  END IF;
END $$;

-- Add the NOT NULL constraint
ALTER TABLE addresses
ALTER COLUMN coordinates SET NOT NULL;

-- Add comment explaining the constraint
COMMENT ON COLUMN addresses.coordinates IS 
'Geographic coordinates for the address. Required field in POINT(longitude latitude) format.';

-- Update the edge function to enforce this at the application level as well
