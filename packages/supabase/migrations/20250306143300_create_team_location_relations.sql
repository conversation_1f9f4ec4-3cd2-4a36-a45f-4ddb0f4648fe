-- Migration to implement hybrid approach for team-related location data
-- Creates junction tables for teams and their associated cities, neighborhoods, and streets

-- 1. Create junction table for teams and cities
CREATE TABLE public.team_cities (
  id SERIAL PRIMARY KEY,
  team_account_id UUID NOT NULL REFERENCES basejump.team_accounts(id) ON DELETE CASCADE,
  city_id INTEGER NOT NULL REFERENCES public.cities(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_account_id, city_id)
);

-- 2. Create junction table for teams and neighborhoods
CREATE TABLE public.team_neighborhoods (
  id SERIAL PRIMARY KEY,
  team_account_id UUID NOT NULL REFERENCES basejump.team_accounts(id) ON DELETE CASCADE,
  neighborhood_id INTEGER NOT NULL REFERENCES public.neighborhoods(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_account_id, neighborhood_id)
);

-- 3. Create junction table for teams and streets
CREATE TABLE public.team_streets (
  id SERIAL PRIMARY KEY,
  team_account_id UUID NOT NULL REFERENCES basejump.team_accounts(id) ON DELETE CASCADE,
  street_id INTEGER NOT NULL REFERENCES public.streets(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(team_account_id, street_id)
);

-- Create indexes for better performance
CREATE INDEX idx_team_cities_team ON public.team_cities (team_account_id);
CREATE INDEX idx_team_cities_city ON public.team_cities (city_id);

CREATE INDEX idx_team_neighborhoods_team ON public.team_neighborhoods (team_account_id);
CREATE INDEX idx_team_neighborhoods_neighborhood ON public.team_neighborhoods (neighborhood_id);

CREATE INDEX idx_team_streets_team ON public.team_streets (team_account_id);
CREATE INDEX idx_team_streets_street ON public.team_streets (street_id);

-- Enable Row Level Security
ALTER TABLE public.team_cities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_neighborhoods ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_streets ENABLE ROW LEVEL SECURITY;

-- Grant access permissions
GRANT SELECT ON public.team_cities TO anon, authenticated;
GRANT SELECT ON public.team_neighborhoods TO anon, authenticated;
GRANT SELECT ON public.team_streets TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON public.team_cities TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.team_neighborhoods TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.team_streets TO authenticated;

-- Add comments
COMMENT ON TABLE public.team_cities IS 'Junction table tracking which cities are associated with which teams';
COMMENT ON TABLE public.team_neighborhoods IS 'Junction table tracking which neighborhoods are associated with which teams';
COMMENT ON TABLE public.team_streets IS 'Junction table tracking which streets are associated with which teams';

-- Create RLS policies

-- 1. Policies for team_cities
CREATE POLICY "Allow public read access to team_cities"
  ON public.team_cities
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Team members can manage their team's city associations"
  ON public.team_cities
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1
      FROM basejump.team_users
      WHERE team_users.team_id = team_cities.team_account_id
      AND team_users.user_id = auth.uid()
    )
  );

-- 2. Policies for team_neighborhoods
CREATE POLICY "Allow public read access to team_neighborhoods"
  ON public.team_neighborhoods
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Team members can manage their team's neighborhood associations"
  ON public.team_neighborhoods
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1
      FROM basejump.team_users
      WHERE team_users.team_id = team_neighborhoods.team_account_id
      AND team_users.user_id = auth.uid()
    )
  );

-- 3. Policies for team_streets
CREATE POLICY "Allow public read access to team_streets"
  ON public.team_streets
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Team members can manage their team's street associations"
  ON public.team_streets
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1
      FROM basejump.team_users
      WHERE team_users.team_id = team_streets.team_account_id
      AND team_users.user_id = auth.uid()
    )
  );

-- Create triggers and functions to automatically update the junction tables

-- 1. Function to update team_cities, team_neighborhoods, and team_streets when properties are created/updated
CREATE OR REPLACE FUNCTION update_team_location_associations()
RETURNS TRIGGER AS $$
BEGIN
  -- When a property is created or updated with a valid address_id
  IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') AND NEW.address_id IS NOT NULL THEN
    -- Get location IDs from the address and insert into junction tables
    INSERT INTO public.team_cities (team_account_id, city_id)
    SELECT NEW.team_account_id, a.city_id
    FROM public.addresses a
    WHERE a.id = NEW.address_id
    ON CONFLICT (team_account_id, city_id) DO NOTHING;
    
    INSERT INTO public.team_neighborhoods (team_account_id, neighborhood_id)
    SELECT NEW.team_account_id, a.neighborhood_id
    FROM public.addresses a
    WHERE a.id = NEW.address_id
    ON CONFLICT (team_account_id, neighborhood_id) DO NOTHING;
    
    INSERT INTO public.team_streets (team_account_id, street_id)
    SELECT NEW.team_account_id, a.street_id
    FROM public.addresses a
    WHERE a.id = NEW.address_id
    ON CONFLICT (team_account_id, street_id) DO NOTHING;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update the junction tables when properties are created/updated
CREATE TRIGGER update_team_location_associations_trigger
AFTER INSERT OR UPDATE ON public.properties
FOR EACH ROW
EXECUTE FUNCTION update_team_location_associations();

-- 2. Function to populate the junction tables with existing data
CREATE OR REPLACE FUNCTION populate_team_location_tables()
RETURNS void AS $$
BEGIN
  -- Populate team_cities
  INSERT INTO public.team_cities (team_account_id, city_id)
  SELECT DISTINCT p.team_account_id, a.city_id
  FROM public.properties p
  JOIN public.addresses a ON p.address_id = a.id
  ON CONFLICT (team_account_id, city_id) DO NOTHING;

  -- Populate team_neighborhoods
  INSERT INTO public.team_neighborhoods (team_account_id, neighborhood_id)
  SELECT DISTINCT p.team_account_id, a.neighborhood_id
  FROM public.properties p
  JOIN public.addresses a ON p.address_id = a.id
  ON CONFLICT (team_account_id, neighborhood_id) DO NOTHING;

  -- Populate team_streets
  INSERT INTO public.team_streets (team_account_id, street_id)
  SELECT DISTINCT p.team_account_id, a.street_id
  FROM public.properties p
  JOIN public.addresses a ON p.address_id = a.id
  ON CONFLICT (team_account_id, street_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql;

-- Execute the function to populate the tables with existing data
SELECT populate_team_location_tables();

-- Create helper functions for autocomplete queries

-- 1. Function to get neighborhoods for a team with optional name filter
CREATE OR REPLACE FUNCTION get_team_neighborhoods(
  team_id UUID,
  name_prefix TEXT DEFAULT NULL
)
RETURNS TABLE (
  id INTEGER,
  name TEXT,
  city_id INTEGER,
  city_name TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    n.id,
    n.name,
    c.id as city_id,
    c.name as city_name
  FROM public.neighborhoods n
  JOIN public.team_neighborhoods tn ON n.id = tn.neighborhood_id
  JOIN public.cities c ON n.city_id = c.id
  WHERE tn.team_account_id = team_id
  AND (name_prefix IS NULL OR n.name ILIKE name_prefix || '%')
  ORDER BY n.name ASC;
END;
$$ LANGUAGE plpgsql;

-- 2. Function to get cities for a team with optional name filter
CREATE OR REPLACE FUNCTION get_team_cities(
  team_id UUID,
  name_prefix TEXT DEFAULT NULL
)
RETURNS TABLE (
  id INTEGER,
  name TEXT,
  state public.brazilian_state
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    c.name,
    c.state
  FROM public.cities c
  JOIN public.team_cities tc ON c.id = tc.city_id
  WHERE tc.team_account_id = team_id
  AND (name_prefix IS NULL OR c.name ILIKE name_prefix || '%')
  ORDER BY c.name ASC;
END;
$$ LANGUAGE plpgsql;

-- 3. Function to get streets for a team with optional name filter
CREATE OR REPLACE FUNCTION get_team_streets(
  team_id UUID,
  name_prefix TEXT DEFAULT NULL
)
RETURNS TABLE (
  id INTEGER,
  name TEXT,
  city_id INTEGER,
  city_name TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id,
    s.name,
    c.id as city_id,
    c.name as city_name
  FROM public.streets s
  JOIN public.team_streets ts ON s.id = ts.street_id
  JOIN public.cities c ON s.city_id = c.id
  WHERE ts.team_account_id = team_id
  AND (name_prefix IS NULL OR s.name ILIKE name_prefix || '%')
  ORDER BY s.name ASC;
END;
$$ LANGUAGE plpgsql;
