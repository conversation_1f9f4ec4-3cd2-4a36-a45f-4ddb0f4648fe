-- Create normalized address tables

-- 1. Cities Table
CREATE TABLE public.cities (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  CONSTRAINT cities_name_unique UNIQUE (name)
);
-- Create case-insensitive index for city names
CREATE UNIQUE INDEX idx_cities_name_lower ON public.cities (LOWER(name));

-- 2. Neighborhoods Table
CREATE TABLE public.neighborhoods (
  id SERIAL PRIMARY KEY,
  city_id INTEGER NOT NULL REFERENCES public.cities(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  CONSTRAINT neighborhoods_city_name_unique UNIQUE (city_id, name)
);

-- 3. Streets Table
CREATE TABLE public.streets (
  id SERIAL PRIMARY KEY,
  city_id INTEGER NOT NULL REFERENCES public.cities(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  CONSTRAINT streets_city_name_unique UNIQUE (city_id, name)
);

-- 4. Addresses Table
CREATE TABLE public.addresses (
  id SERIAL PRIMARY KEY,
  city_id INTEGER NOT NULL REFERENCES public.cities(id) ON DELETE CASCADE,
  street_id INTEGER NOT NULL REFERENCES public.streets(id) ON DELETE CASCADE,
  neighborhood_id INTEGER NOT NULL REFERENCES public.neighborhoods(id) ON DELETE CASCADE,
  street_number TEXT NOT NULL,
  complement TEXT,
  coordinates GEOMETRY(POINT, 4326), -- Changed from GEOGRAPHY to GEOMETRY as per requirements
  postcode TEXT,
  state TEXT,
  country TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create spatial index on coordinates
CREATE INDEX idx_addresses_coordinates ON public.addresses USING GIST (coordinates);

-- Create indexes for neighborhoods
CREATE INDEX idx_neighborhoods_city_id ON public.neighborhoods (city_id);
CREATE INDEX idx_neighborhoods_name ON public.neighborhoods (name);

-- Create indexes for streets
CREATE INDEX idx_streets_city_id ON public.streets (city_id);
CREATE INDEX idx_streets_name ON public.streets (name);

-- Create trigger for updating timestamps
CREATE TRIGGER set_timestamp
  BEFORE UPDATE ON public.addresses
  FOR EACH ROW
  EXECUTE FUNCTION basejump.trigger_set_timestamps();

-- Enable Row Level Security for all tables
ALTER TABLE public.cities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.neighborhoods ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.streets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.addresses ENABLE ROW LEVEL SECURITY;

-- Grant access to authenticated users and anon for reading all tables
GRANT SELECT ON public.cities TO anon, authenticated;
GRANT SELECT ON public.neighborhoods TO anon, authenticated;
GRANT SELECT ON public.streets TO anon, authenticated;
GRANT SELECT ON public.addresses TO anon, authenticated;

GRANT INSERT, UPDATE ON public.cities TO authenticated;
GRANT INSERT, UPDATE ON public.neighborhoods TO authenticated;
GRANT INSERT, UPDATE ON public.streets TO authenticated;
GRANT INSERT, UPDATE ON public.addresses TO authenticated;

-- Allow all users to read these tables
CREATE POLICY "Allow public read access to cities"
  ON public.cities
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Allow public read access to neighborhoods"
  ON public.neighborhoods
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Allow public read access to streets"
  ON public.streets
  FOR SELECT
  TO public
  USING (true);

CREATE POLICY "Allow public read access to addresses"
  ON public.addresses
  FOR SELECT
  TO public
  USING (true);

-- Allow authenticated users to insert/update
CREATE POLICY "Allow authenticated to insert cities"
  ON public.cities
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Allow authenticated to update cities"
  ON public.cities
  FOR UPDATE
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated to insert neighborhoods"
  ON public.neighborhoods
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Allow authenticated to update neighborhoods"
  ON public.neighborhoods
  FOR UPDATE
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated to insert streets"
  ON public.streets
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Allow authenticated to update streets"
  ON public.streets
  FOR UPDATE
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated to insert addresses"
  ON public.addresses
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Allow authenticated to update addresses"
  ON public.addresses
  FOR UPDATE
  TO authenticated
  USING (true);

-- 5. Modify properties table to use the normalized addresses
-- First, add the address_id column
ALTER TABLE public.properties ADD COLUMN address_id INTEGER REFERENCES public.addresses(id);

-- 6. Migration function to populate the normalized tables and update properties
-- This function will be executed to transfer data from the old structure to the new one
CREATE OR REPLACE FUNCTION migrate_addresses() RETURNS void AS $$
DECLARE
  property_record RECORD;
  city_id INTEGER;
  neighborhood_id INTEGER;
  street_id INTEGER;
  address_id INTEGER;
BEGIN
  -- Process each property
  FOR property_record IN SELECT * FROM public.properties LOOP
    -- Insert or get city
    INSERT INTO public.cities (name)
    VALUES (property_record.address_city)
    ON CONFLICT (LOWER(name)) DO NOTHING;
    
    SELECT id INTO city_id FROM public.cities WHERE LOWER(name) = LOWER(property_record.address_city);
    
    -- Insert or get neighborhood
    INSERT INTO public.neighborhoods (city_id, name)
    VALUES (city_id, property_record.address_neighborhood)
    ON CONFLICT (city_id, name) DO NOTHING;
    
    SELECT id INTO neighborhood_id FROM public.neighborhoods 
    WHERE city_id = city_id AND name = property_record.address_neighborhood;
    
    -- Insert or get street
    INSERT INTO public.streets (city_id, name)
    VALUES (city_id, property_record.address_street)
    ON CONFLICT (city_id, name) DO NOTHING;
    
    SELECT id INTO street_id FROM public.streets 
    WHERE city_id = city_id AND name = property_record.address_street;
    
    -- Insert address
    INSERT INTO public.addresses (
      city_id, 
      street_id, 
      neighborhood_id, 
      street_number, 
      complement, 
      coordinates,
      postcode,
      state,
      country
    )
    VALUES (
      city_id,
      street_id,
      neighborhood_id,
      property_record.address_number,
      property_record.address_complement,
      property_record.coordinates::GEOMETRY(POINT, 4326),
      property_record.address_postcode,
      property_record.address_state,
      property_record.address_country
    )
    RETURNING id INTO address_id;
    
    -- Update property with new address_id
    UPDATE public.properties
    SET address_id = address_id
    WHERE id = property_record.id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Execute the migration function
SELECT migrate_addresses();

-- Drop the migration function as it's no longer needed
DROP FUNCTION migrate_addresses();

-- Remove old address columns after migration
ALTER TABLE public.properties
  DROP COLUMN address_street,
  DROP COLUMN address_number,
  DROP COLUMN address_complement,
  DROP COLUMN address_neighborhood,
  DROP COLUMN address_city,
  DROP COLUMN address_state,
  DROP COLUMN address_country,
  DROP COLUMN address_postcode,
  DROP COLUMN coordinates;

-- Make address_id NOT NULL after migration
ALTER TABLE public.properties ALTER COLUMN address_id SET NOT NULL;

-- Create index on address_id
CREATE INDEX idx_properties_address_id ON public.properties (address_id);

COMMENT ON TABLE public.cities IS 'Stores city information for normalized addresses';
COMMENT ON TABLE public.neighborhoods IS 'Stores neighborhood information for normalized addresses';
COMMENT ON TABLE public.streets IS 'Stores street information for normalized addresses';
COMMENT ON TABLE public.addresses IS 'Stores normalized address information';
COMMENT ON COLUMN public.properties.address_id IS 'Reference to the normalized address';
