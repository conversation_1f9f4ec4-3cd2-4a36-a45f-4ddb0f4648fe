set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_team_billing_status(team_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    result      jsonb;
    role_result jsonb;
BEGIN
    -- Log account role retrieval
    RAISE NOTICE 'Fetching role for team_id: %', team_id;
    SELECT public.current_user_team_role(team_id) 
    INTO role_result;
    RAISE NOTICE 'Role Result: %', role_result;

    -- Log main result query details
    RAISE NOTICE 'Fetching billing details for team_id: %', team_id;

    SELECT jsonb_build_object(
        'status', subscription.status,
        'trial_start', subscription.trial_start,
        'trial_end', subscription.trial_end,
        'current_period_start', subscription.current_period_start,
        'current_period_end', subscription.current_period_end,
        'cancel_at_period_end', subscription.cancel_at_period_end,
        'cancel_at', subscription.cancel_at,
        'canceled_at', subscription.canceled_at,
        'plan_name', subscription.plan_name,
        'quantity', subscription.quantity,
        'provider', config.billing_provider,
        'subscription_id', subscription.id,
        'customer_id', customer.id,
        'team_id', team_id,
        'billing_email',
        COALESCE(customer.email, auth_user.email) -- if we don't have a customer email, use the user's email as a fallback
    )
    INTO result
    FROM basejump.team_accounts team
    JOIN auth.users auth_user ON auth_user.id = team.primary_owner_user_id
    LEFT JOIN basejump.billing_subscriptions subscription ON subscription.team_account_id = team.id
    LEFT JOIN basejump.billing_customers customer ON customer.account_id = COALESCE(subscription.team_account_id, team.id)
    JOIN basejump.config config ON TRUE
    WHERE team.id = team_id
    ORDER BY subscription.created DESC
    LIMIT 1;

    -- Log final result
    RAISE NOTICE 'Result: %', result;

    RETURN result || role_result;
END;
$function$;

grant execute on function public.get_team_billing_status(uuid) to authenticated;

-- Fix service_role_upsert_customer_subscription function to handle email field correctly
CREATE OR REPLACE FUNCTION public.service_role_upsert_customer_subscription(account_id uuid,
                                                                          customer jsonb default null,
                                                                          subscription jsonb default null)
    RETURNS void AS
$$
BEGIN
    -- if the customer is not null, upsert the data into billing_customers, only upsert fields that are present in the jsonb object
    if customer is not null then
        insert into basejump.billing_customers (id, account_id, email, provider)
        values (customer ->> 'id', 
                service_role_upsert_customer_subscription.account_id,
                COALESCE(customer ->> 'billing_email', customer ->> 'email'),
                customer ->> 'provider')
        on conflict (id) do update
            set email = COALESCE(EXCLUDED.email, basejump.billing_customers.email),
                provider = COALESCE(EXCLUDED.provider, basejump.billing_customers.provider);
    end if;

    -- if the subscription is not null, upsert the data into billing_subscriptions
    if subscription is not null then
        insert into basejump.billing_subscriptions (id, team_account_id, billing_customer_id, status, metadata, price_id,
                                                  quantity, cancel_at_period_end, created, current_period_start,
                                                  current_period_end, ended_at, cancel_at, canceled_at, trial_start,
                                                  trial_end, provider, plan_name)
        values (subscription ->> 'id',
                service_role_upsert_customer_subscription.account_id,
                subscription ->> 'customer_id',
                (subscription ->> 'status')::basejump.subscription_status,
                subscription -> 'metadata',
                subscription ->> 'price_id',
                (subscription ->> 'quantity')::integer,
                (subscription ->> 'cancel_at_period_end')::boolean,
                (subscription ->> 'created')::timestamp with time zone,
                (subscription ->> 'current_period_start')::timestamp with time zone,
                (subscription ->> 'current_period_end')::timestamp with time zone,
                (subscription ->> 'ended_at')::timestamp with time zone,
                (subscription ->> 'cancel_at')::timestamp with time zone,
                (subscription ->> 'canceled_at')::timestamp with time zone,
                (subscription ->> 'trial_start')::timestamp with time zone,
                (subscription ->> 'trial_end')::timestamp with time zone,
                subscription ->> 'provider',
                subscription ->> 'plan_name')
        on conflict (id) do update
            set status                = EXCLUDED.status,
                metadata             = EXCLUDED.metadata,
                price_id            = EXCLUDED.price_id,
                quantity            = EXCLUDED.quantity,
                cancel_at_period_end = EXCLUDED.cancel_at_period_end,
                current_period_start = EXCLUDED.current_period_start,
                current_period_end   = EXCLUDED.current_period_end,
                ended_at            = EXCLUDED.ended_at,
                cancel_at           = EXCLUDED.cancel_at,
                canceled_at         = EXCLUDED.canceled_at,
                trial_start         = EXCLUDED.trial_start,
                trial_end          = EXCLUDED.trial_end,
                provider           = EXCLUDED.provider,
                plan_name         = EXCLUDED.plan_name;
    end if;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions to service role
GRANT EXECUTE ON FUNCTION public.service_role_upsert_customer_subscription(uuid, jsonb, jsonb) TO service_role;
