-- Add cloudflare_hostname_id column to custom_domains table

-- Add the column as NOT NULL
ALTER TABLE public.custom_domains
ADD COLUMN cloudflare_hostname_id TEXT NOT NULL;

-- Add comment explaining the column
COMMENT ON COLUMN public.custom_domains.cloudflare_hostname_id IS
'Cloudflare hostname ID for the custom domain, used to identify the hostname in Cloudflare API.';

-- Update RLS policy to only allow team owners to manage custom domains
DROP POLICY IF EXISTS "Team members can manage their custom domains" ON public.custom_domains;

CREATE POLICY "Team owners can manage their custom domains"
ON public.custom_domains
FOR ALL
TO authenticated
USING (
  basejump.has_role_on_team(team_account_id, 'owner') = true
);
