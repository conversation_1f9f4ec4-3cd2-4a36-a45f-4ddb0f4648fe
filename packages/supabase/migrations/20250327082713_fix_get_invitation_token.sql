-- Drop the existing function so we can replace it
DROP FUNCTION IF EXISTS public.get_invitation_token(uuid);

-- Create an improved version of the function
CREATE OR REPLACE FUNCTION public.get_invitation_token(invitation_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, basejump
AS $$
DECLARE
    invite_record basejump.invitations;
    team_id uuid;
    is_team_owner boolean;
BEGIN
    -- Find the invitation by ID
    SELECT * INTO invite_record
    FROM basejump.invitations
    WHERE id = invitation_id;
    
    -- Check if invitation exists
    IF invite_record IS NULL THEN
        RETURN json_build_object('error', 'Invitation not found');
    END IF;
    
    -- Get the team ID from the invitation
    team_id := invite_record.account_id;
    
    -- Check if user is team owner using the team_role function (which should already exist)
    SELECT (current_user_team_role(invite_record.account_id)::json->>'account_role') = 'owner' INTO is_team_owner;

    -- Debug information
    RAISE LOG 'Checking token: id=%, team_id=%, token=%, is_owner=%', 
               invitation_id, team_id, invite_record.token, is_team_owner;
    
    -- Return a proper error if user is not a team owner
    IF NOT is_team_owner THEN
        RETURN json_build_object('error', 'Only team owners can access invitation tokens');
    END IF;
    
    -- Check if invitation is expired (for 24-hour invitations)
    IF invite_record.invitation_type = '24_hour' AND invite_record.created_at < NOW() - INTERVAL '24 hours' THEN
        RETURN json_build_object('error', 'Invitation has expired');
    END IF;
    
    -- Return the token
    RETURN json_build_object(
        'token', invite_record.token,
        'account_role', invite_record.account_role,
        'invitation_type', invite_record.invitation_type,
        'created_at', invite_record.created_at
    );
END;
$$;

-- Grant access to the function
GRANT EXECUTE ON FUNCTION public.get_invitation_token(uuid) TO authenticated;