-- Create properties_media table
CREATE TABLE public.properties_media (
  id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
  property_id UUID REFERENCES public.properties(id) ON DELETE CASCADE,
  url TEXT NOT NULL,
  media_type TEXT NOT NULL CHECK (media_type IN ('image', 'video')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add comment to the table
COMMENT ON TABLE public.properties_media IS 'Stores property media (photos and videos) URLs';

-- Create index for property_id lookups
CREATE INDEX idx_properties_media_property_id ON public.properties_media (property_id);

-- Enable Row Level Security
ALTER TABLE public.properties_media ENABLE ROW LEVEL SECURITY;

-- Create the trigger for updating timestamps
CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON public.properties_media
    FOR EACH ROW
    EXECUTE FUNCTION basejump.trigger_set_timestamps();

-- Grant access to authenticated users and anon for reading
GRANT SELECT ON public.properties_media TO anon;
GRANT SELECT ON public.properties_media TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.properties_media TO authenticated;

-- RLS Policies
-- Allow public read access to all property media
CREATE POLICY "Allow public read access to property media"
    ON public.properties_media
    FOR SELECT
    TO public
    USING (true);

-- Allow team members to manage media for their properties
CREATE POLICY "Team members can manage property media"
    ON public.properties_media
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1
            FROM public.properties p
            WHERE p.id = properties_media.property_id
            AND basejump.has_role_on_team(p.team_account_id) = true
        )
    );