-- Create function to automatically create property_amenities and building_amenities records
-- when a new property is created
CREATE OR REPLACE FUNCTION public.set_amenities_relations_on_property_created()
    RETURNS TRIGGER
    SECURITY DEFINER
    SET search_path = public
AS $$
DECLARE
    property_amenities_record_id UUID;
    building_amenities_record_id UUID;
BEGIN
    -- Create a new property_amenities record
    INSERT INTO public.property_amenities (property_id)
    VALUES (NEW.id)
    RETURNING id INTO property_amenities_record_id;
    
    -- Create a new building_amenities record
    INSERT INTO public.building_amenities (property_id)
    VALUES (NEW.id)
    RETURNING id INTO building_amenities_record_id;
    
    -- Update the property record with the new amenities IDs
    UPDATE public.properties
    SET 
        property_amenities_id = property_amenities_record_id,
        building_amenities_id = building_amenities_record_id
    WHERE id = NEW.id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Grant execute to the trigger function
GRANT EXECUTE ON FUNCTION public.set_amenities_relations_on_property_created() TO postgres;

-- Create the trigger
DROP TRIGGER IF EXISTS on_property_created ON public.properties;
CREATE TRIGGER on_property_created
    AFTER INSERT
    ON public.properties
    FOR EACH ROW
    EXECUTE FUNCTION public.set_amenities_relations_on_property_created();
