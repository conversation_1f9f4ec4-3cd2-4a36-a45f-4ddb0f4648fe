-- Create public_team_profiles table
CREATE TABLE IF NOT EXISTS public.public_team_profiles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  team_account_id uuid NOT NULL REFERENCES basejump.team_accounts(id) ON DELETE CASCADE,
  name text,
  address text,
  phone_number text,
  whatsapp_number text,
  instagram_url text,
  facebook_url text,
  youtube_url text,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(team_account_id)
);

-- Add trigger for updated_at timestamp
CREATE OR REPLACE FUNCTION public.set_current_timestamp_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_public_team_profiles_updated_at
BEFORE UPDATE ON public.public_team_profiles
FOR EACH ROW
EXECUTE FUNCTION public.set_current_timestamp_updated_at();

-- Add trigger to create a public team profile when a team account is created
CREATE OR REPLACE FUNCTION public.create_public_team_profile_on_team_creation()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.public_team_profiles (team_account_id, name)
  VALUES (NEW.id, NEW.name);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER create_public_team_profile_on_team_creation
AFTER INSERT ON basejump.team_accounts
FOR EACH ROW
EXECUTE FUNCTION public.create_public_team_profile_on_team_creation();

-- Enable RLS
ALTER TABLE public.public_team_profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
-- Allow public read access
CREATE POLICY "Public profiles are viewable by everyone"
ON public.public_team_profiles
FOR SELECT USING (true);

-- Allow team members to update their team's profile
CREATE POLICY "Team members can update their team's profile"
ON public.public_team_profiles
FOR UPDATE
USING (basejump.has_role_on_team(team_account_id) = true)
WITH CHECK (basejump.has_role_on_team(team_account_id) = true);

-- Allow team members to insert their team's profile (fallback if trigger fails)
CREATE POLICY "Team members can insert their team's profile"
ON public.public_team_profiles
FOR INSERT
WITH CHECK (basejump.has_role_on_team(team_account_id) = true);

-- Grant permissions
GRANT SELECT ON public.public_team_profiles TO anon, authenticated;
GRANT INSERT, UPDATE ON public.public_team_profiles TO authenticated;
