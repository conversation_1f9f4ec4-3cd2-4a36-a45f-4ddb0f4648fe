-- Update functions to use team_role instead of account_role

-- Update get_invitation_token function
CREATE OR REPLACE FUNCTION public.get_invitation_token(invitation_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public', 'basejump'
AS $$
DECLARE
    invite_record basejump.invitations;
    team_id uuid;
    is_team_owner boolean;
BEGIN
    -- Find the invitation by ID
    SELECT * INTO invite_record
    FROM basejump.invitations
    WHERE id = invitation_id;
    
    -- Check if invitation exists
    IF invite_record IS NULL THEN
        RETURN json_build_object('error', 'Invitation not found');
    END IF;
    
    -- Get the team ID from the invitation
    team_id := invite_record.account_id;
    
    -- Check if user is team owner using the team_role function (which should already exist)
    -- Updated to use team_role instead of account_role
    SELECT (current_user_team_role(invite_record.account_id)::json->>'team_role') = 'owner' INTO is_team_owner;

    -- Debug information
    RAISE LOG 'Checking token: id=%, team_id=%, token=%, is_owner=%', 
               invitation_id, team_id, invite_record.token, is_team_owner;
    
    -- Return a proper error if user is not a team owner
    IF NOT is_team_owner THEN
        RETURN json_build_object('error', 'Only team owners can access invitation tokens');
    END IF;
    
    -- Check if invitation is expired (for 24-hour invitations)
    IF invite_record.invitation_type = '24_hour' AND invite_record.created_at < NOW() - INTERVAL '24 hours' THEN
        RETURN json_build_object('error', 'Invitation has expired');
    END IF;
    
    -- Return the token
    RETURN json_build_object(
        'token', invite_record.token,
        'account_role', invite_record.account_role,
        'invitation_type', invite_record.invitation_type,
        'created_at', invite_record.created_at
    );
END;
$$;

-- Update get_team_invitations function
CREATE OR REPLACE FUNCTION public.get_team_invitations(team_id uuid, results_limit integer DEFAULT 25, results_offset integer DEFAULT 0)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public', 'basejump'
AS $$
BEGIN
    -- only team owners can access this function
    -- Updated to use team_role instead of account_role
    if (select public.current_user_team_role(get_team_invitations.team_id) ->> 'team_role' <> 'owner') then
        raise exception 'Only team owners can access this function';
    end if;

    return (select json_agg(
                           json_build_object(
                                   'account_role', i.account_role,
                                   'created_at', i.created_at,
                                   'invitation_type', i.invitation_type,
                                   'invitation_id', i.id
                               )
                       )
            from basejump.invitations i
            where i.account_id = get_team_invitations.team_id
              and i.created_at > now() - interval '24 hours'
            limit coalesce(get_team_invitations.results_limit, 25) offset coalesce(get_team_invitations.results_offset, 0));
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_invitation_token(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_team_invitations(uuid, integer, integer) TO authenticated;
