-- Create properties_prices table
CREATE TABLE public.properties_prices (
    id UUID PRIMARY KEY DEFAULT extensions.uuid_generate_v4(),
    property_id UUID NOT NULL REFERENCES public.properties(id) ON DELETE CASCADE,
    -- Price details
    sale_price NUMERIC,
    rent_price NUMERIC,
    bnb_price NUMERIC,
    -- Tax details
    condominium_monthly_tax NUMERIC,
    iptu_monthly_tax NUMERIC,
    insurance_monthly_tax NUMERIC,
    other_monthly_tax NUMERIC,
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Ensure only one price record per property
    CONSTRAINT unique_property_price UNIQUE (property_id)
);

-- Add comment to the table
COMMENT ON TABLE public.properties_prices IS 'Stores property pricing and tax information';

-- Create index for property_id lookups
CREATE INDEX idx_properties_prices_property_id ON public.properties_prices (property_id);

-- Enable Row Level Security
ALTER TABLE public.properties_prices ENABLE ROW LEVEL SECURITY;

-- Create the trigger for updating timestamps
CREATE TRIGGER set_timestamp
    BEFORE UPDATE ON public.properties_prices
    FOR EACH ROW
    EXECUTE FUNCTION basejump.trigger_set_timestamps();

-- Grant access to authenticated users and anon for reading
GRANT SELECT ON public.properties_prices TO anon;
GRANT SELECT ON public.properties_prices TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.properties_prices TO authenticated;

-- RLS Policies
-- Allow public read access to all property prices
CREATE POLICY "Allow public read access to property prices"
    ON public.properties_prices
    FOR SELECT
    TO public
    USING (true);

-- Allow team members to manage prices for their properties
CREATE POLICY "Team members can manage property prices"
    ON public.properties_prices
    FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1
            FROM public.properties p
            WHERE p.id = properties_prices.property_id
            AND basejump.has_role_on_team(p.team_account_id) = true
        )
    );

-- Remove price and tax columns from properties table
ALTER TABLE public.properties
    DROP COLUMN sale_price,
    DROP COLUMN rent_price,
    DROP COLUMN bnb_price,
    DROP COLUMN condominium_monthly_tax,
    DROP COLUMN iptu_monthly_tax,
    DROP COLUMN insurance_monthly_tax,
    DROP COLUMN other_monthly_tax;

-- Drop the old price index if it exists
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM pg_indexes WHERE schemaname = 'public' AND indexname = 'idx_properties_price') THEN
        DROP INDEX public.idx_properties_price;
    END IF;
END $$;
