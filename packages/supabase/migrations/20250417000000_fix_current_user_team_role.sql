-- Fix the current_user_team_role function by adding SECURITY DEFINER
-- This ensures the function runs with the permissions of the function owner
-- rather than the calling user, allowing proper access to the team_accounts table

CREATE OR REPLACE FUNCTION public.current_user_team_role(team_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    response jsonb;
BEGIN
    SELECT jsonb_build_object(
        'team_role', team_user.team_role,
        'is_primary_owner', team.primary_owner_user_id = auth.uid()
    )
    INTO response
    FROM basejump.team_users team_user
    JOIN basejump.team_accounts team ON team.id = team_user.team_id
    WHERE team_user.user_id = auth.uid()
    AND team_user.team_id = current_user_team_role.team_id;

    -- if the user is not a member of the team, throw an error
    if response ->> 'team_role' IS NULL then
        raise exception 'User is not a member of this team';
    end if;

    return response;
END
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.current_user_team_role(uuid) TO authenticated;
