{"name": "supabase", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/supabase", "projectType": "application", "targets": {"start": {"executor": "nx:run-commands", "options": {"cwd": "packages/supabase", "command": "npx supabase start"}}, "stop": {"executor": "nx:run-commands", "options": {"cwd": "packages/supabase", "command": "npx supabase stop"}}, "functions:serve": {"executor": "nx:run-commands", "options": {"cwd": "packages/supabase", "command": "npx supabase functions serve"}}}, "tags": []}