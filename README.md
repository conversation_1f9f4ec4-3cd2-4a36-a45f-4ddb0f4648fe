# Imoblr Platform

<img src="packages/app/assets/logos/imoblr-symbol.svg" width="100" alt="Imoblr Logo">

Imoblr is a modern real estate platform that provides tools for property management, listing, and client interactions. The platform is built as a cross-platform application that works on web, iOS, and Android.

## Architecture Documentation

For a comprehensive overview of the project architecture, please see the [ARCHITECTURE.md](./ARCHITECTURE.md) file. This document provides detailed information about the project structure, technologies used, and how different components interact with each other.

## Getting Started

### Prerequisites

- [Node.js](https://nodejs.org/) (v18 or later)
- [pnpm](https://pnpm.io/) (v8.0.0 or later)
- [Nx CLI](https://nx.dev/getting-started/nx-setup) (optional, but recommended)
- [Supabase CLI](https://supabase.com/docs/guides/cli) (for local development with Supabase)

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd Platform
```

2. Install dependencies:

```bash
pnpm install
```

3. Set up environment variables:

```bash
# For the app package
cp packages/app/.env.example packages/app/.env.local

# For Supabase functions
cp packages/supabase/functions/.env.example packages/supabase/functions/.env
```

4. Update the environment variables in the `.env` files with your own values.

### Setting up Supabase

1. Install the Supabase CLI if you haven't already:

```bash
npm install -g supabase
```

2. Start the local Supabase services:

```bash
cd packages/supabase
supabase start
```

3. The CLI will output the local URLs and credentials for your Supabase instance. Update your `.env.local` file with these values.

## Development

### Running the App

To start the app in development mode:

```bash
nx dev app
```

For staging environment:

```bash
nx dev:staging app
```

### Running the API

To start the API locally:

```bash
nx serve api
```

### Running on Mobile Devices

For iOS:

```bash
nx ios app
```

For Android:

```bash
nx android app
```

## Building for Production

### Building the App

```bash
nx build app
```

### Building the API

```bash
nx build api
```

## Deployment

### Deploying the API

```bash
nx deploy api
```

## Project Structure

The project is organized as an Nx monorepo with multiple packages:

- `packages/api`: Cloudflare Workers API
- `packages/app`: Expo/React Native application
- `packages/supabase`: Supabase configuration and migrations

## Useful Commands

```bash
# Lint the code
nx lint app

# Format the code
nx format app

# Run prebuild for native apps
nx prebuild app

# Sync TypeScript project references
npx nx sync
```
