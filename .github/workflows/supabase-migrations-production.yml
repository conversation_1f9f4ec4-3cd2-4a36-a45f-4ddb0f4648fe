name: Supa<PERSON> Migrations [PRODUCTION]

on:
  push:
    branches:
      - main
    paths:
      - "packages/supabase/migrations/**"

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_PRODUCTION_ACCESS_TOKEN }}
      SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_PRODUCTION_DB_PASSWORD }}
      SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PRODUCTION_PROJECT_ID }}
    steps:
      - uses: actions/checkout@v4
      - uses: supabase/setup-cli@v1
        with:
          version: latest
      - run: cd packages/supabase && npx supabase link --project-ref $SUPABASE_PROJECT_ID && npx supabase db push
