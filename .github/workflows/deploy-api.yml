name: Deploy API

on:
  push:
    branches:
      - staging
      - main
    paths:
      - 'packages/api/**'
      - '.github/workflows/deploy-api.yml'

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: |
          cd packages/api
          bun install

      - name: Set deployment name
        id: set-name
        run: |
          if [[ $GITHUB_REF == 'refs/heads/staging' ]]; then
            echo "WORKER_NAME=staging-api" >> $GITHUB_ENV
          else
            echo "WORKER_NAME=api" >> $GITHUB_ENV
          fi

      - name: Deploy to Cloudflare Workers
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_EDIT_WORKERS_API_TOKEN }}
        run: |
          cd packages/api
          bunx wrangler deploy --name $WORKER_NAME
