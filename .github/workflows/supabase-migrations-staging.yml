name: Supa<PERSON> Migrations [STAGING]

on:
  push:
    branches:
      - staging
    paths:
      - "packages/supabase/migrations/**"

jobs:
  deploy:
    runs-on: ubuntu-latest
    env:
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_STAGING_ACCESS_TOKEN }}
      SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_STAGING_DB_PASSWORD }}
      SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_STAGING_PROJECT_ID }}
    steps:
      - uses: actions/checkout@v4
      - uses: supabase/setup-cli@v1
        with:
          version: latest
      - run: cd packages/supabase && npx supabase link --project-ref $SUPABASE_PROJECT_ID && npx supabase db push
