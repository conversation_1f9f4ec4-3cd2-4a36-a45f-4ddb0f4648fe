# Imoblr Platform Architecture

This document provides a comprehensive overview of the Imoblr Platform architecture, explaining the project structure, technologies used, and how different components interact with each other.

## Project Overview

Imoblr is a real estate platform that provides tools for property management, listing, and client interactions. The platform is built as a modern, cross-platform application that works on web, iOS, and Android, with dedicated customer-facing websites.

## Tech Stack

The Imoblr Platform is built using the following technologies:

### Core Technologies

- **Nx Monorepo**: The project is structured as a monorepo using Nx for efficient code sharing and build orchestration
- **TypeScript**: Used throughout the codebase for type safety
- **Bun**: Package manager and JavaScript runtime
- **Supabase**: Backend-as-a-Service providing database, authentication, storage, and serverless functions
- **Cloudflare Workers**: Serverless functions for API endpoints and proxy services

### Frontend Technologies

- **Expo/React Native**: Cross-platform mobile application framework
- **Expo Router**: Navigation solution for Expo applications
- **NativeWind/Tailwind CSS**: Styling solution
- **React Query**: Data fetching and state management
- **React Hook Form**: Form handling
- **Zod**: Schema validation
- **Astro**: Static site generation for customer websites

### Backend Technologies

- **Hono**: Lightweight, fast web framework for Cloudflare Workers
- **PostgreSQL**: Database (via Supabase)
- **Supabase Edge Functions**: Serverless functions for backend logic
- **Stripe**: Payment processing integration

## Project Structure

The project is organized as an Nx monorepo with multiple packages:

```
Imoblr/
├── packages/
│   ├── api/                 # Cloudflare Workers API
│   ├── app/                 # Expo/React Native application
│   ├── customer-sites/      # Astro-based customer websites
│   ├── proxy/               # Cloudflare Worker for domain proxying
│   └── supabase/            # Supabase configuration and migrations
├── node_modules/
├── nx.json                  # Nx configuration
├── package.json             # Root package configuration
└── README.md                # Project documentation
```

### Packages

#### `packages/api`

The API package contains Cloudflare Workers that provide backend functionality:

- **Main API**: Consolidated API worker for Imoblr
- **Addresses Worker**: Handles address-related functionality
- **Media Worker**: Handles media-related functionality
- **Domains Worker**: Manages custom domain configuration
- **Team Profiles Worker**: Manages team profile information
- **Websites Worker**: Handles website-related functionality

The API is built using the Hono framework and communicates with Supabase for data storage and authentication.

#### `packages/app`

The app package contains the Expo/React Native application that provides the user interface for web, iOS, and Android platforms:

- **app/**: Expo Router app directory
- **assets/**: Static assets
- **components/**: UI components
- **constants/**: Constants and configuration
- **context/**: React context providers
- **hooks/**: Custom React hooks
- **theme/**: Styling themes
- **types/**: TypeScript type definitions
- **utils/**: Utility functions

#### `packages/customer-sites`

The customer-sites package contains the Astro-based website generation for property listings and team profiles:

- **src/**: Source code for the Astro site
- **public/**: Static assets
- **components/**: Reusable UI components
- **layouts/**: Page layouts
- **pages/**: Page templates

This package uses Astro with React components and is deployed to Cloudflare Pages.

#### `packages/proxy`

The proxy package contains a Cloudflare Worker that handles custom domain routing for customer websites:

- **index.js**: Main worker code for proxying requests
- **wrangler.toml**: Cloudflare Worker configuration

#### `packages/supabase`

The supabase package contains configuration, migrations, and edge functions for the Supabase backend:

- **migrations/**: Database schema migrations
- **functions/**: Edge functions for serverless backend logic
- **tests/**: Database tests
- **config.toml**: Supabase configuration

## Data Flow

1. **User Interface**: The Expo/React Native application provides the user interface across web, iOS, and Android platforms.
2. **API Requests**: The application makes requests to either:
   - Supabase directly for data operations and authentication
   - Cloudflare Workers API for specialized functionality
3. **Data Storage**: Data is stored in PostgreSQL databases managed by Supabase
4. **Authentication**: User authentication is handled by Supabase Auth
5. **File Storage**: Media files are stored in Supabase Storage
6. **Customer Websites**: Generated using Astro and served via Cloudflare Pages
7. **Domain Routing**: Custom domains are handled by the proxy worker

## Database Schema

The database uses PostgreSQL and includes the following key tables:

### Public Schema
- **properties**: Stores property information
- **properties_prices**: Stores pricing information for properties
- **properties_media**: Stores media files associated with properties
- **addresses**: Stores address information
- **cities**, **neighborhoods**, **streets**: Geographic hierarchy data
- **websites**: Stores information about tenant websites
- **custom_domains**: Stores custom domain configuration
- **website_themes**: Stores theme information for websites
- **building_amenities**, **property_amenities**: Stores amenity information
- **public_team_profiles**: Stores public team profile information

### Basejump Schema
- **team_accounts**: Stores team account information
- **team_users**: Stores team user relationships
- **user_accounts**: Stores user account information
- **invitations**: Stores team invitations
- **billing_customers**, **billing_subscriptions**: Stores billing information

Other important schemas:
- **storage**: Supabase storage tables
- **auth**: Supabase authentication tables

## Authentication and Authorization

Authentication is handled by Supabase Auth, which provides:
- Email/password authentication
- Social login options
- JWT-based session management

Authorization is implemented through:
- Row-level security (RLS) policies in the database
- JWT verification in API endpoints
- Role-based access control
- Team-based permissions using Basejump

## Customer Website System

The customer website system consists of several components:

1. **Website Generation**: Astro-based static site generation in the customer-sites package
2. **Domain Management**:
   - Custom domains stored in the database
   - DNS configuration via Cloudflare
   - Proxy worker for routing requests
3. **Themes**: Customizable themes for different website styles
4. **Content Management**: Property and team data from the Supabase database

## Deployment

The platform is deployed across multiple environments:

- **Development**: Local development environment
- **Staging**: Testing environment for pre-production validation
- **Production**: Live environment for end users

Deployment is managed through:
- GitHub Actions for CI/CD
- Cloudflare Workers for API deployment
- Cloudflare Pages for customer websites
- Expo for mobile app builds

## Environment Configuration

The application uses environment variables for configuration:

- **.env.local**: Local development environment
- **.env.staging**: Staging environment
- **.env.production**: Production environment

Key environment variables include:
- Supabase URL and API keys
- Cloudflare credentials
- Stripe API keys
- Google API keys

## Development Workflow

1. **Setup**: Install dependencies using Bun
2. **Local Development**: Run the application and API locally
3. **Testing**: Test changes in the local environment
4. **Deployment**: Deploy changes to staging and then production

## Conclusion

The Imoblr Platform is built as a modern, scalable application using a monorepo structure with Nx. It leverages Supabase for backend services, Cloudflare Workers for API functionality, Expo/React Native for cross-platform frontend development, and Astro for customer-facing websites. This architecture provides a flexible, maintainable foundation for the real estate platform that serves both property management professionals and their clients.
