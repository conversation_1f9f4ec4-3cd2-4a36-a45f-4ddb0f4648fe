{
  "compilerOptions": {
    "strict": true,
    "moduleResolution": "node",
    "baseUrl": ".", // Required for paths to work
    "paths": {
      "@platform/types": ["./types/index.ts"], // Alias for the shared types
      "@platform/types/*": ["./types/*"],
      "@platform/assets/*": ["packages/assets/*"]
    }
  },
  "include": ["custom.d.ts", "**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "nativewind-env.d.ts"],
  "exclude": ["node_modules", "supabase"]
}
